<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xyy.saas.inquiry.hospital.server.dal.mysql.doctor.InquiryDoctorMapper">

  <!--
      一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
      无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
      代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
      文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
   -->

  <resultMap id="InquiryDoctorRespVO" type="com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.InquiryDoctorRespVO">
    <id property="id" column="id" jdbcType="BIGINT"/>
    <result property="ext" column="ext" jdbcType="VARCHAR" typeHandler="com.xyy.saas.inquiry.annotation.JsonTypeHandler"/>
  </resultMap>

  <select id="selectDoctorPage" resultMap="InquiryDoctorRespVO">
    select a.*,COALESCE(b.certify_status, 0) AS certify_status,b.signature_status,b.signature_platform, b.authorize_free_sign_status,b.authorize_free_sign_ddl
    from saas_inquiry_doctor a left join
    saas_inquiry_signature_ca_auth b on a.user_id = b.user_id
    <where>
      a.disable = false and a.deleted = false
      <if test="reqVo.signaturePlatform != null">
        and b.signature_platform = #{reqVo.signaturePlatform}
      </if>
      <if test="reqVo.certifyStatus != null">
        and b.certify_status = #{reqVo.certifyStatus}
      </if>
      <include refid="baseCondition"/>
    </where>
    ORDER BY a.id DESC
  </select>

  <sql id="baseCondition">
    <if test="reqVo.pref != null">
      AND a.pref = #{reqVo.pref}
    </if>
    <if test="reqVo.doctorIds != null and reqVo.doctorIds.size() > 0">
      AND a.id IN
      <foreach item="id" index="index" collection="doctorIds"
        open="(" separator="," close=")">
        #{reqVo.id}
      </foreach>
    </if>
    <if test="reqVo.doctorPrefs != null and reqVo.doctorPrefs.size() > 0">
      AND a.pref IN
      <foreach item="pref" index="index" collection="reqVo.doctorPrefs"
        open="(" separator="," close=")">
        #{pref}
      </foreach>
    </if>
    <if test="reqVo.name != null and reqVo.name.trim() != ''">
      AND a.name LIKE CONCAT('%', #{reqVo.name}, '%')
    </if>
    <if test="reqVo.sex != null">
      AND a.sex = #{reqVo.sex}
    </if>
    <if test="reqVo.idCard != null">
      AND a.id_card = #{reqVo.idCard}
    </if>
    <if test="reqVo.mobile != null">
      AND a.mobile = #{reqVo.mobile}
    </if>
    <if test="reqVo.userId != null">
      AND a.user_id = #{reqVo.userId}
    </if>
    <if test="reqVo.auditStatus != null">
      AND a.audit_status = #{reqVo.auditStatus}
    </if>
    <if test="reqVo.onlineStatus != null">
      AND a.online_status = #{reqVo.onlineStatus}
    </if>
    <if test="reqVo.cooperation != null">
      AND a.cooperation = #{reqVo.cooperation}
    </if>
    <if test="reqVo.startInquiryTime != null and reqVo.endInquiryTime != null">
      AND a.start_inquiry_time BETWEEN #{reqVo.startInquiryTime} AND #{reqVo.endInquiryTime}
    </if>
    <if test="reqVo.envTag != null">
      AND a.env_tag = #{reqVo.envTag}
    </if>
    <if test="reqVo.jobType != null">
      AND job_type = #{reqVo.jobType}
    </if>
    <if test="reqVo.prescriptionPasswordStatus != null">
      AND prescription_password_status = #{reqVo.prescriptionPasswordStatus}
    </if>
    <if test="reqVo.fillingStatus4ShaanxiRegulatory != null">
      <choose>
        <when test="reqVo.fillingStatus4ShaanxiRegulatory == 0">
          AND (JSON_EXTRACT(a.ext, '$.fillingStatus4ShaanxiRegulatory') = 0 or JSON_EXTRACT(a.ext, '$.fillingStatus4ShaanxiRegulatory') is null)
        </when>
        <otherwise>
          AND JSON_EXTRACT(a.ext, '$.fillingStatus4ShaanxiRegulatory') = #{reqVo.fillingStatus4ShaanxiRegulatory}
        </otherwise>
      </choose>
    </if>
    <if test="reqVo.fillingStatus4CdRegulatory != null">
      <choose>
        <when test="reqVo.fillingStatus4CdRegulatory == 0">
          AND (JSON_EXTRACT(a.ext, '$.fillingStatus4CdRegulatory') = 0 or JSON_EXTRACT(a.ext, '$.fillingStatus4CdRegulatory') is null)
        </when>
        <otherwise>
          AND JSON_EXTRACT(a.ext, '$.fillingStatus4CdRegulatory') = #{reqVo.fillingStatus4CdRegulatory}
        </otherwise>
      </choose>
    </if>
  </sql>

</mapper>