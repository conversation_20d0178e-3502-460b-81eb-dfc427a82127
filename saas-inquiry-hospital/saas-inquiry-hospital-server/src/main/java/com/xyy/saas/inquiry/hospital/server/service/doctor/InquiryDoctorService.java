package com.xyy.saas.inquiry.hospital.server.service.doctor;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.enums.doctor.OnlineStatusEnum;
import com.xyy.saas.inquiry.hospital.api.doctor.dto.InquiryDoctorCardInfoDto;
import com.xyy.saas.inquiry.hospital.api.doctor.dto.transmission.DoctorExternalTransmissionRespDto;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.DoctorAuditedRecordSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.InquiryDoctorDetailRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.InquiryDoctorPageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.InquiryDoctorRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.InquiryDoctorSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.InquiryDoctorDO;
import jakarta.validation.Valid;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 医生信息 Service 接口
 *
 * <AUTHOR>
 */
public interface InquiryDoctorService {

    /**
     * 创建医生信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    InquiryDoctorDO createInquiryDoctor(@Valid InquiryDoctorSaveReqVO createReqVO);

    /**
     * 更新医生信息
     *
     * @param updateReqVO 更新信息
     */
    void updateInquiryDoctor(@Valid InquiryDoctorSaveReqVO updateReqVO);

    /**
     * 删除医生信息
     *
     * @param id 编号
     */
    void deleteInquiryDoctor(Long id);

    /**
     * 获得医生信息
     *
     * @param id 编号 空时获取当前用户的医生信息
     * @return 医生信息
     */
    InquiryDoctorDetailRespVO getInquiryDoctor(Long id);

    /**
     * 获得医生信息分页
     *
     * @param pageReqVO 分页查询
     * @return 医生信息分页
     */
    PageResult<InquiryDoctorRespVO> getInquiryDoctorPage(InquiryDoctorPageReqVO pageReqVO);

    /**
     * 查询医生列表
     *
     * @param reqVo
     * @return 医生信息不分页
     */
    List<InquiryDoctorDO> getInquiryDoctorList(InquiryDoctorPageReqVO reqVo);

    /**
     * 医生审核
     *
     * @param auditReqVO
     * @return
     */
    Integer auditInquiryDoctor(DoctorAuditedRecordSaveReqVO auditReqVO);


    /**
     * 根据医生id 获取医生基础信息
     *
     * @param doctorId
     * @return
     */
    InquiryDoctorDO getInquiryDoctorByDoctorId(Long doctorId);

    /**
     * 根据医生userId 获取医生基础信息
     *
     * @param userId
     * @return
     */
    InquiryDoctorDO getInquiryDoctorByUserId(Long userId);

    InquiryDoctorDO getDoctorByUserId(Long userId);

    /**
     * 根据医生编码查询医生信息
     *
     * @param doctorPref
     * @return
     */
    InquiryDoctorDO getRequireInquiryDoctorByDoctorPref(String doctorPref);

    /**
     * 根据医生编码查询医生信息 没有返回 null
     *
     * @param doctorPref
     * @return
     */
    InquiryDoctorDO getInquiryDoctorByDoctorPref(String doctorPref);

    /**
     * 修改医生在线状态
     *
     * @param id               医生id
     * @param onlineStatusEnum 在线状态
     * @param startTime        出诊时间
     * @param endTime          停诊时间
     */
    void updateDoctorOnlineStatus(Long id, OnlineStatusEnum onlineStatusEnum, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 超时取消医生接诊
     *
     * @param inquiryPref 问诊编码
     */
    void inquiryTimeOutCancelForDoctorReception(String inquiryPref);

    /**
     * 根据医生编码获取医生卡片信息
     *
     * @param doctorPref
     * @return
     */
    InquiryDoctorCardInfoDto getDoctorCardInfoByDoctorPref(String doctorPref);

    /**
     * 根据医生手机号码获取
     *
     * @param mobile
     * @return
     */
    InquiryDoctorDetailRespVO getInquiryDoctorByMobile(String mobile);

    /**
     * 医生账号注销前检查
     *
     * @return
     */
    Boolean logOffCheck();


    /**
     * 关闭当前医生自动抢单功能
     * @param doctor
     */
    void closeDoctorAutoGrab(InquiryDoctorDO doctor);


    /**
     * 陕西监管备案
     * @param doctorId
     * @return
     */
    DoctorExternalTransmissionRespDto fillShaanxiRegulatory(Long doctorId);

    /**
     * 医师诊疗业务备案
     *
     * @param doctorIds 医生ID集合
     * @return 备案结果
     */
    List<DoctorExternalTransmissionRespDto> physicianDiagnosisTreatmentRecord(List<Long> doctorIds);

    /**
     * 医疗人员信息上报
     *
     * @param doctorIds 医生ID集合
     * @return 上报结果
     */
    List<DoctorExternalTransmissionRespDto> medicalPersonnelInformationReport(List<Long> doctorIds);

    /**
     * 成都监管备案
     * @param id
     * @return
     */
    Boolean fillCdRegulatory( Long id);
}