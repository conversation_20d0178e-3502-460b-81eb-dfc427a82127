package com.xyy.saas.inquiry.hospital.server.convert.prescription;

import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionDetailRespDTO;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionRespDTO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.vo.InquiryPrescriptionDetailRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.vo.InquiryPrescriptionDetailSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.vo.InquiryPrescriptionPricingVO.PrescriptionDetailPricingVO;
import com.xyy.saas.inquiry.hospital.server.controller.app.prescription.vo.PrescriptionIssuesVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.prescription.InquiryPrescriptionDetailDO;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDetailDto;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import com.xyy.saas.inquiry.pojo.inquiry.InquiryProductDetailDto;
import com.xyy.saas.inquiry.pojo.inquiry.InquiryProductDto;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * @ClassName：InquiryPrescription
 * @Author: xucao
 * @Date: 2024/10/28 17:57
 * @Description: 问诊处方转化器
 */
@Mapper
public interface InquiryPrescriptionDetailConvert {

    InquiryPrescriptionDetailConvert INSTANCE = Mappers.getMapper(InquiryPrescriptionDetailConvert.class);

    List<InquiryPrescriptionDetailDO> convertDO(List<InquiryPrescriptionDetailSaveReqVO> createReqVO);

    List<InquiryPrescriptionDetailRespDTO> convertDTOs(List<InquiryPrescriptionDetailDO> detailDOS);

    /**
     * 根据问诊记录+医生开方数据创建处方明细
     *
     * @param prescriptionPref     处方编号
     * @param inquiryRecordDto     问诊记录
     * @param inquiryRecordDetail  问诊记录详情
     * @param prescriptionIssuesVO 医生开方记录
     * @return 处方明细列表
     */
    default List<InquiryPrescriptionDetailSaveReqVO> convertSaveVOs(InquiryPrescriptionRespDTO prescriptionRespDTO, InquiryRecordDto inquiryRecordDto, InquiryRecordDetailDto inquiryRecordDetail, PrescriptionIssuesVO prescriptionIssuesVO) {
        InquiryProductDto inquiryProductDto = inquiryRecordDto.isAutoInquiry() ? inquiryRecordDetail.getPreDrugDetail() : prescriptionIssuesVO.getInquiryProductDto();
        return inquiryProductDto.getInquiryProductInfos().stream().map(p -> {
            InquiryPrescriptionDetailSaveReqVO detailSaveReqVO = convertProduct2Detail(p);
            return detailSaveReqVO.setMedicineType(inquiryRecordDto.getMedicineType())
                .setPrescriptionPref(prescriptionRespDTO.getPref())
                .setInquiryPref(inquiryRecordDto.getPref())
                .setTenantId(inquiryRecordDto.getTenantId())
                .setTenantName(inquiryRecordDto.getTenantName());
        }).toList();
    }

    @Mapping(target = "productPref", source = "pref")
    @Mapping(target = "packageUnit", source = "unitName")
    @Mapping(target = "productName", expression = "java(org.apache.commons.lang3.StringUtils.defaultIfBlank(p.getProductName(),p.getCommonName()))")
    @Mapping(target = "singleDose", expression = "java(org.apache.commons.lang3.StringUtils.equals(\"适量\",p.getSingleUnit()) ? \"\" : p.getSingleDose())")
    InquiryPrescriptionDetailSaveReqVO convertProduct2Detail(InquiryProductDetailDto p);


    default InquiryPrescriptionDetailDO convertPricing(InquiryPrescriptionDetailDO d, PrescriptionDetailPricingVO detailPricingVO) {
        BigDecimal actualAmount = d.getQuantity().multiply(detailPricingVO.getProductPrice()).setScale(2, RoundingMode.HALF_UP);
        return InquiryPrescriptionDetailDO.builder().id(d.getId()).productPrice(detailPricingVO.getProductPrice()).actualAmount(actualAmount).build();
    }

    List<InquiryPrescriptionDetailRespVO> convertVos(List<InquiryPrescriptionDetailDO> prescriptionDetailDOS);

    List<InquiryPrescriptionDetailRespDTO> convertDtos(List<InquiryPrescriptionDetailDO> inquiryPrescriptionDetailDOS);
}
