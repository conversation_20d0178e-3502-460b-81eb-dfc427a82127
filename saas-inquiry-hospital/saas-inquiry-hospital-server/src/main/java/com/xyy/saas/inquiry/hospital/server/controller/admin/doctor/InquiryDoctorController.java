package com.xyy.saas.inquiry.hospital.server.controller.admin.doctor;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.error;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUser;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUserNickname;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import cn.iocoder.yudao.framework.security.core.LoginUser;
import com.xyy.saas.inquiry.enums.transmitter.NodeTypeEnum;
import com.xyy.saas.inquiry.hospital.api.doctor.dto.transmission.DoctorExternalTransmissionRespDto;
import com.xyy.saas.inquiry.hospital.enums.ErrorCodeConstants;
import com.xyy.saas.inquiry.hospital.server.config.transimission.InquiryDoctorFillShaanxiRegulatoryProperties;
import com.xyy.saas.inquiry.hospital.server.config.transimission.InquiryDoctorFillShaanxiRegulatoryProperties.FillApiProperties;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.DoctorAuditedRecordSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.InquiryDoctorDetailRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.InquiryDoctorPageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.InquiryDoctorRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.InquiryDoctorSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.InquiryDoctorDO;
import com.xyy.saas.inquiry.hospital.server.job.AutoInquiryDoctorJobService;
import com.xyy.saas.inquiry.hospital.server.service.doctor.InquiryDoctorService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.SupervisionPhysicianDiagnosisTreatmentRecordReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.SupervisionMedicalPersonnelInformationReportReqVO;


@Tag(name = "管理后台 - 医生信息")
@RestController
@RequestMapping("/hospital/inquiry-doctor")
@Validated
public class InquiryDoctorController {

    @Resource
    private InquiryDoctorService inquiryDoctorService;

    @Resource
    private AutoInquiryDoctorJobService autoInquiryDoctorJobService;


    @PostMapping("/create")
    @Operation(summary = "创建医生信息")
    @PreAuthorize("@ss.hasPermission('hospital:inquiry-doctor:create')")
    public CommonResult<InquiryDoctorDO> createInquiryDoctor(@Valid @RequestBody InquiryDoctorSaveReqVO createReqVO) {
        return success(inquiryDoctorService.createInquiryDoctor(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新医生信息")
    @PreAuthorize("@ss.hasPermission('hospital:inquiry-doctor:update')")
    public CommonResult<Boolean> updateInquiryDoctor(@Valid @RequestBody InquiryDoctorSaveReqVO updateReqVO) {
        inquiryDoctorService.updateInquiryDoctor(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除医生信息")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('hospital:inquiry-doctor:delete')")
    public CommonResult<Boolean> deleteInquiryDoctor(@RequestParam("id") Long id) {
        inquiryDoctorService.deleteInquiryDoctor(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得医生信息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('hospital:inquiry-doctor:query')")
    public CommonResult<InquiryDoctorDetailRespVO> getInquiryDoctor(@RequestParam("id") Long id) {
        return success(inquiryDoctorService.getInquiryDoctor(id));
    }

    @GetMapping("/get-by-mobile")
    @Operation(summary = "获得医生信息根据手机号")
    @Parameter(name = "mobile", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('hospital:inquiry-doctor:query')")
    public CommonResult<InquiryDoctorDetailRespVO> getInquiryDoctorByMobile(@RequestParam("mobile") String mobile) {
        return success(inquiryDoctorService.getInquiryDoctorByMobile(mobile));
    }


    @PostMapping("/audit")
    @Operation(summary = "审核医生信息")
    @PreAuthorize("@ss.hasPermission('hospital:inquiry-doctor:update')")
    public CommonResult<Integer> auditInquiryDoctor(@Valid @RequestBody DoctorAuditedRecordSaveReqVO auditReqVO) {
        LoginUser loginUser = getLoginUser();
        if (ObjectUtils.isEmpty(loginUser)) {
            return error(ErrorCodeConstants.SYSTEM_LOGIN_USER_NOT_EXISTS);
        }
        auditReqVO.setAuditorId(loginUser.getId());
        auditReqVO.setAuditorName(getLoginUserNickname());
        return success(inquiryDoctorService.auditInquiryDoctor(auditReqVO));
    }

    @GetMapping("/page")
    @Operation(summary = "获得医生信息分页")
    @PreAuthorize("@ss.hasPermission('hospital:inquiry-doctor:query')")
    public CommonResult<PageResult<InquiryDoctorRespVO>> getInquiryDoctorPage(@Valid InquiryDoctorPageReqVO pageReqVO) {
        PageResult<InquiryDoctorRespVO> pageResult = inquiryDoctorService.getInquiryDoctorPage(pageReqVO);
        return success(pageResult);
    }

    @GetMapping("/list")
    @Operation(summary = "获得医生信息分页")
    @PreAuthorize("@ss.hasPermission('hospital:inquiry-doctor:query')")
    public CommonResult<List<InquiryDoctorRespVO>> getInquiryDoctorList(@Valid InquiryDoctorPageReqVO pageReqVO) {
        List<InquiryDoctorDO> pageResult = inquiryDoctorService.getInquiryDoctorList(pageReqVO);
        return success(BeanUtils.toBean(pageResult, InquiryDoctorRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出医生信息 Excel")
    @PreAuthorize("@ss.hasPermission('hospital:inquiry-doctor:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportInquiryDoctorExcel(@Valid InquiryDoctorPageReqVO pageReqVO,
        HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<InquiryDoctorRespVO> list = inquiryDoctorService.getInquiryDoctorPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "医生信息.xls", "数据", InquiryDoctorDetailRespVO.class,
            BeanUtils.toBean(list, InquiryDoctorDetailRespVO.class));
    }


    @PostMapping("/init-auto-inquiry-doctor-timer-wheel")
    @Operation(summary = "初始化自动开方医生时间轮")
    @PreAuthorize("@ss.hasPermission('hospital:inquiry-doctor:initTimerWheel')")
    public void initAutoInquiryDoctorTimerWheel() {
        autoInquiryDoctorJobService.initAutoInquiryDoctorTimerWheel();
        // autoInquiryDoctorJobService.jobHandAutoInquiryDoctor();
    }

    // =================================================================================================================
    // ======================================== 陕西西电监管：医师诊疗业务备案、医疗人员信息上报 =======================================================
    // =================================================================================================================

    @PostMapping("/fill-shaanxi-regulatory")
    @Operation(summary = "陕西监管备案")
    @Parameter(name = "id", description = "医生ID", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('hospital:inquiry-doctor:supervision')")
    public CommonResult<DoctorExternalTransmissionRespDto> fillShaanxiRegulatory(@Valid @RequestParam("id") Long id) {
        return CommonResult.success(inquiryDoctorService.fillShaanxiRegulatory(id));
    }


    @PostMapping("/fill-cd-regulatory")
    @Operation(summary = "成都监管备案")
    @Parameter(name = "id", description = "医生ID", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('hospital:inquiry-doctor:cdsupervision')")
    public CommonResult<Boolean> fillCddRegulatory(@Valid @RequestParam("id") Long id) {
        return CommonResult.success(inquiryDoctorService.fillCdRegulatory(id));
    }

    @PostMapping("/physician-diagnosis-treatment-record")
    @Operation(summary = "医师诊疗业务备案")
    @PreAuthorize("@ss.hasPermission('hospital:inquiry-doctor:supervision')")
    public CommonResult<List<DoctorExternalTransmissionRespDto>> physicianDiagnosisTreatmentRecord(@Valid @RequestBody SupervisionPhysicianDiagnosisTreatmentRecordReqVO reqVO) {
        return CommonResult.success(inquiryDoctorService.physicianDiagnosisTreatmentRecord(reqVO.getDoctorIds()));
    }

    @PostMapping("/medical-personnel-information-report")
    @Operation(summary = "医疗人员信息上报")
    @PreAuthorize("@ss.hasPermission('hospital:inquiry-doctor:supervision')")
    public CommonResult<List<DoctorExternalTransmissionRespDto>> medicalPersonnelInformationReport(@Valid @RequestBody SupervisionMedicalPersonnelInformationReportReqVO reqVO) {
        return CommonResult.success(inquiryDoctorService.medicalPersonnelInformationReport(reqVO.getDoctorIds()));
    }
}
