package com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo;

import com.xyy.saas.inquiry.enums.doctor.CooperationStatusEnum;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryHospitalDeptDoctorSaveReqVO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "管理后台 - 医生信息新增/修改 Request VO")
@Data
@Accessors(chain = true)
public class InquiryDoctorSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "20449")
    private Long id;

    @Schema(description = "医生ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1276")
    private Long doctorId;

    @Schema(description = "医生名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @NotEmpty(message = "医生名称不能为空")
    @Size(max = 32, message = "医生名称不能超过32个字符")
    private String name;

    @Schema(description = "性别 1男 2女", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "性别 1男 2女不能为空")
    private Integer sex;

    @Schema(description = "身份证号码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "身份证号码不能为空")
    @Size(max = 32, message = "身份证号码不能超过32个字符")
    private String idCard;

    @Schema(description = "手机号")
    @NotEmpty(message = "手机号")
    @Size(max = 32, message = "手机号不能超过32个字符")
    private String mobile;

    @Schema(description = "第一职业机构")
    @NotEmpty(message = "第一职业机构不能为空")
    @Size(max = 128, message = "第一职业机构不能超过128个字符")
    private String firstPracticeName;

    @Schema(description = "第一职业机构等级")
    private Integer firstPracticeLevel;

    @Schema(description = "医生类型： 1全职医生 2兼职医生", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "医生类型不能为空")
    private Integer jobType;

    @Schema(description = "是否自动抢单： 0 否  1是", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "自动抢单状态不能为空")
    private Integer autoGrabStatus;

    /**
     * {@link CooperationStatusEnum}
     */
    @Schema(description = "合作状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "合作状态不能为空")
    private Integer cooperation;

    @Schema(description = "专业职称")
    @NotNull(message = "专业职称不能为空")
    private Integer titleCode;

    @Schema(description = "科室")
    @NotEmpty(message = "第一职业机构科室不能为空")
    @Size(max = 64, message = "第一职业机构科室名称不能超过64个字符")
    private String firstPracticeDeptName;

    @Schema(description = "医生渠道", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer canal;

    @Schema(description = "邀请人姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @Size(max = 64, message = "邀请人姓名不能超过64个字符")
    private String inviterName;

    @Schema(description = "邀请人工号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @Size(max = 32, message = "邀请人工号不能超过32个字符")
    private String inviterNo;

    @Schema(description = "医生医保编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @Size(max = 128, message = "医生医保编码不能超过128个字符")
    private String doctorMedicareNo;

    @Schema(description = "擅长专业,eg:擅长神经内科诊疗", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "擅长专业不能为空")
    @Size(max = 256, message = "擅长专业不能超过256个字符")
    private String professionalDec;

    @Schema(description = "个人简介", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "个人简介不能为空")
    @Size(max = 512, message = "个人简介不能超过512个字符")
    private String biography;

    @Schema(description = "医生关联医院信息", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<InquiryHospitalDeptDoctorSaveReqVO> hospitalDoctorItems;

    @Schema(description = "头像", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "头像不能为空")
    @Size(max = 512, message = "头像url不能超过512个字符")
    private String photo;

    @Schema(description = "查证结果", requiredMode = Schema.RequiredMode.REQUIRED)
    @Size(max = 2048, message = "查证结果url不能超过2048个字符")
    private String verifiyImgUrl;

    @Schema(description = "职称证", requiredMode = Schema.RequiredMode.REQUIRED)
    @Size(max = 2048, message = "职称证url不能超过2048个字符")
    private String titleImgUrl;

    @Schema(description = "个人证明", requiredMode = Schema.RequiredMode.REQUIRED)
    @Size(max = 2048, message = "个人证明url不能超过2048个字符")
    private String personalImgUrl;

    @Schema(description = "抗菌药物合格证明", requiredMode = Schema.RequiredMode.REQUIRED)
    @Size(max = 2048, message = "抗菌药物合格证明url不能超过2048个字符")
    private String antibacterialDrugCertificateImgUrl;

    @Schema(description = "执业证", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<String> occupationImgUrls;

    @Schema(description = "资格证", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<String> qualificationImgUrls;

    @Schema(description = "海南合同", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<String> hnContractImgUrls;

    @Schema(description = "成都合同", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<String> cdContractImgUrls;

    @Schema(description = "武汉合同", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<String> whContractImgUrls;

    // @Schema(description = "医生电子签章", requiredMode = Schema.RequiredMode.REQUIRED)
    // @Size(max = 256, message = "医生电子签章url不能超过256个字符")
    // private String doctorElectronSignChapterUrl;
    //
    // @Schema(description = "医生电子签名", requiredMode = Schema.RequiredMode.REQUIRED)
    // @Size(max = 256, message = "医生电子签名url不能超过256个字符")
    // private String doctorElectronSignImgUrl;

    @Schema(description = "身份证正面", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "身份证正面不能为空")
    @Size(max = 2048, message = "身份证正面url不能超过2048个字符")
    private String idCardFrontImgUrl;


    @Schema(description = "身份证反面", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "身份证反面不能为空")
    @Size(max = 2048, message = "身份证反面url不能超过2048个字符")
    private String idCardReverseImgUrl;

    @Schema(description = "民族", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer nationCode;

    @Schema(description = "学历", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer formalLevel;

    @Schema(description = "省份", requiredMode = Schema.RequiredMode.REQUIRED)
    private String orgProvinceCode;

    @Schema(description = "通信地址", requiredMode = Schema.RequiredMode.REQUIRED)
    @Size(max = 256, message = "通信地址不能超过256个字符")
    private String address;


    @Schema(description = "工作履历信息", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<DoctorWorkRecordSaveReqVO> jobItems;

    @Schema(description = "专业职称证书编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @Size(max = 128, message = "专业职称证书编号不能超过128个字符")
    private String titleNo;

    @Schema(description = "专业职称证书取得时间，例如：2016-03-20")
    private LocalDateTime titleTime;

    @Schema(description = "开始执业时间，例如：2021-03-20")
    @NotNull(message = "开始执业时间不能为空")
    private LocalDateTime startPracticeTime;

    @Schema(description = "执业结束时间，例如：2029-03-20")
    private LocalDateTime endPracticeDate;

    @Schema(description = "执业证书号", requiredMode = Schema.RequiredMode.REQUIRED)
    @Size(max = 128, message = "执业证书号不能超过128个字符")
    @NotEmpty(message = "执业证书号不能为空")
    private String professionalNo;

    @Schema(description = "执业证书取得时间，例如：2016-03-20")
    private LocalDateTime professionalTime;

    @Schema(description = "资格证书号")
    @Size(max = 128, message = "资格证书号不能超过128个字符")
    private String qualificationNo;

    @Schema(description = "资格证书取得时间，例如：2016-03-20")
    private LocalDateTime qualificationTime;

    @Schema(description = "收款人姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @Size(max = 32, message = "收款人姓名不能超过32个字符")
    private String payeeName;

    @Schema(description = "收款人身份证号码", requiredMode = Schema.RequiredMode.REQUIRED)
    @Size(max = 32, message = "收款人身份证号码不能超过32个字符")
    private String payeeIdCard;

    @Schema(description = "收款人手机号", requiredMode = Schema.RequiredMode.REQUIRED)
    @Size(max = 32, message = "收款人手机号不能超过32个字符")
    private String payeeTelPhone;

    @Schema(description = "银行卡号", requiredMode = Schema.RequiredMode.REQUIRED)
    @Size(max = 64, message = "银行卡号不能超过64个字符")
    private String payeeBankNo;

    @Schema(description = "开户行", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    @Size(max = 128, message = "开户行不能超过128个字符")
    private String payeeBankName;

    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "8822")
    private Long userId;

    @Schema(description = "环境标志：prod-真实数据；test-测试数据；show-线上演示数据")
    private String envTag;

    /**
     * 旧系统guid
     */
    private String guid;

    /**
     * 审核状态
     */
    private Integer auditStatus;
}