package com.xyy.saas.inquiry.hospital.server.mq.consumer.user;

import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.framework.tenant.core.util.TenantUtils;
import cn.iocoder.yudao.module.system.api.user.AdminUserApi;
import cn.iocoder.yudao.module.system.api.user.dto.AdminUserRespDTO;
import com.xyy.saas.eventbus.rocketmq.annotation.EventBusConsumer;
import com.xyy.saas.eventbus.rocketmq.annotation.EventBusListener;
import com.xyy.saas.inquiry.constant.TenantConstant;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.InquiryDoctorDO;
import com.xyy.saas.inquiry.hospital.server.dal.mysql.doctor.InquiryDoctorMapper;
import com.xyy.saas.inquiry.mq.user.UserBaseInfoChangeEvent;
import com.xyy.saas.inquiry.mq.user.UserBaseInfoDto;
import com.xyy.saas.inquiry.signature.api.ca.InquirySignatureCaAuthApi;
import jakarta.annotation.Resource;
import java.util.Objects;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

/**
 * @Desc 用户信息修改,
 * <AUTHOR>
 */
@Component
@Slf4j
@EventBusConsumer(groupId = "com_xyy_saas_inquiry_hospital_server_mq_consumer_user_UserBaseInfoChangeDoctorMQConsumer",
    topic = UserBaseInfoChangeEvent.TOPIC)
public class UserBaseInfoChangeDoctorMQConsumer {

    @Resource
    private InquiryDoctorMapper inquiryDoctorMapper;

    @Resource
    private AdminUserApi adminUserApi;

    @DubboReference
    private InquirySignatureCaAuthApi inquirySignatureCaAuthApi;

    @EventBusListener
    public void doctorUserBaseInfoUpdateMQConsumer(UserBaseInfoChangeEvent userBaseInfoChangeEvent) {
        UserBaseInfoDto userBaseInfoDto = userBaseInfoChangeEvent.getMsg();
        if (userBaseInfoDto == null || userBaseInfoDto.getUserId() == null) {
            return;
        }
        InquiryDoctorDO doctorDO = inquiryDoctorMapper.selectOne(InquiryDoctorDO::getUserId, userBaseInfoDto.getUserId());
        if (doctorDO == null) {
            return;
        }

        Long tenantId = Optional.ofNullable(userBaseInfoDto.getTenantId()).orElse(TenantContextHolder.getTenantId());
        if (!Objects.equals(tenantId, TenantConstant.DEFAULT_TENANT_ID)) {
            return;
        }
        AdminUserRespDTO userBaseInfo = TenantUtils.execute(tenantId, () -> adminUserApi.getUserBaseInfo(userBaseInfoDto.getUserId()));

        boolean change = !StringUtils.equals(userBaseInfo.getNickname(), doctorDO.getName())
            || (StringUtils.isNotBlank(userBaseInfo.getIdCard()) && !StringUtils.equals(userBaseInfo.getIdCard(), doctorDO.getIdCard()))
            || !StringUtils.equals(userBaseInfo.getMobile(), doctorDO.getMobile());

        if (change || !Objects.equals(userBaseInfo.getSex(), doctorDO.getSex())) {
            log.info("用户基础信息修改-医生,userId:{},nickName:{}", userBaseInfo.getId(), userBaseInfoDto.getNickname());
            inquiryDoctorMapper.updateById(InquiryDoctorDO.builder().id(doctorDO.getId()).name(userBaseInfo.getNickname()).idCard(userBaseInfo.getIdCard()).mobile(userBaseInfo.getMobile()).sex(userBaseInfo.getSex()).build());
        }

        if (change) {
            log.info("用户三要素修改,重置CA认证数据,userId:{},nickName:{}", userBaseInfo.getId(), userBaseInfoDto.getNickname());
            inquirySignatureCaAuthApi.resetCaAuth(doctorDO.getUserId());
        }
    }

}
