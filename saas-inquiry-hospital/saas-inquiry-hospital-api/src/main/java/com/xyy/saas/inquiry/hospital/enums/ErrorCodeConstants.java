package com.xyy.saas.inquiry.hospital.enums;

import cn.iocoder.yudao.framework.common.exception.ErrorCode;

/**
 * 使用 2_002_000_000 段
 */
public interface ErrorCodeConstants {

    ErrorCode SYSTEM_LOGIN_USER_NOT_EXISTS = new ErrorCode(2_002_000_000, "未获取到当前登录人员信息");

    ErrorCode SERVER_ERROR = new ErrorCode(2_002_000_001, "查询医生信息异常");

    // ========== 医生医院 2_002_001==========

    ErrorCode INQUIRY_HOSPITAL_NOT_EXISTS = new ErrorCode(2_002_001_000, "医院信息不存在");

    ErrorCode INQUIRY_HOSPITAL_DOCTOR_RELATION_NOT_EXISTS = new ErrorCode(2_002_001_001, "医院科室医生关系不存在");
    ErrorCode INQUIRY_HOSPITAL_EXISTS = new ErrorCode(2_002_001_002, "医院信息[{}]已存在");

    ErrorCode INQUIRY_HOSPITAL_DEPARTMENT_RELATION_NOT_EXISTS = new ErrorCode(2_002_001_003, "医院科室信息不存在");

    ErrorCode INQUIRY_HOSPITAL_DEPARTMENT_RELATION_DOCTOR_EXISTS = new ErrorCode(2_002_001_003, "医院科室下 医生{} 已存在");

    ErrorCode INQUIRY_DOCTOR_NOT_EXISTS = new ErrorCode(2_002_001_004, "医生信息不存在");

    ErrorCode INQUIRY_HOSPITAL_DEPARTMENT_NOT_EXISTS = new ErrorCode(2_002_001_005, "科室信息不存在");

    ErrorCode INQUIRY_HOSPITAL_DEPARTMENT_EXISTS = new ErrorCode(2_002_001_006, "科室信息[{}]已存在");

    ErrorCode DOCTOR_PRACTICE_NOT_EXISTS = new ErrorCode(2_002_001_007, "医生执业信息不存在");

    ErrorCode INQUIRY_DOCTOR_STATUS_NOT_EXISTS = new ErrorCode(2_002_001_008, "医生出诊状态关系不存在");

    ErrorCode DOCTOR_FILING_NOT_EXISTS = new ErrorCode(2_002_001_009, "医生备案信息不存在");

    ErrorCode DOCTOR_BILLING_NOT_EXISTS = new ErrorCode(2_002_001_010, "医生收款信息不存在");

    ErrorCode DOCTOR_WORK_RECORD_NOT_EXISTS = new ErrorCode(2_002_001_011, "医生工作履历记录不存在");

    ErrorCode DOCTOR_AUDITED_RECORD_NOT_EXISTS = new ErrorCode(2_002_001_012, "医生审核记录不存在");

    ErrorCode INQUIRY_HOSPITAL_DEPARTMENT_RELATION_EXISTS = new ErrorCode(2_002_001_013, "当前医院已存在此科室");

    ErrorCode INQUIRY_HOSPITAL_EMPLOYEE_NOT_EXISTS = new ErrorCode(2_002_001_014, "当前人员无医院员工权限");

    ErrorCode INQUIRY_PROFESSION_IDENTIFICATION_NOT_EXISTS = new ErrorCode(2_002_001_04, "问诊职业(医生药师)证件信息不存在");

    ErrorCode INQUIRY_DOCTOR_CAN_NOT_AUDIT = new ErrorCode(2_002_001_015, "当前医生非待审核状态,无法审核");

    ErrorCode INQUIRY_DOCTOR_NOT_AUDIT_PASS = new ErrorCode(2_002_001_016, "医生还未审核通过");

    ErrorCode INQUIRY_DOCTOR_NOT_COOPERATING = new ErrorCode(2_002_001_017, "医生非合作中状态");

    ErrorCode INQUIRY_DOCTOR_NOT_OFFLINE = new ErrorCode(2_002_001_018, "当前医生已出诊");

    ErrorCode DOCTOR_INQUIRY_TYPE_NOT_EXISTS = new ErrorCode(2_002_001_019, "当前医生无此开方权限");

    ErrorCode DOCTOR_NOT_IN_RECEPTION_STATUS = new ErrorCode(2_002_001_020, "当前医生非出诊状态");

    ErrorCode DOCTOR_IS_ONLINE_DELETE_ERROR = new ErrorCode(2_002_001_021, "当前医生已出诊,无法删除");

    ErrorCode INQUIRY_DOCTOR_VIDEO_NOT_EXISTS = new ErrorCode(2_002_001_022, "医生录屏记录不存在");

    ErrorCode INQUIRY_DOCTOR_CA_NOT_SET_AUTO = new ErrorCode(2_002_001_023, "医生未完成CA认证,不可设置[自动开方]权限");

    ErrorCode INQUIRY_DOCTOR_CREATE_EXISTS = new ErrorCode(2_002_001_024, "医生信息已存在,不可重复新增");

    ErrorCode INQUIRY_DOCTOR_AUTO_VIDEO_NOT_EXISTS = new ErrorCode(2_002_001_025, "医生编号：{},未维护录播视频");

    ErrorCode INQUIRY_DOCTOR_VIDEO_NOT_SET = new ErrorCode(2_002_001_026, "视频自动开方未获取到录屏编码");

    // 问诊已评价不能重复评价
    ErrorCode INQUIRY_DOCTOR_REVIEW_EXISTS = new ErrorCode(2_002_001_027, "问诊已评价不能重复评价");

    // 已出诊的医生必须保留一项接诊权限
    ErrorCode INQUIRY_DOCTOR_ONLINE_STATUS_ERROR = new ErrorCode(2_002_001_028, "当前医生已出诊，修改时至少保留一项真人接诊权限");

    ErrorCode DOCTOR_IS_ONLINE_LOGOFF_ERROR = new ErrorCode(2_002_001_029, "出诊状态下无法注销账号,请先停诊");

    ErrorCode DOCTOR_IS_RECEPTING_LOGOFF_ERROR = new ErrorCode(2_002_001_030, "还有接诊中的患者，请接诊完成后进行停诊注销");

    // 问诊科室与医生科室不匹配
    ErrorCode INQUIRY_DOCTOR_DEPARTMENT_NOT_MATCH = new ErrorCode(2_002_001_031, "问诊科室与医生科室不匹配");

    // ===== 医生常用语 2_002_002

    ErrorCode DOCTOR_QUICK_REPLY_MSG_NOT_EXISTS = new ErrorCode(2_002_002_000, "医生快捷回复语不存在");

    ErrorCode DOCTOR_QUICK_REPLY_MSG_TITLE_LIMIT = new ErrorCode(2_002_002_001, "快捷回复语分组不可超过{}条");

    ErrorCode DOCTOR_QUICK_REPLY_MSG_CONTENT_LIMIT = new ErrorCode(2_002_002_002, "组内常用语不可超过{}条");

//    ======= 诊断 =======  2_002_003

    ErrorCode INQUIRY_DIAGNOSIS_NOT_EXISTS = new ErrorCode(2_002_003_000, "诊断信息不存在");

    ErrorCode INQUIRY_DIAGNOSIS_EXISTS = new ErrorCode(2_002_003_001, "诊断信息[{}]已存在");

    ErrorCode MAIN_SUIT_NOT_EXISTS = new ErrorCode(2_002_003_002, "主诉信息不存在");

    ErrorCode MAIN_SUIT_EXISTS = new ErrorCode(2_002_003_003, "主诉信息[{}]已存在");

    ErrorCode INQUIRY_DIAGNOSIS_DEPARTMENT_RELATION_NOT_EXISTS = new ErrorCode(2_002_003_004, "诊断关联科室信息不存在");

    ErrorCode INQUIRY_DIAGNOSIS_DUPLICATED = new ErrorCode(2_002_003_005, "诊断信息不可重复");

    //  ======= 处方相关 =======  2_002_004

    ErrorCode INQUIRY_PRESCRIPTION_NOT_EXISTS = new ErrorCode(2_002_004_000, "处方记录不存在");

    ErrorCode INQUIRY_PRESCRIPTION_DETAIL_NOT_EXISTS = new ErrorCode(2_002_004_001, "处方记录详情不存在");

    ErrorCode INQUIRY_PRESCRIPTION_SIGNATURE_ERROR = new ErrorCode(2_002_004_002, "处方签章失败:[{}]");

    ErrorCode INQUIRY_PRESCRIPTION_PRICING_ERROR = new ErrorCode(2_002_004_003, "处方已划价,不可重复操作");

    ErrorCode INQUIRY_PRESCRIPTION_PRICING_DETAIL_ERROR = new ErrorCode(2_002_004_004, "处方划价失败,处方明细不匹配");

    ErrorCode INQUIRY_PRESCRIPTION_PRICING_NO_AUDIT_ERROR = new ErrorCode(2_002_004_005, "此处方未完成审核，请先审核完成再处理划价");

    ErrorCode INQUIRY_PRESCRIPTION_NO_PRICING_ERROR = new ErrorCode(2_002_004_006, "{}处方未进行划价，请划价后再打印处方！");

    ErrorCode INQUIRY_PRESCRIPTION_MAX_SIZE_ERROR = new ErrorCode(2_002_004_007, "一次最多只能{}{}条数据");

    ErrorCode INQUIRY_PRESCRIPTION_NO_DATA_ERROR = new ErrorCode(2_002_004_008, "没有可以{}的处方数据");

    ErrorCode INQUIRY_PRESCRIPTION_UN_OPERATE_ERROR = new ErrorCode(2_002_004_007, "以下处方状态未完成,不可操作{},处方号:{}");

    ErrorCode INQUIRY_PRESCRIPTION_PDF_DOWN_FAIL = new ErrorCode(2_002_004_008, "处方下载失败:{}");

    ErrorCode INQUIRY_REMOTE_AUDIT_CANCEL_FAIL = new ErrorCode(2_002_004_009, "远程审方取消失败,当前问诊单已处理");

    ErrorCode INQUIRY_PRESCRIPTION_BATCH_PRINT_ERROR = new ErrorCode(2_002_004_010, "批量拼接打印处方失败:{}");


    // ======= 问诊相关 =======  2_002_005
    ErrorCode AUTO_INQUIRY_RECEPTION_ERROR = new ErrorCode(2_002_005_000, "自动开方医生接诊失败");

    ErrorCode INQUIRY_NO_DOCTOR_RECEPTION = new ErrorCode(2_002_005_001, "未匹配到可接诊医生");

    ErrorCode INQUIRY_DOCTOR_RECEPTION_NOT_MATCH = new ErrorCode(2_002_005_002, "当前问诊已被其他医生接诊");

    // ======= 门诊病例 =======  2_002_006
    ErrorCode INQUIRY_CLINICAL_CASE_NOT_EXISTS = new ErrorCode(2_002_006_000, "门诊病例不存在");

    // ======= 外配(电子)处方 =======  2_002_007
    ErrorCode SAAS_PRESCRIPTION_EXTERNAL_NOT_EXISTS = new ErrorCode(2_002_007_000, "外配(电子)处方记录不存在");

    // ======= 医保订单信息 =======  2_002_008
    ErrorCode MEDICAL_INSURANCE_ORDER_NOT_EXISTS = new ErrorCode(2_002_008_000, "医保订单信息不存在");

    // 陕西监管备案
    ErrorCode DOCTOR_FILL_SHAANXI_REGULATORY_FAIL = new ErrorCode(2_002_009_000, "陕西监管备案失败：\n{}");
}


