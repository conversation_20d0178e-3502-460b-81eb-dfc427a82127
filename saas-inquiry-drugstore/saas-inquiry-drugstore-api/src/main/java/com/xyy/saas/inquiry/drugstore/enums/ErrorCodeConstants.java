package com.xyy.saas.inquiry.drugstore.enums;

import cn.iocoder.yudao.framework.common.exception.ErrorCode;

/**
 * 使用 2_001_000_000 段
 *
 * @Author:chenxiaoyi
 * @Date:2024/09/05 12:02
 */
public interface ErrorCodeConstants {


    ErrorCode TENANT_PARAM_INFO_IS_NULL = new ErrorCode(2_001_000_001, "门店基本参数信息不可为空");

    ErrorCode TENANT_PACKAGE_COST_RELATION_NOT_EXISTS = new ErrorCode(2_001_000_002, "门店问诊套餐额度不存在");

    ErrorCode TENANT_PACKAGE_INQUIRY_RECORD_RELATION_NOT_EXISTS = new ErrorCode(2_001_000_003, "门店问诊套餐操作记录不存在");

    ErrorCode TENANT_PACKAGE_INQUIRY_INFO_ISNULL = new ErrorCode(2_001_000_004, "门店问诊套餐初始化信息为空");

    ErrorCode TENANT_PACKAGE_COST_RELATION_EXISTS = new ErrorCode(2_001_000_005, "门店问诊套餐额度已存在");

    ErrorCode TENANT_PACKAGE_COST_INIT_ERROR = new ErrorCode(2_001_000_006, "门店问诊套餐额度初始化失败:{}");

    ErrorCode TENANT_PACKAGE_INQUIRY_NO_ERROR = new ErrorCode(2_001_000_007, "门店无可用套餐额度");

    ErrorCode TENANT_PACKAGE_INQUIRY_COST_DEDUCT_ERROR = new ErrorCode(2_001_000_008, "门店套餐额度变更失败,请重试");

    ErrorCode TENANT_PACKAGE_INQUIRY_RECORD_ERROR = new ErrorCode(2_001_000_009, "门店套餐额度记录失败");


    ErrorCode TENANT_PARAM_CONFIG_NOT_EXISTS = new ErrorCode(2_001_000_010, "门店参数配置不存在");

    ErrorCode INQUIRY_OPTION_CONFIG_NOT_EXISTS = new ErrorCode(2_001_000_011, "选项配置不存在");


    ErrorCode INQUIRY_AREA_NOT_EXISTS = new ErrorCode(2_001_000_012, "区域编码【{}】不存在");
    ErrorCode INQUIRY_OPTION_CONFIG_NOT_FOR_TARGET = new ErrorCode(2_001_000_013, "不是{}选项配置");


    ErrorCode DRUGSTORE_MINI_APP_QR_CODE_CREATE_ERROR = new ErrorCode(2_001_000_014, "门店小程序二维码创建失败:{}");

    ErrorCode DRUGSTORE_MP_QR_CODE_CREATE_ERROR = new ErrorCode(2_001_000_015, "门店公众号二维码创建失败:{}");

    ErrorCode TUTORIAL_MAINTENANCE_NOT_EXISTS = new ErrorCode(2_001_000_016, "教程不存在");

    ErrorCode TUTORIAL_MAINTENANCE_CREATE_FAIL = new ErrorCode(2_001_000_016, "创建教程失败");

    ErrorCode OPTION_AREA_PRODUCT_NUM_LIMIT_FAIL = new ErrorCode(2_001_000_017, "药品数量不能超过1000");
}
