package com.xyy.saas.inquiry.drugstore.api.tenant;

import cn.iocoder.yudao.framework.tenant.core.util.TenantUtils;
import com.xyy.saas.inquiry.drugstore.api.tenant.dto.TenantParamConfigDTO;
import com.xyy.saas.inquiry.enums.inquiry.PrescriptionDateTypeEnum;
import com.xyy.saas.inquiry.enums.prescription.PrescriptionRemoteAuditTypeEnum;
import com.xyy.saas.inquiry.enums.tenant.TenantParamConfigTypeEnum;
import com.xyy.saas.inquiry.enums.tenant.TenantTypeEnum;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 门店配置Api
 *
 * @Author:chenxiaoyi
 * @Date:2024/12/04 15:09
 */
public interface TenantParamConfigApi {


    /**
     * 批量查询门店配置
     *
     * @param tenantIds 门店ids
     * @param type      类型
     * @return 配置列表
     */
    List<TenantParamConfigDTO> batchQueryTenantParamConfig(List<Long> tenantIds, TenantParamConfigTypeEnum type);

    /**
     * 查询门店配置
     *
     * @param tenantId
     * @param type
     * @return
     */
    TenantParamConfigDTO queryTenantParamConfig(Long tenantId, TenantParamConfigTypeEnum type);

    /**
     * 获得参数配置 先取自己，没有取全局
     *
     * @param tenantParamConfigTypeEnum 枚举类型
     * @return String类型的配置值, 没有返回空, 需要StringUtils.isBlank校验
     */
    String getParamConfigValueSelf2Sys(TenantParamConfigTypeEnum tenantParamConfigTypeEnum);

    /**
     * 获得参数配置 先取自己，没有取全局
     *
     * @param tenantParamConfigTypeEnum 枚举类型
     * @return Integer类型的配置值, 没有-null
     */
    Integer getParamConfigValueSelf2SysInteger(TenantParamConfigTypeEnum tenantParamConfigTypeEnum);

    /**
     * 获取门店处方日期类型 - 没有默认 yyyy-MM-dd HH:mm:ss
     */
    default Integer getTenantPresDateType(Long tenantId) {
        return TenantUtils.execute(tenantId, () -> Optional.ofNullable(getParamConfigValueSelf2SysInteger(TenantParamConfigTypeEnum.INQUIRY_PRES_DATE_TYPE))
            .orElse(PrescriptionDateTypeEnum.YYYY_MM_DD_HH_MM_SS.getCode()));
    }

    /**
     * 获取门店远程审方类型 是否总部审方 - 单体固定false,没有默认 总部
     */
    default boolean isHeadTenantRemoteAudit(Long tenantId, TenantTypeEnum wzTenantType) {
        if (Objects.equals(wzTenantType, TenantTypeEnum.SINGLE_STORE)) {
            return false;
        }
        Integer remoteType = TenantUtils.execute(tenantId, () -> Optional.ofNullable(getParamConfigValueSelf2SysInteger(TenantParamConfigTypeEnum.PRESCRIPTION_REMOTE_AUDIT_TYPE))
            .orElse(PrescriptionRemoteAuditTypeEnum.HEAD_AUDIT.getCode()));

        return Objects.equals(remoteType, PrescriptionRemoteAuditTypeEnum.HEAD_AUDIT.getCode());
    }

    /**
     * 先取全局，没有取自己
     *
     * @param tenantParamConfigTypeEnum 枚举
     * @return 配置值
     */
    String getParamConfigValueSys2Self(TenantParamConfigTypeEnum tenantParamConfigTypeEnum);

    /**
     * 获得参数配置 先取全局，没有取自己
     *
     * @param tenantParamConfigTypeEnum 枚举类型
     * @return Integer类型的配置值, 没有-null
     */
    Integer getParamConfigValueSys2SelfInteger(TenantParamConfigTypeEnum tenantParamConfigTypeEnum);


}
