package com.xyy.saas.inquiry.drugstore.server.dal.mysql.tenant;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.tenant.core.aop.TenantIgnore;
import com.xyy.saas.inquiry.drugstore.server.controller.admin.cost.vo.TenantPackageCostLogReqVO;
import com.xyy.saas.inquiry.drugstore.server.dal.dataobject.tenant.TenantPackageCostLogDO;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 门店问诊套餐操作记录 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface TenantPackageCostLogMapper extends BaseMapperX<TenantPackageCostLogDO> {

    default PageResult<TenantPackageCostLogDO> selectPage(TenantPackageCostLogReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<TenantPackageCostLogDO>()
            .eqIfPresent(TenantPackageCostLogDO::getTenantId, reqVO.getTenantId())
            .inIfPresent(TenantPackageCostLogDO::getTenantId, reqVO.getTenantIds())
            .eqIfPresent(TenantPackageCostLogDO::getCostId, reqVO.getCostId())
            .inIfPresent(TenantPackageCostLogDO::getCostId, reqVO.getCostIds())
            .eqIfPresent(TenantPackageCostLogDO::getBizId, reqVO.getBizId())
            .eqIfPresent(TenantPackageCostLogDO::getChangeCost, reqVO.getChangeCost())
            .betweenIfPresent(TenantPackageCostLogDO::getCreateTime, reqVO.getCreateTime())
            .eqIfPresent(TenantPackageCostLogDO::getWayType, reqVO.getWayType())
            .eqIfPresent(TenantPackageCostLogDO::getRecordType, reqVO.getRecordType())
            .inIfPresent(TenantPackageCostLogDO::getRecordType, reqVO.getRecordTypes())
            .orderByDesc(TenantPackageCostLogDO::getId));
    }

    default TenantPackageCostLogDO selectByBizId(String bizId, Integer recordType) {
        return selectOne(new LambdaQueryWrapperX<TenantPackageCostLogDO>()
            .eq(TenantPackageCostLogDO::getBizId, bizId)
            .eqIfPresent(TenantPackageCostLogDO::getRecordType, recordType), false);
    }

    default List<TenantPackageCostLogDO> selectByBizIds(List<String> bizIds, Integer recordType) {
        return selectList(new LambdaQueryWrapperX<TenantPackageCostLogDO>()
            .in(TenantPackageCostLogDO::getBizId, bizIds)
            .eqIfPresent(TenantPackageCostLogDO::getRecordType, recordType));
    }

    /**
     * 统计问诊消耗额度数量
     *
     * @param tenantId
     * @param costIds
     * @param bizType
     * @return
     */
    @TenantIgnore
    Long countInquiryByTenantCostId(@Param("tenantId") Long tenantId, @Param("costIds") List<Long> costIds, @Param("bizType") Integer bizType);

}