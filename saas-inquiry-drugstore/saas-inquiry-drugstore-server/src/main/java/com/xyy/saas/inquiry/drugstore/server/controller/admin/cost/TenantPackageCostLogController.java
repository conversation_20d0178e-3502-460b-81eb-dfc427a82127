package com.xyy.saas.inquiry.drugstore.server.controller.admin.cost;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantPackageShareRelationPageReqDto;
import com.xyy.saas.inquiry.drugstore.server.controller.admin.cost.vo.TenantPackageCostLogReqVO;
import com.xyy.saas.inquiry.drugstore.server.controller.admin.cost.vo.TenantPackageCostLogRespVO;
import com.xyy.saas.inquiry.drugstore.server.dal.dataobject.tenant.TenantPackageCostLogDO;
import com.xyy.saas.inquiry.drugstore.server.service.tenant.TenantPackageCostLogService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.io.IOException;
import java.util.List;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


@Tag(name = "管理后台 - 门店问诊套餐操作记录日志")
@RestController
@RequestMapping("/drugstore/tenant-package-cost-log")
@Validated
public class TenantPackageCostLogController {

    @Resource
    private TenantPackageCostLogService tenantPackageCostLogService;


    @GetMapping("/get")
    @Operation(summary = "获得门店问诊套餐操作记录")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    // @PreAuthorize("@ss.hasPermission('drugstore:tenant-package-cost-log:query')")
    public CommonResult<TenantPackageCostLogRespVO> getTenantPackageCostLog(@RequestParam("id") Long id) {
        TenantPackageCostLogDO tenantPackageCostLog = tenantPackageCostLogService.getTenantPackageCostLog(id);
        return success(BeanUtils.toBean(tenantPackageCostLog, TenantPackageCostLogRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得门店问诊套餐操作记录分页")
    public CommonResult<PageResult<TenantPackageCostLogRespVO>> getTenantPackageCostLogPage(@Valid TenantPackageCostLogReqVO pageReqVO) {
        PageResult<TenantPackageCostLogRespVO> pageResult = tenantPackageCostLogService.getTenantPackageCostLogPage(pageReqVO);
        return success(pageResult);
    }


    @GetMapping("/share-page")
    @Operation(summary = "获得共享套餐门店分页+统计数量")
    public CommonResult<PageResult<TenantPackageCostLogRespVO>> getTenantPackageCostLogSharePage(@Valid TenantPackageShareRelationPageReqDto relatePageReqDto) {
        PageResult<TenantPackageCostLogRespVO> pageResult = tenantPackageCostLogService.getTenantPackageCostLogSharePage(relatePageReqDto);
        return success(pageResult);
    }


    @GetMapping("/export-excel")
    @Operation(summary = "导出门店问诊套餐操作记录 Excel")
    // @PreAuthorize("@ss.hasPermission('drugstore:tenant-package-cost-log:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportTenantPackageCostLogExcel(@Valid TenantPackageCostLogReqVO pageReqVO,
        HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<TenantPackageCostLogRespVO> list = tenantPackageCostLogService.getTenantPackageCostLogPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "门店问诊套餐操作记录.xls", "数据", TenantPackageCostLogRespVO.class, list);
    }

}