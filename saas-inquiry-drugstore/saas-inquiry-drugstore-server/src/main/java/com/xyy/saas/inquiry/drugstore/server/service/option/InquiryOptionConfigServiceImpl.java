package com.xyy.saas.inquiry.drugstore.server.service.option;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.TENANT_NOT_EXISTS;
import static com.xyy.saas.inquiry.drugstore.enums.ErrorCodeConstants.INQUIRY_OPTION_CONFIG_NOT_EXISTS;
import static com.xyy.saas.inquiry.drugstore.enums.ErrorCodeConstants.INQUIRY_OPTION_CONFIG_NOT_FOR_TARGET;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.web.core.util.WebFrameworkUtils;
import cn.iocoder.yudao.module.system.api.tenant.TenantApi;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantReqDto;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantRespDto;
import cn.iocoder.yudao.module.system.api.user.AdminUserApi;
import com.xyy.saas.inquiry.constant.TenantConstant;
import com.xyy.saas.inquiry.drugstore.api.option.dto.InquiryOptionAreaConfigRespDto;
import com.xyy.saas.inquiry.drugstore.api.option.dto.InquiryOptionConfigQueryDto;
import com.xyy.saas.inquiry.drugstore.api.option.dto.InquiryOptionConfigRespDto;
import com.xyy.saas.inquiry.drugstore.api.option.dto.InquiryOptionGlobalConfigRespDto;
import com.xyy.saas.inquiry.drugstore.api.option.dto.InquiryOptionStoreConfigRespDto;
import com.xyy.saas.inquiry.drugstore.api.option.dto.InquiryProductDoctorDto;
import com.xyy.saas.inquiry.drugstore.enums.InquiryOptionTypeEnum;
import com.xyy.saas.inquiry.drugstore.enums.InquiryTargetTypeEnum;
import com.xyy.saas.inquiry.drugstore.server.controller.admin.option.vo.InquiryOptionAreaConfigReqVO;
import com.xyy.saas.inquiry.drugstore.server.controller.admin.option.vo.InquiryOptionConfigPageReqVO;
import com.xyy.saas.inquiry.drugstore.server.controller.admin.option.vo.InquiryOptionGlobalConfigReqVO;
import com.xyy.saas.inquiry.drugstore.server.controller.admin.option.vo.InquiryOptionStoreConfigReqVO;
import com.xyy.saas.inquiry.drugstore.server.convert.option.InquiryOptionConfigConvert;
import com.xyy.saas.inquiry.drugstore.server.dal.dataobject.option.InquiryOptionConfigDO;
import com.xyy.saas.inquiry.drugstore.server.dal.mysql.option.InquiryOptionConfigMapper;
import com.xyy.saas.inquiry.hospital.api.doctor.InquiryDoctorApi;
import com.xyy.saas.inquiry.hospital.api.doctor.dto.InquiryDoctorDto;
import com.xyy.saas.inquiry.hospital.api.hospital.InquiryHospitalApi;
import com.xyy.saas.inquiry.hospital.api.hospital.dto.InquiryHospitalReqDto;
import com.xyy.saas.inquiry.hospital.api.hospital.dto.InquiryHospitalRespDto;
import com.xyy.saas.inquiry.pojo.TenantDto;
import com.xyy.saas.inquiry.util.UserUtil;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

/**
 * 问诊配置选项 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class InquiryOptionConfigServiceImpl implements InquiryOptionConfigService {

    @Resource
    private InquiryOptionConfigMapper inquiryOptionConfigMapper;
    @Resource
    private TenantApi tenantApi;

    @Resource
    private AdminUserApi adminUserApi;

    @DubboReference
    private InquiryHospitalApi inquiryHospitalApi;


    @DubboReference
    private InquiryDoctorApi inquiryDoctorApi;

    private int insertOrUpdateBatch(List<InquiryOptionConfigDO> saveDoList) {
        if (CollectionUtils.isEmpty(saveDoList)) {
            return 0;
        }

        saveDoList.forEach(s -> {
            s.setCreator(WebFrameworkUtils.getLoginUserId() == null ? "" : WebFrameworkUtils.getLoginUserId().toString());
        });

        return inquiryOptionConfigMapper.insertOrUpdateBatch(saveDoList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int saveInquiryOptionGlobalConfig(InquiryOptionGlobalConfigReqVO reqVO) {
        InquiryOptionConfigQueryDto queryDto = new InquiryOptionConfigQueryDto();
        // 全局-全部配置
        queryDto.setTargetType(InquiryTargetTypeEnum.GLOBAL.getType())
            .setTargetId(TenantConstant.DEFAULT_TENANT_ID);
        // 查询出当前配置
        List<InquiryOptionConfigDO> doList = inquiryOptionConfigMapper.queryList(queryDto);
        List<InquiryOptionConfigDO> saveDoList = InquiryOptionConfigConvert.INSTANCE.globalOptions2DOList(reqVO, doList);
        // 批量保存
        return insertOrUpdateBatch(saveDoList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int saveInquiryOptionAreaConfig(InquiryOptionAreaConfigReqVO reqVO) {
        InquiryOptionConfigQueryDto queryDto = new InquiryOptionConfigQueryDto();
        // Area areaVO = AreaUtils.getArea(reqVO.getArea());
        queryDto.setTargetType(InquiryTargetTypeEnum.AREA.getType())
            .setTargetIds(reqVO.getAreas())
            // 配置类型
            .setOptionTypeList(List.of(reqVO.getOptionType()));

        // if (PROVINCE.getType().equals(areaVO.getType())) {
        //     queryDto.setProvinceCode("" + areaVO.getId());
        // } else if (AreaTypeEnum.CITY.getType().equals(areaVO.getType())) {
        //     queryDto.setProvinceCode("" + areaVO.getParent().getId());
        //     queryDto.setCityCode("" + areaVO.getId());
        // } else if (AreaTypeEnum.DISTRICT.getType().equals(areaVO.getType())) {
        //     queryDto.setProvinceCode("" + areaVO.getParent().getParent().getId());
        //     queryDto.setCityCode("" + areaVO.getParent().getId());
        //     queryDto.setAreaCode("" + areaVO.getId());
        // } else {
        //     throw exception(INQUIRY_AREA_NOT_EXISTS, reqVO.getArea());
        // }
        // 查询出当前配置
        List<InquiryOptionConfigDO> doList = inquiryOptionConfigMapper.queryList(queryDto);
        List<InquiryOptionConfigDO> saveDoList = InquiryOptionConfigConvert.INSTANCE.areaOptions2DOList(reqVO, doList);
        // 批量保存
        return insertOrUpdateBatch(saveDoList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int saveInquiryOptionStoreConfig(InquiryOptionStoreConfigReqVO reqVO) {
        if (CollUtil.isEmpty(reqVO.getTenantIdList())) {
            return 0;
        }

        return reqVO.getTenantIdList().stream().distinct().mapToInt(tenantId -> {
            // 查询租户信息
            TenantDto tenantDto = tenantApi.getTenant(tenantId);
            if (tenantDto == null) {
                throw exception(TENANT_NOT_EXISTS);
            }
            InquiryOptionConfigQueryDto queryDto = new InquiryOptionConfigQueryDto();
            queryDto.setTargetType(InquiryTargetTypeEnum.STORE.getType())
                .setTargetId(tenantId)
                // 配置类型
                .setOptionTypeList(List.of(reqVO.getOptionType()));
            // 查询出当前配置
            List<InquiryOptionConfigDO> doList = inquiryOptionConfigMapper.queryList(queryDto);
            List<InquiryOptionConfigDO> saveDoList = InquiryOptionConfigConvert.INSTANCE.storeOptions2DOList(reqVO, tenantDto, doList);
            // 批量保存
            return insertOrUpdateBatch(saveDoList);
        }).sum();
    }

    @Override
    @Transactional(readOnly = true)
    public InquiryOptionGlobalConfigRespDto getInquiryOptionGlobalConfig(InquiryOptionConfigQueryDto dto) {
        dto.setTargetType(InquiryTargetTypeEnum.GLOBAL.type)
            .setTargetId(TenantConstant.DEFAULT_TENANT_ID);
        List<InquiryOptionConfigDO> optionList = inquiryOptionConfigMapper.queryList(dto);
        // 转换返回
        return InquiryOptionConfigConvert.INSTANCE.convertDO2GlobalOptions(optionList);
    }

    @Override
    @Transactional(readOnly = true)
    public PageResult<InquiryOptionAreaConfigRespDto> getInquiryOptionAreaConfigPage(
        InquiryOptionConfigPageReqVO reqVO) {
        reqVO.setTargetType(InquiryTargetTypeEnum.AREA.type);
        PageResult<InquiryOptionConfigDO> pageResult = inquiryOptionConfigMapper.selectPage(reqVO);
        // 转换返回
        List<InquiryOptionAreaConfigRespDto> list = pageResult.getList().stream().map(InquiryOptionConfigConvert.INSTANCE::convertDO2AreaOptions).toList();
        UserUtil.fillUserInfo(list, adminUserApi::getUserNameMap);
        // 填充医院信息
        fillExtInfo(list);
        return new PageResult<>(list, pageResult.getTotal());
    }

    private void fillExtInfo(List<InquiryOptionAreaConfigRespDto> list) {
        List<String> hosPrefs = list.stream()
            .flatMap(a -> Stream.of(a.getPresAllRealPeopleInquiryHospitalPrefs(), a.getAreaInquiryHospitalPrefs()).filter(CollUtil::isNotEmpty).flatMap(Collection::stream)).toList();
        // 填充医院信息
        if (CollUtil.isNotEmpty(hosPrefs)) {
            List<InquiryHospitalRespDto> inquiryHospitals = inquiryHospitalApi.getInquiryHospitals(InquiryHospitalReqDto.builder().inquiryHospitalPrefs(hosPrefs).disable(null).build());
            if (CollUtil.isNotEmpty(inquiryHospitals)) {
                for (InquiryOptionAreaConfigRespDto respDto : list) {
                    if (CollUtil.isNotEmpty(respDto.getPresAllRealPeopleInquiryHospitalPrefs())) {
                        respDto.setPresAllRealPeopleInquiryHospitalPrefNames(inquiryHospitals.stream().filter(a -> respDto.getPresAllRealPeopleInquiryHospitalPrefs().contains(a.getPref())).map(InquiryHospitalRespDto::getName).toList());
                    }
                    if (CollUtil.isNotEmpty(respDto.getAreaInquiryHospitalPrefs())) {
                        respDto.setAreaInquiryHospitalNames(inquiryHospitals.stream().filter(a -> respDto.getAreaInquiryHospitalPrefs().contains(a.getPref())).map(InquiryHospitalRespDto::getName).toList());
                    }
                }
            }
        }

        // 填充医生信息
        List<String> docPrefs = list.stream()
            .flatMap(a -> Stream.of(a.getAreaInquiryDoctorPrefs()).filter(CollUtil::isNotEmpty).flatMap(Collection::stream)).toList();
        if (CollUtil.isNotEmpty(docPrefs)) {
            List<InquiryDoctorDto> doctors = inquiryDoctorApi.getInquiryDoctorByPrefList(docPrefs);
            if (CollUtil.isNotEmpty(doctors)) {
                for (InquiryOptionAreaConfigRespDto respDto : list) {
                    if (CollUtil.isNotEmpty(respDto.getAreaInquiryDoctorPrefs())) {
                        respDto.setAreaInquiryDoctorPrefs(doctors.stream().filter(a -> respDto.getAreaInquiryDoctorPrefs().contains(a.getPref()))
                            .map(d -> d.getName().concat("【").concat(d.getPref()).concat("】")).toList());
                    }
                }
            }
        }
    }

    @Override
    @Transactional(readOnly = true)
    public List<InquiryOptionAreaConfigRespDto> getInquiryOptionAreaAllConfig(InquiryOptionConfigPageReqVO reqVO) {
        InquiryOptionConfigQueryDto queryDto = InquiryOptionConfigQueryDto.builder().optionTypeList(Collections.singletonList(reqVO.getOptionType())).targetType(InquiryTargetTypeEnum.AREA.type).build();
        List<InquiryOptionConfigDO> doList = inquiryOptionConfigMapper.queryList(queryDto);
        return InquiryOptionConfigConvert.INSTANCE.convertDtos(doList);
    }

    @Override
    @Transactional(readOnly = true)
    public PageResult<InquiryOptionStoreConfigRespDto> getInquiryOptionStoreConfigPage(
        InquiryOptionConfigPageReqVO reqVO) {
        if (StringUtils.isNotBlank(reqVO.getTargetName())) {
            List<TenantDto> tenantList = tenantApi.getTenantList(reqVO.getTargetName());
            if (CollectionUtils.isEmpty(tenantList)) {
                return PageResult.empty();
            }
            reqVO.setTargetIds(cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertList(tenantList, TenantDto::getId));
            reqVO.setTargetName(null);
        }
        reqVO.setTargetType(InquiryTargetTypeEnum.STORE.type);
        PageResult<InquiryOptionConfigDO> pageResult = inquiryOptionConfigMapper.selectPage(reqVO);
        // 转换返回
        List<InquiryOptionStoreConfigRespDto> list = pageResult.getList().stream().map(InquiryOptionConfigConvert.INSTANCE::convertDO2StoreOptions).toList();
        if (CollUtil.isEmpty(list)) {
            return new PageResult<>();
        }
        Map<Long, TenantDto> tenantDtoMap = tenantApi.getTenantListMap(cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertList(list, InquiryOptionStoreConfigRespDto::getTenantId));
        for (InquiryOptionStoreConfigRespDto dto : list) {
            Optional.ofNullable(tenantDtoMap.get(dto.getTenantId())).ifPresent(t -> dto.setTenantPref(t.getPref()));
        }
        UserUtil.fillUserInfo(list, adminUserApi::getUserNameMap);
        return new PageResult<>(list, pageResult.getTotal());
    }

    @Override
    @Transactional(readOnly = true)
    public InquiryOptionAreaConfigRespDto getInquiryOptionAreaConfig(Long id) {
        InquiryOptionConfigDO optionConfigDO = inquiryOptionConfigMapper.selectById(id);
        if (optionConfigDO == null) {
            throw exception(INQUIRY_OPTION_CONFIG_NOT_EXISTS);
        }
        if (!Objects.equals(optionConfigDO.getTargetType(), InquiryTargetTypeEnum.AREA.type)) {
            throw exception(INQUIRY_OPTION_CONFIG_NOT_FOR_TARGET, InquiryTargetTypeEnum.AREA.name);
        }
        return InquiryOptionConfigConvert.INSTANCE.convertDO2AreaOptions(optionConfigDO);
    }

    @Override
    @Transactional(readOnly = true)
    public InquiryOptionStoreConfigRespDto getInquiryOptionStoreConfig(Long id) {
        InquiryOptionConfigDO optionConfigDO = inquiryOptionConfigMapper.selectById(id);
        if (optionConfigDO == null) {
            throw exception(INQUIRY_OPTION_CONFIG_NOT_EXISTS);
        }
        if (!Objects.equals(optionConfigDO.getTargetType(), InquiryTargetTypeEnum.STORE.type)) {
            throw exception(INQUIRY_OPTION_CONFIG_NOT_FOR_TARGET, InquiryTargetTypeEnum.STORE.name);
        }
        return InquiryOptionConfigConvert.INSTANCE.convertDO2StoreOptions(optionConfigDO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteInquiryOptionConfig(Long id) {
        // 校验存在
        validateInquiryOptionConfigExists(id);
        // 删除
        return inquiryOptionConfigMapper.deleteById(id);
    }

    private void validateInquiryOptionConfigExists(Long id) {
        if (inquiryOptionConfigMapper.selectById(id) == null) {
            throw exception(INQUIRY_OPTION_CONFIG_NOT_EXISTS);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchDeleteInquiryOptionConfig(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return 0;
        }
        return inquiryOptionConfigMapper.deleteByIds(ids);
    }

    @Override
    @Transactional(readOnly = true)
    public InquiryOptionConfigRespDto getInquiryOptionConfig(TenantDto tenantDto,
        InquiryOptionTypeEnum... optionTypeEnums) {
        Assert.notNull(tenantDto, "租户信息不能为空");
        // 查询全局 & 区域 & 门店配置, optionType 不传默认查全部类型
        List<Integer> optionTypeList = Arrays.stream(optionTypeEnums).map(InquiryOptionTypeEnum::getType).toList();
        Map<Integer, List<InquiryOptionConfigDO>> optionTypeMap = inquiryOptionConfigMapper.queryAllByTenantAndOptionType(tenantDto, optionTypeList)
            .stream().collect(Collectors.groupingBy(InquiryOptionConfigDO::getOptionType));

        // 获取优先级最高的配置
        List<InquiryOptionConfigDO> optionList = new ArrayList<>();
        for (Entry<Integer, List<InquiryOptionConfigDO>> entry : optionTypeMap.entrySet()) {
            InquiryOptionTypeEnum optionTypeEnum = InquiryOptionTypeEnum.fromType(entry.getKey());
            if (optionTypeEnum == null) {
                continue;
            }
            // 优先级最高的配置（配置不覆盖传递）
            InquiryOptionConfigDO option = null;
            int priority = Integer.MAX_VALUE;
            for (InquiryOptionConfigDO optionConfigDO : entry.getValue()) {
                int priority1 = optionConfigDO.priority(optionTypeEnum);
                if (priority > priority1) {
                    priority = priority1;
                    option = optionConfigDO;
                }
            }
            if (option != null) {
                optionList.add(option);
            }
        }
        // 转换返回
        return InquiryOptionConfigConvert.INSTANCE.convertDO2GenericOptions(optionList);
    }


    @Override
    public PageResult<TenantRespDto> getTenantPage(TenantReqDto tenantReqDto) {

        PageResult<TenantRespDto> pageResult = tenantApi.pageTenant(tenantReqDto);
        if (CollectionUtils.isEmpty(pageResult.getList())) {
            return pageResult;
        }
        if (tenantReqDto.getOptionType() != null) {
            List<Long> tenantIds = cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertList(pageResult.getList(), TenantRespDto::getId);

            List<Long> existsTenantIds = inquiryOptionConfigMapper.queryList(
                    InquiryOptionConfigQueryDto.builder().targetIds(tenantIds).optionTypeList(Collections.singletonList(tenantReqDto.getOptionType()))
                        .targetType(InquiryTargetTypeEnum.STORE.type).build())
                .stream().map(InquiryOptionConfigDO::getTargetId).filter(Objects::nonNull).toList();

            for (TenantRespDto dto : pageResult.getList()) {
                dto.setExistsOptionType(existsTenantIds.contains(dto.getId()));
            }
        }

        return pageResult;
    }

    @Override
    public InquiryOptionConfigDO getInquiryOptionConfig(Long targetId, InquiryOptionTypeEnum optionTypeEnum) {
        return inquiryOptionConfigMapper.queryOne(InquiryOptionConfigQueryDto.builder().targetId(targetId).optionTypeList(Collections.singletonList(optionTypeEnum.getType())).build());
    }

    @Override
    public InquiryOptionAreaConfigRespDto queryInquiryOptionAreaConfig(InquiryOptionConfigPageReqVO reqVO) {
        reqVO.setTargetType(InquiryTargetTypeEnum.AREA.type);
        List<InquiryOptionConfigDO> list = inquiryOptionConfigMapper.queryList(reqVO);
        if (CollectionUtils.isEmpty(list)) {
            return InquiryOptionAreaConfigRespDto.builder().build();
        }

        List<InquiryOptionAreaConfigRespDto> respDtos = list.stream().map(InquiryOptionConfigConvert.INSTANCE::convertDO2AreaOptions).toList();

        Set<String> productNames = respDtos.stream().flatMap(i -> Stream.of(i.getAreaInquiryProductNames()).filter(CollUtil::isNotEmpty).flatMap(Collection::stream))
            .collect(Collectors.toSet());

        Set<String> doctorPrefs = respDtos.stream().flatMap(i -> Stream.of(i.getAreaInquiryDoctorPrefs()).filter(CollUtil::isNotEmpty).flatMap(Collection::stream))
            .collect(Collectors.toSet());

        InquiryOptionAreaConfigRespDto respDto = InquiryOptionAreaConfigRespDto.builder()
            .areaInquiryProductNames(new ArrayList<>(productNames))
            .areaInquiryDoctorPrefs(new ArrayList<>(doctorPrefs))
            .build();

        if (CollUtil.isNotEmpty(respDto.getAreaInquiryDoctorPrefs())) {
            List<InquiryDoctorDto> doctors = inquiryDoctorApi.getInquiryDoctorByPrefList(respDto.getAreaInquiryDoctorPrefs());
            if (CollUtil.isNotEmpty(doctors)) {
                List<InquiryProductDoctorDto> doctorDtos = doctors.stream().map(p -> InquiryProductDoctorDto.builder().pref(p.getPref()).name(p.getName()).build()).toList();
                respDto.setAreaInquiryDoctorNamePrefs(doctorDtos);
            }
        }
        return respDto;
    }
}