package com.xyy.saas.inquiry.drugstore.server.service.tenant;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.inquiry.drugstore.enums.ErrorCodeConstants.TENANT_PACKAGE_INQUIRY_RECORD_RELATION_NOT_EXISTS;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.yudao.module.system.api.tenant.TenantApi;
import cn.iocoder.yudao.module.system.api.tenant.TenantPackageShareApi;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantPackageShareRelationPageReqDto;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantPackageShareRelationRespDto;
import com.xyy.saas.inquiry.drugstore.server.controller.admin.cost.vo.TenantPackageCostLogReqVO;
import com.xyy.saas.inquiry.drugstore.server.controller.admin.cost.vo.TenantPackageCostLogRespVO;
import com.xyy.saas.inquiry.drugstore.server.controller.admin.cost.vo.TenantPackageCostLogSaveReqVO;
import com.xyy.saas.inquiry.drugstore.server.controller.admin.cost.vo.TenantPackageCostReqVO;
import com.xyy.saas.inquiry.drugstore.server.convert.tennat.TenantPackageCostLogConvert;
import com.xyy.saas.inquiry.drugstore.server.dal.dataobject.tenant.TenantPackageCostDO;
import com.xyy.saas.inquiry.drugstore.server.dal.dataobject.tenant.TenantPackageCostLogDO;
import com.xyy.saas.inquiry.drugstore.server.dal.mysql.tenant.TenantPackageCostLogMapper;
import com.xyy.saas.inquiry.drugstore.server.dal.mysql.tenant.TenantPackageCostMapper;
import com.xyy.saas.inquiry.enums.system.BizTypeEnum;
import com.xyy.saas.inquiry.enums.tenant.CostRecordTypeEnum;
import com.xyy.saas.inquiry.pojo.TenantDto;
import jakarta.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

/**
 * 门店问诊套餐操作记录 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class TenantPackageCostLogServiceImpl implements TenantPackageCostLogService {

    @Resource
    private TenantPackageCostLogMapper tenantPackageCostLogMapper;

    @Resource
    private TenantPackageCostMapper tenantPackageCostMapper;

    @Autowired
    private TenantApi tenantApi;

    @Autowired
    private TenantPackageShareApi tenantPackageShareApi;


    @Override
    public Long createTenantPackageCostLog(TenantPackageCostLogSaveReqVO createReqVO) {
        // 插入
        TenantPackageCostLogDO tenantPackageCostLog = BeanUtils.toBean(createReqVO, TenantPackageCostLogDO.class);
        tenantPackageCostLogMapper.insert(tenantPackageCostLog);
        // 返回
        return tenantPackageCostLog.getId();
    }

    @Override
    public void updateTenantPackageCostLog(TenantPackageCostLogSaveReqVO updateReqVO) {
        // 校验存在
        validateTenantPackageCostLogExists(updateReqVO.getId());
        // 更新
        TenantPackageCostLogDO updateObj = BeanUtils.toBean(updateReqVO, TenantPackageCostLogDO.class);
        tenantPackageCostLogMapper.updateById(updateObj);
    }

    @Override
    public void deleteTenantPackageCostLog(Long id) {
        // 校验存在
        validateTenantPackageCostLogExists(id);
        // 删除
        tenantPackageCostLogMapper.deleteById(id);
    }

    private void validateTenantPackageCostLogExists(Long id) {
        if (tenantPackageCostLogMapper.selectById(id) == null) {
            throw exception(TENANT_PACKAGE_INQUIRY_RECORD_RELATION_NOT_EXISTS);
        }
    }

    @Override
    public TenantPackageCostLogDO getTenantPackageCostLog(Long id) {
        return tenantPackageCostLogMapper.selectById(id);
    }

    @Override
    @TenantIgnore
    public PageResult<TenantPackageCostLogRespVO> getTenantPackageCostLogPage(TenantPackageCostLogReqVO pageReqVO) {
        // 为空只查问诊业务相关类型
        if (pageReqVO.getRecordType() == null) {
            pageReqVO.setRecordTypes(List.of(CostRecordTypeEnum.INQUIRY.getCode(), CostRecordTypeEnum.INQUIRY_CANAL.getCode()));
        }

        List<Long> tenantIdsByHeadId = tenantApi.getTenantIdsByHeadId();
        if (CollUtil.isEmpty(tenantIdsByHeadId)) {
            return new PageResult<>();
        }

        // 根据套餐查costId
        List<TenantPackageCostDO> packageCostDOS = tenantPackageCostMapper.queryTenantPackageCostByCondition(TenantPackageCostReqVO.builder().tenantPackageId(pageReqVO.getId()).build());

        if (CollUtil.isEmpty(packageCostDOS)) {
            return new PageResult<>();
        }
        pageReqVO.setCostIds(packageCostDOS.stream().map(TenantPackageCostDO::getId).distinct().toList());
        pageReqVO.setTenantIds(tenantIdsByHeadId);

        PageResult<TenantPackageCostLogDO> pageResult = tenantPackageCostLogMapper.selectPage(pageReqVO);
        if (CollUtil.isEmpty(pageResult.getList())) {
            return new PageResult<>();
        }
        List<Long> tenantIds = pageResult.getList().stream().map(TenantPackageCostLogDO::getTenantId).distinct().toList();

        Map<Long, TenantDto> tenantDtoMap = tenantApi.getTenantListMap(tenantIds);

        List<TenantPackageCostLogRespVO> list = pageResult.getList().stream().map(c -> {
            TenantPackageCostLogRespVO tenantPackageCostLogRespVO = TenantPackageCostLogConvert.INSTANCE.convert(c);
            TenantDto tenantDto = tenantDtoMap.get(tenantPackageCostLogRespVO.getTenantId());
            tenantPackageCostLogRespVO.setTenantName(tenantDto.getName());
            tenantPackageCostLogRespVO.setTenantPref(tenantDto.getPref());
            return tenantPackageCostLogRespVO;
        }).toList();

        return new PageResult<>(list, pageResult.getTotal());
    }

    @Override
    public boolean batchCreateTenantPackageCostLog(List<TenantPackageCostLogDO> recordRelationDOS) {
        return tenantPackageCostLogMapper.insertBatch(recordRelationDOS);
    }

    @Override
    public PageResult<TenantPackageCostLogRespVO> getTenantPackageCostLogSharePage(TenantPackageShareRelationPageReqDto relatePageReqDto) {

        PageResult<TenantPackageShareRelationRespDto> pageResult = tenantPackageShareApi.pageTenantPackageShareRelation(relatePageReqDto);

        if (CollUtil.isEmpty(pageResult.getList())) {
            return new PageResult<>();
        }

        // 根据套餐查costId
        List<TenantPackageCostDO> packageCostDOS = tenantPackageCostMapper.queryTenantPackageCostByCondition(TenantPackageCostReqVO.builder().tenantPackageId(relatePageReqDto.getTenantPackageId()).build());
        if (CollUtil.isEmpty(packageCostDOS)) {
            return new PageResult<>();
        }
        List<Long> costIds = packageCostDOS.stream().map(TenantPackageCostDO::getId).distinct().toList();

        List<TenantPackageCostLogRespVO> list = pageResult.getList().stream().map(shareRelationDto -> {
            TenantPackageCostLogRespVO vo = TenantPackageCostLogConvert.INSTANCE.convertVo2RespVO(shareRelationDto);
            // 查询门店当前套餐问诊数量计数
            Long costCount = tenantPackageCostLogMapper.countInquiryByTenantCostId(shareRelationDto.getTenantId(), costIds, BizTypeEnum.HYWZ.getCode());
            vo.setCostCount(Math.max(Optional.ofNullable(costCount).orElse(0L), 0));
            return vo;
        }).toList();

        return new PageResult<>(list, pageResult.getTotal());
    }
}