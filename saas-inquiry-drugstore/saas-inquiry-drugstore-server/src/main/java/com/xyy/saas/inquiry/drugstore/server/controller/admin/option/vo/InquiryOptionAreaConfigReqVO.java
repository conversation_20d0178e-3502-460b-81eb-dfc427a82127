package com.xyy.saas.inquiry.drugstore.server.controller.admin.option.vo;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.inquiry.drugstore.enums.ErrorCodeConstants.OPTION_AREA_PRODUCT_NUM_LIMIT_FAIL;

import cn.hutool.core.collection.CollUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.Data;

@Schema(description = "管理后台 - 区域-问诊配置选项新增/修改 Request VO")
@Data
public class InquiryOptionAreaConfigReqVO implements Serializable {

    /**
     * 选项类型 {@link com.xyy.saas.inquiry.drugstore.enums.InquiryOptionTypeEnum}
     */
    @Schema(description = "选项类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "选项类型")
    private Integer optionType;

    @Schema(description = "地区编码", example = "420100")
    private Integer area;

    @Schema(description = "地区编码集合", example = "420100")
    private List<Long> areas;


    // 问诊单配置-抗菌药品配置
    @Schema(description = "抗菌药品配置-分级目录id", example = "1")
    private Long formAntimicrobialDrugCatalogId;


    // 问诊流程配置-处方类型填写开关
    @Schema(description = "问诊流程配置-处方类型填写开关-是否必填", example = "true")
    private Boolean procPrescriptionTypeRequired;


    // 问诊流程配置-家庭住址填写开关
    @Schema(description = "问诊流程配置-家庭住址填写开关-是否必填", example = "true")
    private Boolean procHomeAddressRequired;


    // 问诊流程配置-监护人填写开关
    @Schema(description = "问诊流程配置-监护人填写开关-是否必填", example = "true")
    private Boolean procGuardianRequired;

    @Schema(description = "监护人填写年龄最低限度,如果<=6岁 则页面需要填写监护人信息", example = "6")
    private Integer procGuardianAge;

    // 监护人填写年龄扩展
    public Map<String, Object> procGuardianAgeExt() {
        return procGuardianAge == null ? null : new HashMap<>() {{
            put("procGuardianAge", procGuardianAge);
        }};
    }


    // 问诊流程配置-问诊合规配置(地区规则)
    @Schema(description = "问诊合规配置", example = "true")
    private Boolean procInquiryCompliance;

    @Schema(description = "问诊合规配置-患者年龄限制-大于等于", example = "15")
    @Max(value = 120, message = "请输入年龄")
    @Min(value = 0, message = "请输入年龄")
    private Integer procInquiryComplianceForPatientAgeGe;

    @Schema(description = "问诊合规配置-患者年龄限制-且小于", example = "60")
    @Max(value = 120, message = "请输入年龄")
    @Min(value = 0, message = "请输入年龄")
    private Integer procInquiryComplianceForPatientAgeLt;

    @Schema(description = "问诊合规配置-妊娠哺乳是否可发起问诊", example = "true")
    private Boolean procInquiryComplianceAllowForPregnancyLactation;

    // 问诊合规配置扩展
    public Map<String, Object> procInquiryComplianceExt() {
        return procInquiryCompliance == null ? null : new HashMap<>() {{
            put("procInquiryComplianceForPatientAgeGe", procInquiryComplianceForPatientAgeGe);
            put("procInquiryComplianceForPatientAgeLt", procInquiryComplianceForPatientAgeLt);
            put("procInquiryComplianceAllowForPregnancyLactation", procInquiryComplianceAllowForPregnancyLactation);
        }};
    }


    // 问诊流程配置-录屏问诊配置
    @Schema(description = "录屏问诊配置", example = "true")
    private Boolean procVideoInquiry;
    @Schema(description = "录屏问诊配置-比例", example = "50")
    @Max(value = 100, message = "录屏问诊配置-比例不能超过100")
    @Min(value = 0, message = "录屏问诊配置-比例不能小于0")
    private Integer procVideoInquiryRatio;

    // 录屏问诊配置扩展
    public Map<String, Object> procVideoInquiryExt() {
        return procVideoInquiry == null ? null : new HashMap<>() {{
            put("procVideoInquiryRatio", procVideoInquiryRatio);
        }};
    }


    // 问诊流程配置-医生接诊默认页面
    @Schema(description = "医生接诊默认页面", requiredMode = Schema.RequiredMode.REQUIRED, example = "true")
    // @NotNull(message = "医生接诊默认页面不能为空")
    private String procDoctorAdmissionDefaultPage;

    @Schema(description = "条件值", requiredMode = Schema.RequiredMode.REQUIRED, example = "true")
    private String optionValue;


    @Schema(description = "医院编码 - 全真人问诊时使用")
    private List<String> presAllRealPeopleInquiryHospitalPrefs;

    // 医院编码 - 全真人问诊
    public Map<String, Object> presAllRealPeopleInquiryExt() {
        return presAllRealPeopleInquiryHospitalPrefs == null ? null : new HashMap<>() {{
            put("presAllRealPeopleInquiryHospitalPrefs", presAllRealPeopleInquiryHospitalPrefs);
        }};
    }


    @Schema(description = "医院编码 - 地区控制")
    private List<String> areaInquiryHospitalPrefs;

    // 医院编码 - 地区控制
    public Map<String, Object> areaInquiryHospitalPrefExt() {
        return areaInquiryHospitalPrefs == null ? null : new HashMap<>() {{
            put("areaInquiryHospitalPrefs", areaInquiryHospitalPrefs);
        }};
    }

    /**
     * 药品指定医生
     */
    @Schema(description = "商品名称 - 地区控制")
    private List<String> areaInquiryProductNames;

    @Schema(description = " 医生编码 - 地区控制")
    private List<String> areaInquiryDoctorPrefs;

    // 药品指定医生 - 地区控制
    public Map<String, Object> areaInquiryProductDoctorExt() {
        return areaInquiryProductNames == null && areaInquiryDoctorPrefs == null ? null : new HashMap<>() {{
            if (CollUtil.size(areaInquiryProductNames) > 1000) {
                throw exception(OPTION_AREA_PRODUCT_NUM_LIMIT_FAIL);
            }
            put("areaInquiryProductNames", areaInquiryProductNames);
            put("areaInquiryDoctorPrefs", areaInquiryDoctorPrefs);
        }};
    }
}