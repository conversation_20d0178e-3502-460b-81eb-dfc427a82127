package com.xyy.saas.inquiry.drugstore.server.controller.admin.cost.vo;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import com.xyy.saas.inquiry.enums.inquiry.InquiryWayTypeEnum;
import com.xyy.saas.inquiry.enums.system.BizTypeEnum;
import com.xyy.saas.inquiry.enums.tenant.TenantPackageRelationStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

@Schema(description = "门店问诊套餐额度分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Accessors(chain = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TenantPackageCostReqVO extends PageParam {


    @Schema(description = "门店id", example = "29653")
    private Long tenantId;

    @Schema(description = "门店ids", example = "29653")
    private Set<Long> tenantIds;
    /**
     * {@link BizTypeEnum}
     */
    @Schema(description = "系统业务类型", example = "0")
    private Integer bizType;

    @Schema(description = "当前在用的套餐订单表id", example = "29653")
    private Long tenantPackageId;

    @Schema(description = "当前在用的套餐订单表ids", example = "29653")
    private List<Long> tenantPackageIds;

    @Schema(description = "问诊业务类型", example = "1")
    private Integer inquiryBizType;

    @Schema(description = "问诊审方类型", example = "1")
    private Integer inquiryAuditType;

    @Schema(description = "问诊额度 -1不限")
    private Long cost;

    @Schema(description = "问诊剩余额度 -1不限")
    private Long surplusCost;

    @Schema(description = "套餐开始时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] startTime;

    @Schema(description = "套餐结束时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] endTime;


    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    /**
     * {@link InquiryWayTypeEnum}
     */
    @Schema(description = "问诊形式 1图文 2视频", example = "1")
    private Integer inquiryWayType;

    @Schema(description = "问诊形式 1图文 2视频", example = "1")
    private List<Integer> inquiryWayTypes;

    /**
     * {@link TenantPackageRelationStatusEnum}
     */
    @Schema(description = "订单状态", example = "2")
    private Integer status;

    @Schema(description = "订单状态", example = "2")
    private List<Integer> statusList;

    /**
     * 套餐是否在服务时间
     */
    @Schema(description = "套餐是否在服务时间", example = "true")
    private boolean inServerTime;


    private LocalDateTime geEndTime;

    /**
     * 处方类型
     */
    private Integer prescriptionType;
}