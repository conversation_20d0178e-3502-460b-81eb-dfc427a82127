package com.xyy.saas.inquiry.drugstore.server.controller.admin.cost.vo;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import com.xyy.saas.inquiry.enums.system.BizTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

@Schema(description = "管理后台 - 门店问诊套餐操作记录分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TenantPackageCostLogReqVO extends PageParam {

    @Schema(description = "套餐订单id", example = "29653")
    @NotNull(message = "套餐订单id不能为空")
    private Long id;

    @Schema(description = "门店id", example = "29653")
    private Long tenantId;

    private List<Long> tenantIds;

    /**
     * {@link BizTypeEnum}
     */
    @Schema(description = "系统业务类型", example = "0")
    private Integer bizType = BizTypeEnum.HYWZ.getCode();

    @Schema(description = "使其变化的套餐额度id", example = "4602")
    private Long costId;

    private List<Long> costIds;

    @Schema(description = "问诊id", example = "25124")
    private String bizId;

    @Schema(description = "变更额度 (扣减会存负数)")
    private Long changeCost;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "类型 1图文 2视频", example = "2")
    private Integer wayType;

    @Schema(description = "记录类型 0初始创建 1问诊 2退款 3作废", example = "2")
    private Integer recordType;

    @Schema(description = "记录类型 0初始创建 1问诊 2退款 3作废", example = "2")
    private List<Integer> recordTypes;

}