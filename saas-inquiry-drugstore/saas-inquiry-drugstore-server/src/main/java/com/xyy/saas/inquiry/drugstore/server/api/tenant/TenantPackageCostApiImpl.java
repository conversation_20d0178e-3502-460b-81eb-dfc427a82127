package com.xyy.saas.inquiry.drugstore.server.api.tenant;

import cn.hutool.core.collection.CollUtil;
import com.xyy.saas.inquiry.drugstore.api.tenant.TenantPackageCostApi;
import com.xyy.saas.inquiry.drugstore.api.tenant.dto.TenantDeductCostDto;
import com.xyy.saas.inquiry.drugstore.api.tenant.dto.TenantPackageCostDto;
import com.xyy.saas.inquiry.drugstore.server.controller.admin.cost.vo.TenantPackageCostReqVO;
import com.xyy.saas.inquiry.drugstore.server.dal.dataobject.tenant.TenantPackageCostDO;
import com.xyy.saas.inquiry.drugstore.server.service.tenant.DrugStoreBaseInfoService;
import com.xyy.saas.inquiry.drugstore.server.service.tenant.TenantPackageCostService;
import com.xyy.saas.inquiry.enums.inquiry.InquiryAuditTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.InquiryBizTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.InquiryWayTypeEnum;
import com.xyy.saas.inquiry.enums.system.BizTypeEnum;
import com.xyy.saas.inquiry.enums.tenant.CostRecordTypeEnum;
import com.xyy.saas.inquiry.enums.tenant.TenantPackageEffectiveStatusEnum;
import com.xyy.saas.inquiry.enums.tenant.TenantPackageRelationStatusEnum;
import com.xyy.saas.inquiry.mq.tenant.cost.TenantChangeCostDto;
import jakarta.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import org.apache.dubbo.config.annotation.DubboService;

/**
 * 门店套餐包额度apiImpl
 *
 * @Author:chenxiaoyi
 * @Date:2024/09/05 13:54
 */
@DubboService
public class TenantPackageCostApiImpl implements TenantPackageCostApi {

    @Resource
    private TenantPackageCostService tenantPackageCostService;

    @Resource
    private DrugStoreBaseInfoService drugStoreBaseInfoService;

    @Override
    public void initTenantPackageCost(TenantPackageCostDto tenantPackageCostDto) {
        tenantPackageCostService.saveTenantPackageCost(tenantPackageCostDto);
    }

    @Override
    public void isValidTenantPackage(BizTypeEnum bizTypeEnum, InquiryWayTypeEnum inquiryWayTypeEnum, InquiryBizTypeEnum inquiryBizTypeEnum, Integer prescriptionType) {
        tenantPackageCostService.isValidTenantPackage(bizTypeEnum, inquiryWayTypeEnum, inquiryBizTypeEnum, prescriptionType);
    }

    @Override
    public TenantDeductCostDto deductTenantCost(InquiryWayTypeEnum wayTypeEnum, InquiryBizTypeEnum inquiryBizTypeEnum, String bizId, Integer prescriptionType, CostRecordTypeEnum costRecordTypeEnum) {
        return tenantPackageCostService.deductTenantCost(wayTypeEnum, inquiryBizTypeEnum, bizId, prescriptionType, costRecordTypeEnum);
    }

    @Override
    public void reBackTenantCost(TenantChangeCostDto changeCostDto) {
        tenantPackageCostService.reBackTenantCost(changeCostDto);
    }

    @Override
    public boolean validPlatformReviewCost() {
        return tenantPackageCostService.validPlatformReviewCost();
    }

    @Override
    public boolean validRemoteAuditCost(InquiryAuditTypeEnum auditTypeEnum, InquiryWayTypeEnum inquiryWayTypeEnum) {

        TenantPackageCostReqVO reqVO = TenantPackageCostReqVO.builder()
            .bizType(BizTypeEnum.HYWZ.getCode())
            .inquiryWayType(inquiryWayTypeEnum == null ? null : inquiryWayTypeEnum.getCode())
            .inquiryBizType(InquiryBizTypeEnum.REMOTE_INQUIRY.getCode())
            .status(TenantPackageRelationStatusEnum.NORMAL.getCode())
            .inServerTime(true).build();

        List<TenantPackageCostDO> packageCostDOS = tenantPackageCostService.availableInquiryCosts(reqVO);
        return CollUtil.isNotEmpty(packageCostDOS)
            && packageCostDOS.stream().anyMatch(p -> auditTypeEnum == null || Objects.equals(p.getInquiryAuditType(), auditTypeEnum.getCode()));
    }

    @Override
    public boolean validStoreRemoteAuditCost() {
        List<TenantPackageCostDO> packageCostDOS = tenantPackageCostService.availableInquiryCosts(BizTypeEnum.HYWZ, InquiryBizTypeEnum.REMOTE_INQUIRY);
        return CollUtil.isNotEmpty(packageCostDOS)
            && packageCostDOS.stream().anyMatch(p -> InquiryAuditTypeEnum.isStoreInquiryAuditType(p.getInquiryAuditType()));
    }

    @Override
    public TenantPackageCostDto getTenantPackageCostById(Long costId) {
        return tenantPackageCostService.getTenantPackageCostById(costId);
    }

    @Override
    public TenantPackageCostDto getTenantPackageCostByLogBizId(String bizId, CostRecordTypeEnum costRecordTypeEnum) {
        return tenantPackageCostService.getTenantPackageCostByLogBizId(bizId, costRecordTypeEnum);
    }

    @Override
    public Map<String, TenantPackageCostDto> getTenantPackageCostByLogBizIds(List<String> bizIds, CostRecordTypeEnum costRecordTypeEnum) {
        return tenantPackageCostService.getTenantPackageCostByLogBizIds(bizIds, costRecordTypeEnum);
    }


    @Override
    public List<TenantPackageCostDto> selectUserTenantPackages(Long userId, List<TenantPackageEffectiveStatusEnum> effectiveStatusEnums) {
        return drugStoreBaseInfoService.selectUserTenantPackages(userId, effectiveStatusEnums);
    }
}
