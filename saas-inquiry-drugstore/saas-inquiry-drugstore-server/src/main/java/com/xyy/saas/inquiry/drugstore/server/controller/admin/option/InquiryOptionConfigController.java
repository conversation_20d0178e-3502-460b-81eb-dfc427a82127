package com.xyy.saas.inquiry.drugstore.server.controller.admin.option;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantReqDto;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantRespDto;
import com.xyy.saas.inquiry.drugstore.api.option.dto.InquiryOptionAreaConfigRespDto;
import com.xyy.saas.inquiry.drugstore.api.option.dto.InquiryOptionConfigQueryDto;
import com.xyy.saas.inquiry.drugstore.api.option.dto.InquiryOptionGlobalConfigRespDto;
import com.xyy.saas.inquiry.drugstore.api.option.dto.InquiryOptionStoreConfigRespDto;
import com.xyy.saas.inquiry.drugstore.server.controller.admin.option.vo.InquiryOptionAreaConfigDelVO;
import com.xyy.saas.inquiry.drugstore.server.controller.admin.option.vo.InquiryOptionAreaConfigReqVO;
import com.xyy.saas.inquiry.drugstore.server.controller.admin.option.vo.InquiryOptionConfigPageReqVO;
import com.xyy.saas.inquiry.drugstore.server.controller.admin.option.vo.InquiryOptionConfigQueryReqVO;
import com.xyy.saas.inquiry.drugstore.server.controller.admin.option.vo.InquiryOptionGlobalConfigReqVO;
import com.xyy.saas.inquiry.drugstore.server.controller.admin.option.vo.InquiryOptionStoreConfigReqVO;
import com.xyy.saas.inquiry.drugstore.server.service.option.InquiryOptionConfigService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


@Tag(name = "管理后台 - 问诊配置选项")
@RestController
@RequestMapping("/drugstore/inquiry-option-config")
@Validated
public class InquiryOptionConfigController {

    @Resource
    private InquiryOptionConfigService inquiryOptionConfigService;

    // 全局
    @PostMapping("/global/save")
    @Operation(summary = "全局-保存问诊配置选项")
    @PreAuthorize("@ss.hasPermission('drugstore:inquiry-option-config:save')")
    public CommonResult<Integer> saveInquiryOptionGlobalConfig(@Valid @RequestBody InquiryOptionGlobalConfigReqVO reqVO) {
        return success(inquiryOptionConfigService.saveInquiryOptionGlobalConfig(reqVO));
    }

    @GetMapping("/global/query")
    @Operation(summary = "全局-保存问诊配置选项")
    @PreAuthorize("@ss.hasPermission('drugstore:inquiry-option-config:query')")
    public CommonResult<InquiryOptionGlobalConfigRespDto> getInquiryOptionGlobalConfig(@Valid InquiryOptionConfigQueryReqVO reqVO) {
        InquiryOptionConfigQueryDto dto = BeanUtils.toBean(reqVO, InquiryOptionConfigQueryDto.class);
        return success(inquiryOptionConfigService.getInquiryOptionGlobalConfig(dto));
    }


    // 区域
    @PostMapping("/area/save")
    @Operation(summary = "区域-保存问诊配置选项")
    @PreAuthorize("@ss.hasPermission('drugstore:inquiry-option-config:save')")
    public CommonResult<Integer> saveInquiryOptionAreaConfig(@Valid @RequestBody InquiryOptionAreaConfigReqVO reqVO) {
        return success(inquiryOptionConfigService.saveInquiryOptionAreaConfig(reqVO));
    }

    @GetMapping("/area/page")
    @Operation(summary = "区域-查询问诊配置选项（分页）")
    @PreAuthorize("@ss.hasPermission('drugstore:inquiry-option-config:query')")
    public CommonResult<PageResult<InquiryOptionAreaConfigRespDto>> getInquiryOptionAreaConfigPage(@Valid InquiryOptionConfigPageReqVO reqVO) {
        return success(inquiryOptionConfigService.getInquiryOptionAreaConfigPage(reqVO));
    }

    @GetMapping("/area/all-area")
    @Operation(summary = "区域-查询问诊地区所有配置选项")
    @PreAuthorize("@ss.hasPermission('drugstore:inquiry-option-config:query')")
    public CommonResult<List<InquiryOptionAreaConfigRespDto>> getInquiryOptionAreaAllConfig(InquiryOptionConfigPageReqVO reqVO) {
        return success(inquiryOptionConfigService.getInquiryOptionAreaAllConfig(reqVO));
    }

    @PostMapping("/area/query")
    @Operation(summary = "区域-获得问诊配置选项-合并ext")
    @PreAuthorize("@ss.hasPermission('drugstore:inquiry-option-config:query')")
    public CommonResult<InquiryOptionAreaConfigRespDto> queryInquiryOptionAreaConfig(@RequestBody InquiryOptionConfigPageReqVO reqVO) {
        return success(inquiryOptionConfigService.queryInquiryOptionAreaConfig(reqVO));
    }

    @GetMapping("/area/get")
    @Operation(summary = "区域-获得问诊配置选项")
    @Parameter(name = "id", description = "编号", required = true, example = "1")
    @PreAuthorize("@ss.hasPermission('drugstore:inquiry-option-config:query')")
    public CommonResult<InquiryOptionAreaConfigRespDto> getInquiryOptionAreaConfig(@RequestParam("id") Long id) {
        return success(inquiryOptionConfigService.getInquiryOptionAreaConfig(id));
    }

    @PostMapping("/area/delete")
    @Operation(summary = "区域-删除问诊配置选项（批量）")
    @PreAuthorize("@ss.hasPermission('drugstore:inquiry-option-config:delete')")
    public CommonResult<Boolean> deleteInquiryOptionAreaConfig(@Valid @RequestBody InquiryOptionAreaConfigDelVO delVO) {
        return success(inquiryOptionConfigService.batchDeleteInquiryOptionConfig(delVO.getIds()) > 0);
    }


    // 门店
    @PostMapping("/store/save")
    @Operation(summary = "门店-保存问诊配置选项")
    @PreAuthorize("@ss.hasPermission('drugstore:inquiry-option-config:save')")
    public CommonResult<Integer> saveInquiryOptionStoreConfig(@Valid @RequestBody InquiryOptionStoreConfigReqVO reqVO) {
        return success(inquiryOptionConfigService.saveInquiryOptionStoreConfig(reqVO));
    }

    @GetMapping("/store/page")
    @Operation(summary = "门店-查询问诊配置选项（分页）")
    @PreAuthorize("@ss.hasPermission('drugstore:inquiry-option-config:query')")
    public CommonResult<PageResult<InquiryOptionStoreConfigRespDto>> getInquiryOptionStoreConfigPage(@Valid InquiryOptionConfigPageReqVO reqVO) {
        return success(inquiryOptionConfigService.getInquiryOptionStoreConfigPage(reqVO));
    }

    @GetMapping("/store/get")
    @Operation(summary = "门店-获得问诊配置选项")
    @Parameter(name = "id", description = "编号", required = true, example = "1")
    @PreAuthorize("@ss.hasPermission('drugstore:inquiry-option-config:query')")
    public CommonResult<InquiryOptionStoreConfigRespDto> getInquiryOptionStoreConfig(@RequestParam("id") Long id) {
        return success(inquiryOptionConfigService.getInquiryOptionStoreConfig(id));
    }

    @DeleteMapping("/store/delete")
    @Operation(summary = "门店-删除问诊配置选项")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('drugstore:inquiry-option-config:delete')")
    public CommonResult<Boolean> deleteInquiryOptionStoreConfig(@RequestParam("id") Long id) {
        return success(inquiryOptionConfigService.deleteInquiryOptionConfig(id) > 0);
    }


    @GetMapping("/page-tenant")
    @Operation(summary = "获得门店分页")
    @PreAuthorize("@ss.hasPermission('drugstore:inquiry-option-config:query')")
    public CommonResult<PageResult<TenantRespDto>> getTenantPage(@Valid TenantReqDto tenantReqDto) {
        PageResult<TenantRespDto> pageResult = inquiryOptionConfigService.getTenantPage(tenantReqDto);
        return success(pageResult);
    }


}