<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xmlns="http://maven.apache.org/POM/4.0.0"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.xyy.saas</groupId>
  <artifactId>saas-inquiry-system</artifactId>
  <version>${revision}</version>
  <packaging>pom</packaging>
  <modules>
    <module>saas-inquiry-user</module>
    <module>saas-inquiry-product</module>
    <module>saas-inquiry-pojo</module>
    <module>saas-transmitter</module>
  </modules>

  <name>${project.artifactId}</name>
  <description>SaaS 问诊系统-基础模块</description>
  <properties>
    <revision>1.0.0-SNAPSHOT</revision>
    <yudao.version>2.3.0-SNAPSHOT</yudao.version>
    <xyy.soa.version>1.0-SNAPSHOT</xyy.soa.version>
    <!-- Maven 相关 -->
    <java.version>21</java.version>
    <maven.compiler.source>${java.version}</maven.compiler.source>
    <maven.compiler.target>${java.version}</maven.compiler.target>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <!--构建时跳过测试的编译和执行-->
    <maven.test.skip>true</maven.test.skip>
    <maven-surefire-plugin.version>2.22.2</maven-surefire-plugin.version>
    <maven-compiler-plugin.version>3.8.1</maven-compiler-plugin.version>
    <maven-springboot-plugin.version>3.3.4</maven-springboot-plugin.version>
    <flatten-maven-plugin.version>1.4.1</flatten-maven-plugin.version>
    <lombok.version>1.18.34</lombok.version>
    <spring.boot.version>3.3.4</spring.boot.version>
    <mapstruct.version>1.5.5.Final</mapstruct.version>

    <!-- 代码扫描 -->
    <sonar.projectKey>saas_saas-inquiry-system_ec2aa2be-ea3a-4140-8f89-998a56021476</sonar.projectKey>
    <sonar.projectName>saas-inquiry-system</sonar.projectName>
    <sonar.qualitygate.wait>true</sonar.qualitygate.wait>
    <sonar.java.binaries>${project.build.directory}/classes</sonar.java.binaries>
    <sonar.java.test.binaries>${project.build.directory}/test-classes</sonar.java.test.binaries>

    <!-- SonarQube 配置，确保可以读取 Jacoco 测试覆盖率 -->
    <sonar.language>java</sonar.language>
    <sonar.java.coveragePlugin>jacoco</sonar.java.coveragePlugin>
    <sonar.coverage.jacoco.xmlReportPaths>**target/site/jacoco/jacoco.xml</sonar.coverage.jacoco.xmlReportPaths>
    <jacoco.version>0.8.11</jacoco.version>
    <!-- 百度api-sdk -->
    <baidu.api.version>4.16.22</baidu.api.version>
  </properties>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>cn.iocoder.boot</groupId>
        <artifactId>yudao-dependencies</artifactId>
        <version>${yudao.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>cn.iocoder.boot</groupId>
        <artifactId>yudao-framework</artifactId>
        <version>${yudao.version}</version>
      </dependency>
      <dependency>
        <groupId>com.xyy.saas</groupId>
        <artifactId>biz-soa-starter</artifactId>
        <version>${xyy.soa.version}</version>
      </dependency>

      <dependency>
        <groupId>cn.iocoder.boot</groupId>
        <artifactId>yudao-module-bpm-biz</artifactId>
        <version>${yudao.version}</version>
      </dependency>

      <!-- 中台标准库api（除了 common 依赖，其他全部排除， 并且common依赖中传递的依赖也全部排除） -->
      <!-- <dependency> -->
      <!--   <groupId>com.xyy.me.product.saas</groupId> -->
      <!--   <artifactId>xyy-me-product-saas-api</artifactId> -->
      <!--   <version>1.0-SNAPSHOT</version> -->
      <!--   <exclusions> -->
      <!--     <exclusion> -->
      <!--       <artifactId>*</artifactId> -->
      <!--       <groupId>*</groupId> -->
      <!--     </exclusion> -->
      <!--   </exclusions> -->
      <!-- </dependency> -->
      <!-- <dependency> -->
      <!--   <groupId>com.xyy.me.product.general</groupId> -->
      <!--   <artifactId>xyy-me-product-general-api</artifactId> -->
      <!--   <version>1.0-SNAPSHOT</version> -->
      <!--   <exclusions> -->
      <!--     <exclusion> -->
      <!--       <artifactId>*</artifactId> -->
      <!--       <groupId>*</groupId> -->
      <!--     </exclusion> -->
      <!--   </exclusions> -->
      <!-- </dependency> -->
      <!-- <dependency> -->
      <!--   <groupId>com.xyy.me.product</groupId> -->
      <!--   <artifactId>common</artifactId> -->
      <!--   <version>1.0-SNAPSHOT</version> -->
      <!--   <exclusions> -->
      <!--     <exclusion> -->
      <!--       <artifactId>*</artifactId> -->
      <!--       <groupId>*</groupId> -->
      <!--     </exclusion> -->
      <!--   </exclusions> -->
      <!-- </dependency> -->


    </dependencies>
  </dependencyManagement>

  <build>
    <pluginManagement>
      <plugins>
        <!-- maven-surefire-plugin 插件，用于运行单元测试。 -->
        <!-- 注意，需要使用 3.0.X+，因为要支持 Junit 5 版本 -->
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-surefire-plugin</artifactId>
          <version>${maven-surefire-plugin.version}</version>
          <configuration>
            <skipTests>false</skipTests>
            <testFailureIgnore>true</testFailureIgnore>
            <argLine>${jacocoArgLine}</argLine>
          </configuration>
        </plugin>
        <!-- maven-compiler-plugin 插件，解决 spring-boot-configuration-processor + Lombok + MapStruct 组合 -->
        <!-- https://stackoverflow.com/questions/33483697/re-run-spring-boot-configuration-annotation-processor-to-update-generated-metada -->
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-compiler-plugin</artifactId>
          <version>${maven-compiler-plugin.version}</version>
          <configuration>
            <annotationProcessorPaths>
              <path>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-configuration-processor</artifactId>
                <version>${spring.boot.version}</version>
              </path>
              <path>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
              </path>
              <path>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${mapstruct.version}</version>
              </path>
            </annotationProcessorPaths>
            <!-- 编译参数写在 arg 内，解决 Spring Boot 3.2 的 Parameter Name Discovery 问题 -->
            <debug>false</debug>
            <compilerArgs>
              <arg>-parameters</arg>
            </compilerArgs>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>flatten-maven-plugin</artifactId>
        </plugin>
      </plugins>
    </pluginManagement>

    <plugins>
      <!-- 统一 revision 版本  https://blog.csdn.net/weixin_43762091/article/details/137960139 -->
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>flatten-maven-plugin</artifactId>
        <version>${flatten-maven-plugin.version}</version>
        <configuration>
          <flattenMode>resolveCiFriendliesOnly</flattenMode>
          <updatePomFile>true</updatePomFile>
        </configuration>
        <executions>
          <execution>
            <goals>
              <goal>flatten</goal>
            </goals>
            <id>flatten</id>
            <phase>process-resources</phase>
          </execution>
          <execution>
            <goals>
              <goal>clean</goal>
            </goals>
            <id>flatten.clean</id>
            <phase>clean</phase>
          </execution>
        </executions>
      </plugin>


    </plugins>
  </build>

  <!-- 控制项目的发布和部署 指定项目的构建产物（如JAR文件、WAR文件等）应该被部署到哪个远程仓库 -->
  <profiles>
    <profile>
      <!--生产环境deploy url-->
      <id>prod</id>
      <distributionManagement>
        <repository>
          <id>maven-releases</id>
          <name>maven-releases</name>
          <url>https://maven.int.ybm100.com/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
          <id>maven-snapshots</id>
          <name>maven-snapshots</name>
          <url>https://maven.int.ybm100.com/repository/maven-snapshots/</url>
        </snapshotRepository>
      </distributionManagement>
    </profile>
    <profile>
      <!--测试环境deploy url-->
      <id>test</id>
      <distributionManagement>
        <repository>
          <id>maven-releases</id>
          <name>maven-releases</name>
          <url>https://mvn.int.ybm100.com/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
          <id>maven-snapshots</id>
          <name>maven-snapshots</name>
          <url>https://mvn.int.ybm100.com/repository/maven-snapshots/</url>
        </snapshotRepository>
      </distributionManagement>
    </profile>

    <profile>
      <id>jacoco</id>
      <build>
        <plugins>
          <!-- jacoco 代码测试覆盖率 -->
          <plugin>
            <groupId>org.jacoco</groupId>
            <artifactId>jacoco-maven-plugin</artifactId>
            <version>${jacoco.version}</version>
            <executions>
              <execution>
                <id>prepare-agent</id>
                <goals>
                  <goal>prepare-agent</goal>
                </goals>
                <configuration>
                  <propertyName>jacocoArgLine</propertyName>
                </configuration>
              </execution>
              <execution>
                <id>report</id>
                <goals>
                  <goal>report</goal>
                </goals>
                <configuration>
                  <formats>
                    <format>XML</format>
                  </formats>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>

  <!--    定义项目构建过程中所需的外部依赖库的来源-->
  <repositories>
    <!-- 使用  aliyun 的 Maven 源，提升下载速度 -->
    <repository>
      <id>aliyunmaven</id>
      <name>aliyun</name>
      <url>https://maven.aliyun.com/repository/public</url>
    </repository>
    <repository>
      <id>mvn-snapshots</id>
      <url>https://mvn.int.ybm100.com/repository/maven-public/</url>
      <releases>
        <enabled>true</enabled>
        <updatePolicy>always</updatePolicy>
      </releases>
      <snapshots>
        <enabled>true</enabled>
        <updatePolicy>always</updatePolicy>
      </snapshots>
    </repository>
  </repositories>

</project>
