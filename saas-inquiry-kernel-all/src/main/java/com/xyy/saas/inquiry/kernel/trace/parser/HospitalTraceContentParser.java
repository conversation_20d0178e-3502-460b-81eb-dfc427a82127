package com.xyy.saas.inquiry.kernel.trace.parser;


import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.xyy.saas.inquiry.enums.doctor.AutoGrabStatusEnum;
import com.xyy.saas.inquiry.enums.inquiry.InquiryWayTypeEnum;
import com.xyy.saas.inquiry.enums.trace.TraceNodeEnum;

import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.InquiryDoctorPageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.InquiryDoctorStatusSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.app.prescription.vo.IssuesPrescriptionRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.app.prescription.vo.PrescriptionCancelVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.InquiryDoctorDO;
import com.xyy.saas.inquiry.hospital.server.service.doctor.InquiryDoctorService;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import com.xyy.saas.inquiry.pojo.HospitalDeptDto.Dept;
import com.xyy.saas.inquiry.trace.model.TraceNodeData;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * @Author: xucao
 * @DateTime: 2025/6/18 14:57
 * @Description: 医院相关链路节点内容转化器
 **/
@Component
@Slf4j
public class HospitalTraceContentParser extends TraceContentParser {

    @Resource
    private InquiryDoctorService inquiryDoctorService;

    private final Map<String, Function<TraceNodeData, TraceNodeData>> contentParserMap = new HashMap<>();

    @PostConstruct
    public void init() {
        contentParserMap.put(TraceNodeEnum.RECEPTION_AREA_DISTRIBUTE.getCode(), this::receptionDistribute);
        contentParserMap.put(TraceNodeEnum.MATCH_DOCTOR.getCode(), this::matchDoctor);
        contentParserMap.put(TraceNodeEnum.DOCTOR_FILTER_MEDICARE.getCode(), this::filterDoctor);
        contentParserMap.put(TraceNodeEnum.INQUIRY_PRODUCT_DOCTOR_FILTER_MEDICARE.getCode(), this::filterDoctor);
        contentParserMap.put(TraceNodeEnum.DOCTOR_FILTER_INTERVAL.getCode(), this::filterDoctor);
        contentParserMap.put(TraceNodeEnum.DOCTOR_FILTER_PROVINCE.getCode(), this::filterDoctor);
        contentParserMap.put(TraceNodeEnum.DOCTOR_FILTER_REPEATDISTRIBUTE.getCode(), this::filterDoctor);
        contentParserMap.put(TraceNodeEnum.DOCTOR_FILTER_GRABFIRST.getCode(), this::filterDoctor);
        contentParserMap.put(TraceNodeEnum.DOCTOR_FILTER_SEND_NUM.getCode(), this::filterDoctor);
        contentParserMap.put(TraceNodeEnum.DOCTOR_FILTER_AUTOGRABCHECK.getCode(), this::filterDoctor);
        contentParserMap.put(TraceNodeEnum.DOCTOR_FILTER_AUTOVIDEOCHOOSE.getCode(), this::filterDoctor);
        contentParserMap.put(TraceNodeEnum.DOCTOR_DISTRIBUTE_AUTO_INQUIRY.getCode(), this::filterDoctor);
        contentParserMap.put(TraceNodeEnum.DOCTOR_DISTRIBUTE_MANUAL_INQUIRY.getCode(), this::filterDoctor);
        contentParserMap.put(TraceNodeEnum.DOCTOR_RECEPTION_AUTO_INQUIRY.getCode(), this::filterDoctor);
        contentParserMap.put(TraceNodeEnum.DOCTOR_RECEPTION_INQUIRY.getCode(), this::doctorReceptionInquiry);
        contentParserMap.put(TraceNodeEnum.DOCTOR_AUTOAUTOGRAB_INQUIRY.getCode(), this::doctorAutoGrabReception);
        contentParserMap.put(TraceNodeEnum.DOCTOR_AUTOAUTOGRAB_FOR_INQUIRY_END.getCode(), this::handleEndAutoGrabInquiry);
        contentParserMap.put(TraceNodeEnum.ISSUE_PRESCRIPTION_AUTO_INQUIRY.getCode(), this::issuePrescription);
        contentParserMap.put(TraceNodeEnum.DOCTOR_ISSUE_PRESCRIPTION.getCode(), this::issuePrescription);
        contentParserMap.put(TraceNodeEnum.DOCTOR_CANCEL_PRESCRIPTION.getCode(), this::cancelIssuePrescription);
        contentParserMap.put(TraceNodeEnum.PRESCRIPTION_ISSUE_TIME_OUT.getCode(), this::issueTimeOut);
        contentParserMap.put(TraceNodeEnum.DOCTOR_RECEIPT_CHANGE.getCode(), this::doctorChange);
    }


    /**
     * 解析内容，填充业务数据
     *
     * @param traceNodeData 链路追踪数据
     * @return 解析后的链路追踪数据
     */
    @Override
    public TraceNodeData parserContent(TraceNodeData traceNodeData) {
        return super.baseParserContent(traceNodeData, contentParserMap);
    }

    /**
     * 接诊大厅调度医生
     */
    private TraceNodeData receptionDistribute(TraceNodeData traceNodeData) {
        String content = "问诊单: " + traceNodeData.getBusinessNo() + " ,接诊大厅开始准备为问诊调度医生";
        traceNodeData.setBusinessData(content);
        return traceNodeData;
    }

    /**
     * 匹配调度医生
     */
    private TraceNodeData matchDoctor(TraceNodeData traceNodeData) {
        InquiryRecordDto recordDto = getInquiryByBusinessData(traceNodeData, "inquiryRecordDto");
        JSONObject josnParam = JSONObject.parseObject(traceNodeData.getBusinessData());
        JSONArray array = josnParam.getJSONArray("result");
        if (array == null || array.isEmpty()) {
            return traceNodeData;
        }
        List<Dept> depts = recordDto.getHospitalDeptDto().getSortInquiryDeptList();
        Dept currDept = depts.get(recordDto.getHospitalDeptDto().getCurrentDispatchDeptIndex());
        List<InquiryDoctorDO> doctorDos = inquiryDoctorService.getInquiryDoctorList(
            InquiryDoctorPageReqVO.builder().doctorPrefs(new ArrayList<String>(array.stream().filter(String.class::isInstance).map(String.class::cast).collect(Collectors.toList()))).build());
        String content = "问诊单: " + traceNodeData.getBusinessNo() + " ,本次调度派单的科室为: " + currDept.getDeptName() + "(" + currDept.getDeptPref() + ")" + " ,本次调度派单的医生为: " + doctorDos.stream().map(doctorDo -> {
            return doctorDo.getName() + "(" + doctorDo.getPref() + ")";
        }).collect(Collectors.joining(","));
        traceNodeData.setBusinessData(content);
        return traceNodeData;
    }

    /**
     * 医生过滤解析
     */
    private TraceNodeData filterDoctor(TraceNodeData traceNodeData) {
        JSONObject josnParam = JSONObject.parseObject(traceNodeData.getBusinessData());
        JSONObject jsonreq = josnParam.getJSONObject("request");
        JSONArray array = jsonreq.getJSONArray("doctorList");
        String msg = " ,执行当前过滤后可调度医生: ";
        if (ObjectUtil.equals(traceNodeData.getNodeCode(), TraceNodeEnum.DOCTOR_DISTRIBUTE_AUTO_INQUIRY.getCode())) {
            msg = " ,调度自动开方接诊,调度医生: ";
        }
        if (ObjectUtil.equals(traceNodeData.getNodeCode(), TraceNodeEnum.DOCTOR_DISTRIBUTE_MANUAL_INQUIRY.getCode())) {
            msg = " ,调度真人接诊,调度医生: ";
        }
        if (ObjectUtil.equals(traceNodeData.getNodeCode(), TraceNodeEnum.DOCTOR_RECEPTION_AUTO_INQUIRY.getCode())) {
            msg = " ,执行自动开方医生接诊,接诊医生: ";
        }
        String content = "问诊单: " + traceNodeData.getBusinessNo();
        if (array == null || array.isEmpty()) {
            content = content + " ,当前可派单医生为空，不再执行医生过滤";
        } else {
            List<InquiryDoctorDO> doctorDos = inquiryDoctorService.getInquiryDoctorList(
                InquiryDoctorPageReqVO.builder().doctorPrefs(new ArrayList<String>(array.stream().filter(String.class::isInstance).map(String.class::cast).collect(Collectors.toList()))).build());
            content = content + msg + doctorDos.stream().map(doctorDo -> {
                return doctorDo.getName() + "(" + doctorDo.getPref() + ")";
            }).collect(Collectors.joining(","));
        }
        traceNodeData.setBusinessData(content);
        return traceNodeData;
    }

    /**
     * 真人医生接诊
     *
     * @param traceNodeData
     * @return
     */
    private TraceNodeData doctorReceptionInquiry(TraceNodeData traceNodeData) {
        JSONObject josnParam = JSONObject.parseObject(traceNodeData.getBusinessData());
        JSONObject jsonresp = josnParam.getJSONObject("result");
        InquiryRecordDto recordDto = JSONObject.parseObject(jsonresp.getString("data"), InquiryRecordDto.class);
        InquiryDoctorDO doctorDO = inquiryDoctorService.getInquiryDoctorByDoctorPref(recordDto.getDoctorPref());
        String content = "问诊单: " + traceNodeData.getBusinessNo() + " ,被真人医生接诊,接诊医生: " + doctorDO.getName() + "(" + doctorDO.getPref() + ")";
        traceNodeData.setBusinessData(content);
        return traceNodeData;
    }

    /**
     * 医生派单自动抢单接诊
     *
     * @param traceNodeData
     * @return
     */
    private TraceNodeData doctorAutoGrabReception(TraceNodeData traceNodeData) {
        JSONObject josnParam = JSONObject.parseObject(traceNodeData.getBusinessData());
        JSONObject jsonreq = josnParam.getJSONObject("request");
        boolean result = josnParam.getBoolean("result");
        JSONArray array = jsonreq.getJSONArray("doctorList");
        InquiryDoctorDO doctorDO = new InquiryDoctorDO();
        if (array != null && !array.isEmpty()) {
            doctorDO = inquiryDoctorService.getInquiryDoctorByDoctorPref(array.getString(0));
        }
        String content = "问诊单: " + traceNodeData.getBusinessNo() + " ,派单模式下医生自动抢单: " + (result ? "成功" : "失败") + " ,抢单医生: " +
            doctorDO.getName() + "(" + doctorDO.getPref() + ")";
        traceNodeData.setBusinessData(content);
        return traceNodeData;
    }

    /**
     * 医生处理完处方后系统自动为自动抢单医生派单
     *
     * @param traceNodeData
     * @return
     */
    private TraceNodeData handleEndAutoGrabInquiry(TraceNodeData traceNodeData) {
        JSONObject josnParam = JSONObject.parseObject(traceNodeData.getBusinessData());
        JSONObject jsonreq = josnParam.getJSONObject("request");
        boolean result = josnParam.getBoolean("result");
        InquiryDoctorDO doctorDO = JSONObject.parseObject(jsonreq.getString("doctor"), InquiryDoctorDO.class);
        String content = "问诊单: " + traceNodeData.getBusinessNo() + " ,接诊大厅主动为自动抢单医生派单: " + (result ? "成功" : "失败") + " ,派单医生: "
            + doctorDO.getName() + "(" + doctorDO.getPref() + ")";
        traceNodeData.setBusinessData(content);
        return traceNodeData;
    }


    /**
     * 自动开方问诊开具处方
     *
     * @param traceNodeData
     * @return
     */
    private TraceNodeData issuePrescription(TraceNodeData traceNodeData) {
        JSONObject josnParam = JSONObject.parseObject(traceNodeData.getBusinessData());
        JSONObject result = josnParam.getJSONObject("result");
        IssuesPrescriptionRespVO respVO = JSONObject.parseObject(result.getString("data"), IssuesPrescriptionRespVO.class);
        String msg = " ,自动开方完成,开方医生: ";
        if (ObjectUtil.equals(traceNodeData.getNodeCode(), TraceNodeEnum.DOCTOR_ISSUE_PRESCRIPTION.getCode())) {
            msg = " ,真人医生开方完成,开方医生: ";
        }
        String content = "问诊单: " + traceNodeData.getBusinessNo() + msg + respVO.getDoctorName() + "(" + respVO.getDoctorPref() + ")";
        traceNodeData.setBusinessData(content);
        return traceNodeData;
    }

    /**
     * 自动开方问诊开具处方
     *
     * @param traceNodeData
     * @return
     */
    private TraceNodeData cancelIssuePrescription(TraceNodeData traceNodeData) {
        JSONObject josnParam = JSONObject.parseObject(traceNodeData.getBusinessData());
        JSONObject result = josnParam.getJSONObject("request");
        PrescriptionCancelVO reqVo = JSONObject.parseObject(result.getString("reqVo"), PrescriptionCancelVO.class);
        String content = "问诊单: " + traceNodeData.getBusinessNo() + " ,医生取消开方,取消开方原因: " + reqVo.getCancelReason();
        traceNodeData.setBusinessData(content);
        return traceNodeData;
    }

    /**
     * 开方超时
     *
     * @param traceNodeData
     * @return
     */
    private TraceNodeData issueTimeOut(TraceNodeData traceNodeData) {
        InquiryRecordDto inquiryDto = getInquiryByBusinessData(traceNodeData, "inquiryRecordDto");
        String content = "问诊单: " + traceNodeData.getBusinessNo() + " ,接诊医生: " + inquiryDto.getDeptName() + "(" + inquiryDto.getDeptPref() + ")" + " ,开具处方超时";
        traceNodeData.setBusinessData(content);
        return traceNodeData;
    }

    /**
     * 医生变更相关
     *
     * @param traceNodeData
     * @return
     */
    private TraceNodeData doctorChange(TraceNodeData traceNodeData) {
        JSONObject josnParam = JSONObject.parseObject(traceNodeData.getBusinessData());
        InquiryDoctorDO doctorDO = new InquiryDoctorDO();
        String method = josnParam.getString("method");
        String content = "医生: ";
        if (StringUtils.equals(method, "startReceipt") || StringUtils.equals(method, "updateDoctorReceiptScope")) {
            content = content + getContent(josnParam, method);
        }
        if (StringUtils.equals(method, "stopReceipt")) {
            JSONObject jsonreq = josnParam.getJSONObject("request");
            Long userId = jsonreq.getLong("userId");
            doctorDO = inquiryDoctorService.getInquiryDoctorByUserId(userId);
            content = content + doctorDO.getName() + "(" + doctorDO.getPref() + ")" + " ,停诊";
        }
        traceNodeData.setBusinessData(content);
        return traceNodeData;
    }

    private String getContent(JSONObject josnParam, String method) {
        JSONObject jsonreq = josnParam.getJSONObject("request");
        InquiryDoctorStatusSaveReqVO reqVO = JSONObject.parseObject(jsonreq.getString("reqVO"), InquiryDoctorStatusSaveReqVO.class);
        InquiryDoctorDO doctorDO = inquiryDoctorService.getInquiryDoctorByUserId(reqVO.getUserId());
        String content = doctorDO.getName() + "(" + doctorDO.getPref() + ")";
        if (StringUtils.equals(method, "updateDoctorReceiptScope")) {
            content = content + " ,变更接诊权限";
        }
        if (StringUtils.equals(method, "startReceipt")) {
            content = content + " ,出诊";
        }
        content = content + " ,接诊权限: 【" + reqVO.getInquiryWayTypeItems().stream().map(InquiryWayTypeEnum::getDescForPackage).collect(Collectors.joining(",")) + "】"
            + " ,自动抢单状态: " + (ObjectUtil.equals(doctorDO.getAutoGrabStatus(), AutoGrabStatusEnum.CLOSE.getCode()) ? "关闭" : "开启");
        return content;
    }


}
