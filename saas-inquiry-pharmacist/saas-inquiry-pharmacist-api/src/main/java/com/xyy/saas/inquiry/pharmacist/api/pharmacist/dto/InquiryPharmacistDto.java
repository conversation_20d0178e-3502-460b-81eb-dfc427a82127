package com.xyy.saas.inquiry.pharmacist.api.pharmacist.dto;

import com.xyy.saas.inquiry.enums.doctor.AuditStatusEnum;
import com.xyy.saas.inquiry.enums.doctor.DoctorJobTypeEnum;
import com.xyy.saas.inquiry.enums.doctor.DrawnSignEnum;
import com.xyy.saas.inquiry.enums.doctor.OnlineStatusEnum;
import com.xyy.saas.inquiry.enums.doctor.PharmacistTypeEnum;
import java.io.Serializable;
import lombok.Data;

/**
 * @ClassName：InquiryPharmacistDto
 * @Author: xucao
 * @Date: 2024/10/29 9:38
 * @Description: 药师服务接口参数
 */
@Data
public class InquiryPharmacistDto implements Serializable {

    /**
     * 主键
     */
    private Long id;
    /**
     * 药师编码
     */
    private String pref;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 姓名
     */
    private String name;
    /**
     * 性别 1男 2女
     */
    private Integer sex;
    /**
     * 身份证号码
     */
    private String idCard;
    /**
     * 联系电话
     */
    private String mobile;
    /**
     * 审核状态 0、待审核  1、审核通过  2、审核驳回 {@link AuditStatusEnum}
     */
    private Integer auditStatus;

    /**
     * 证件照地址
     */
    private String photo;
    /**
     * 个人简介
     */
    private String biography;
    /**
     * 药师执业资格,中药或西药
     */
    private Integer qualification;
    /**
     * 药师类型 平台药师 / 门店药师 / 医院药师 {@link PharmacistTypeEnum}
     */
    private Integer pharmacistType;

    /**
     * 药师性质 {@link com.xyy.saas.inquiry.enums.doctor.PharmacistNatureEnum}
     */
    private Integer pharmacistNature;

    /**
     * 毕业学校
     */
    private String school;

    /**
     * 民族
     */
    private Integer nationCode;

    /**
     * 学历
     */
    private Integer formalLevel;

    /**
     * 通信地址
     */
    private String address;

    /**
     * 在线状态：0闭诊 1出诊 {@link OnlineStatusEnum}
     */
    private Integer onlineStatus;

    /**
     * 是否手绘签名(不走认证自己绘制)：0否  1是 {@link DrawnSignEnum}
     */
    private Integer drawnSign;

    /**
     * 执业省份
     */
    private String provinceCode;

    /**
     * 药师工作类型 1全职/ 2兼职 {@link DoctorJobTypeEnum}
     */
    private Integer jobType;

    /**
     * 指纹
     */
    private String fingerPrint;

    /**
     * 备注
     */
    private String remark;

    /**
     * 药师在当前医院的编码
     */
    private String pharmacistHospitalPref;

    /**
     * 药师在当前医院的科室编码
     */
    private String pharmacistHospitalDeptPref;
    /**
     * 药师在当前医院的科室名称
     */
    private String pharmacistHospitalDeptName;

    /**
     * 注册证编号
     */
    private String registrationNo;

    /**
     * 药师资格证件编号
     */
    private String qualificationNo;

}
