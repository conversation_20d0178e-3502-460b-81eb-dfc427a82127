<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xyy.saas.inquiry.pharmacist.server.dal.mysql.pharmacist.InquiryPharmacistMapper">


  <!--
      一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
      无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
      代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
      文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
   -->

  <delete id="deleteById">
    delete
    from saas_inquiry_pharmacist
    where id = #{id}
  </delete>

  <update id="offline">
    update saas_inquiry_pharmacist set online_status = 0
    where id in
    <foreach collection="ids" open="(" item="id" separator="," close=")">
      #{id}
    </foreach>

  </update>

  <select id="pagePharmacistSystem"
    resultType="com.xyy.saas.inquiry.pharmacist.server.controller.admin.pharmacist.vo.InquiryPharmacistRespVO">
    SELECT a.*,c.certify_status,c.signature_status,c.authorize_free_sign_status,c.authorize_free_sign_ddl
    FROM saas_inquiry_pharmacist a
    left join saas_inquiry_signature_ca_auth c on a.user_id = c.user_id
    <where>
      <if test="reqVO.signaturePlatform != null">
        and c.signature_platform = #{reqVo.signaturePlatform}
      </if>
      <if test="reqVO.certifyStatus != null">
        and c.certify_status = #{reqVo.certifyStatus}
      </if>
      <include refid="Base_Column_Where"/>
    </where>
    ORDER BY a.id DESC
  </select>

  <select id="pagePharmacistStore"
    resultType="com.xyy.saas.inquiry.pharmacist.server.controller.admin.pharmacist.vo.InquiryPharmacistRespVO">
    SELECT a.*,c.certify_status,c.signature_status,c.authorize_free_sign_status,c.authorize_free_sign_ddl
    FROM saas_inquiry_pharmacist a
    left join saas_inquiry_signature_ca_auth c on a.user_id = c.user_id
    <where>
      <include refid="Base_Column_Where"/>
      <if test="reqVO.userIds != null and reqVO.userIds.size() > 0 ">
        and a.user_id in
        <foreach collection="reqVO.userIds" open="(" item="id" separator="," close=")">
          #{id}
        </foreach>
      </if>
      <if test="reqVO.signaturePlatform != null">
        and c.signature_platform = #{reqVO.signaturePlatform}
      </if>
      <if test="reqVO.certifyStatus != null">
        and c.certify_status = #{reqVO.certifyStatus}
      </if>
    </where>
    ORDER BY a.id DESC

  </select>
  <select id="pagePharmacistBindStore" resultType="com.xyy.saas.inquiry.pojo.TenantDto">
    SELECT b.status,d.* FROM saas_inquiry_pharmacist a
    left join system_tenant_user_relation b on a.user_id = b.user_id and b.deleted = false
    left join system_tenant d on b.tenant_id = d.id
    <where>
      <include refid="Base_Column_Where"/>
      <if test="reqVO.tenantId != null">
        AND b.tenant_id = #{reqVO.tenantId}
      </if>
      <if test="reqVO.status != null">
        AND b.status = #{reqVO.status}
      </if>
      <if test="reqVO.roleIds != null and reqVO.roleIds.size() > 0 ">
        and exists (select 1 from system_user_role c where a.user_id = c.user_id and b.tenant_id = c.tenant_id
        and c.role_id in
        <foreach collection="reqVO.roleIds" open="(" item="id" separator="," close=")">
          #{id}
        </foreach>
        )
      </if>
      <if test="reqVO.tenantIds != null and reqVO.tenantIds.size() > 0 ">
        and b.tenant_id in
        <foreach collection="reqVO.tenantIds" open="(" item="id" separator="," close=")">
          #{id}
        </foreach>
      </if>
    </where>
    ORDER BY a.id DESC
  </select>
  <select id="queryByCondition"
    resultType="com.xyy.saas.inquiry.pharmacist.server.dal.dataobject.pharmacist.InquiryPharmacistDO">
    SELECT a.* FROM saas_inquiry_pharmacist a
    <where>
      <include refid="Base_Column_Where"/>
    </where>
  </select>

  <select id="getPharmacistBindStoreList"
    resultType="com.xyy.saas.inquiry.pojo.TenantDto">
    SELECT d.* FROM saas_inquiry_pharmacist a
    left join system_tenant_user_relation b on a.user_id = b.user_id and b.deleted = false
    left join system_tenant d on b.tenant_id = d.id
    where a.deleted = false and b.deleted = false and d.deleted = false and a.user_id = #{userId}
    <if test="roleIds != null and roleIds.size() > 0 ">
      and exists (select 1 from system_user_role c where a.user_id = c.user_id
      and c.role_id in
      <foreach collection="roleIds" open="(" item="id" separator="," close=")">
        #{id}
      </foreach>
      )
    </if>
  </select>

  <sql id="Base_Column_Where">
    <if test="reqVO.id != null">
      AND a.id = #{reqVO.id}
    </if>
    <if test="reqVO.userId != null">
      AND a.user_id = #{reqVO.userId}
    </if>
    <if test="reqVO.pref != null and reqVO.pref != '' ">
      AND a.pref = #{reqVO.pref}
    </if>
    <if test="reqVO.name != null and reqVO.name != ''">
      AND a.name LIKE CONCAT('%', #{reqVO.name}, '%')
    </if>
    <if test="reqVO.sex != null">
      AND a.sex = #{reqVO.sex}
    </if>
    <if test="reqVO.idCard != null and reqVO.idCard != ''">
      AND a.id_card = #{reqVO.idCard}
    </if>
    <if test="reqVO.mobile != null and reqVO.mobile != ''">
      AND a.mobile = #{reqVO.mobile}
    </if>
    <if test="reqVO.auditStatus != null">
      AND a.audit_status = #{reqVO.auditStatus}
    </if>
    <if test="reqVO.onlineStatus != null">
      AND a.online_status = #{reqVO.onlineStatus}
    </if>
    <if test="reqVO.qualification != null">
      AND a.qualification = #{reqVO.qualification}
    </if>
    <if test="reqVO.pharmacistType != null">
      AND a.pharmacist_type = #{reqVO.pharmacistType}
    </if>
    <if test="reqVO.jobType != null">
      AND a.job_type = #{reqVO.jobType}
    </if>
    <if test="reqVO.pharmacistNature != null">
      AND a.pharmacist_nature = #{reqVO.pharmacistNature}
    </if>
    <if test="reqVO.envTag != null">
      AND a.env_tag = #{reqVO.envTag}
    </if>
    <if test="reqVO.createTime != null and reqVO.createTime.length > 0">
      AND a.create_time BETWEEN #{reqVO.createTime[0],javaType=java.time.LocalDateTime} AND
      #{reqVO.createTime[1],javaType=java.time.LocalDateTime}
    </if>
  </sql>

</mapper>