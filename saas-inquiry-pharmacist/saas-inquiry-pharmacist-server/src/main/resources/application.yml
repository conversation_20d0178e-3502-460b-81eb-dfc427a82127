server:
  port: 40883

spring:
  profiles:
    active: test
  application:
    name: saas-inquiry-pharmacist-server
  cloud:
    nacos:
      config:
        server-addr: ${spring.cloud.nacos.discovery.server-addr}
        import-check:
          enabled: false
          #file-extension: yaml
    # Redis ???Redisson ???????????????????
    datasource:
      dynamic:
        druid: # Druid ????????????
          initial-size: 5 # ?????
          min-idle: 10 # ???????
          max-active: 20 # ???????
          max-wait: 600000 # ???????????????????
          time-between-eviction-runs-millis: 60000 # ???????????????????????????????
          min-evictable-idle-time-millis: 300000 # ??????????????????????
          max-evictable-idle-time-millis: 900000 # ??????????????????????
          validation-query: SELECT 1 # ??????????
          test-while-idle: true
          test-on-borrow: false
          test-on-return: false
        primary: master
dubbo:
  application:
    name: ${spring.application.name}
  registry:
    address: nacos://${spring.cloud.nacos.discovery.server-addr}
  config-center:
    address: nacos://${spring.cloud.nacos.discovery.server-addr}
  protocol:
    name: dubbo
    port: -1 # ????????
  consumer:
    timeout: 5000
    check: false
  provider:
    timeout: 10000
# MyBatis Plus ????
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true # ????? true ????????????
#event:
#  bus:
#    aliMQAccessKey: ""
#    aliMQSecretKey: ""
#    aliYunNameServer: ************:9876

