package com.xyy.saas.inquiry.pharmacist.server.controller.admin.pharmacist.vo;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.xyy.saas.inquiry.enums.doctor.DoctorJobTypeEnum;
import com.xyy.saas.inquiry.enums.user.UserStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.AssertTrue;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

@Schema(description = "管理后台 - 药师信息分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class InquiryPharmacistPageReqVO extends PageParam {

    @Schema(description = "药师ID", example = "1372")
    private Long id;

    @Schema(description = "用户ID", example = "19857")
    private Long userId;

    @Schema(description = "编码", example = "P1000001")
    private String pref;

    @Schema(description = "姓名", example = "张三")
    private String name;

    @Schema(description = "性别 1男 2女")
    private Integer sex;

    @Schema(description = "身份证号码")
    private String idCard;

    @Schema(description = "联系电话")
    private String mobile;

    @Schema(description = "审核状态 0、待审核  1、审核通过  2、审核驳回", example = "1")
    private Integer auditStatus;

    @Schema(description = "在线状态 0离线 1在线", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer onlineStatus;

    @Schema(description = "证件照地址")
    private String photo;

    @Schema(description = "个人简介")
    private String biography;

    @Schema(description = "药师执业资格,中药或西药")
    private Integer qualification;

    @Schema(description = "药师类型 平台药师 / 门店药师 / 医院药师", example = "2")
    private Integer pharmacistType;

    /**
     * 药师工作类型 {@link DoctorJobTypeEnum}
     */
    @Schema(description = "药师工作类型 1全职/ 2兼职", example = "2")
    private Integer jobType;

    @Schema(description = "药师性质", example = "2")
    private Integer pharmacistNature;

    @Schema(description = "毕业学校")
    private String school;

    // user ------
    /**
     * {@link UserStatusEnum}
     */
    @Schema(description = "用户 0启用 1禁用", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("状态")
    private Integer status;


    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    /**
     * 门店id
     */
    private Long tenantId;


    /**
     * 角色Ids
     */
    private List<Long> roleIds;

    /**
     * 可见门店ids
     */
    private List<Long> tenantIds;

    /**
     * 用户ids
     */
    private List<Long> userIds;

    @Schema(description = "环境标志：prod-真实数据；test-测试数据；show-线上演示数据", requiredMode = Schema.RequiredMode.REQUIRED)
    private String envTag;

    @Schema(description = "签章平台", example = "1")
    private Integer signaturePlatform;

    @Schema(description = "实名认证状态 0: 待认证，1: 认证完成，2: 认证失败", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private Integer certifyStatus;

    private Long maxId;

    @AssertTrue(message = "签章平台不能为空")
    @JsonIgnore
    public boolean isSignaturePlatformValid() {
        return certifyStatus == null || signaturePlatform != null;
    }
}