package com.xyy.saas.inquiry.pharmacist.server.controller.admin.pharmacist.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 门店药师关系 Response VO")
@Data
@ExcelIgnoreUnannotated
public class InquiryTenantPharmacistRelationRespVO {

    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "2618")
    @ExcelProperty("用户ID")
    private Long id;

    @Schema(description = "药师ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "31948")
    @ExcelProperty("药师ID")
    private Long pharmacistId;

    @Schema(description = "药师状态 0启用 1禁用", example = "1")
    @ExcelProperty("药师状态 0启用 1禁用")
    private Integer status;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}