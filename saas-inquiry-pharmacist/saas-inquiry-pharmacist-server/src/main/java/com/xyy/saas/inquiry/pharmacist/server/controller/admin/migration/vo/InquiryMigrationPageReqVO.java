package com.xyy.saas.inquiry.pharmacist.server.controller.admin.migration.vo;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;
import org.springframework.format.annotation.DateTimeFormat;

@Schema(description = "管理后台 - 荷叶老问诊迁移分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class InquiryMigrationPageReqVO extends PageParam {

    @Schema(description = "机构编码")
    private String organSign;

    private List<String> organSigns;

    private List<Long> ids;

    @Schema(description = "名称", example = "王五")
    private String name;
    /**
     * 迁移后门店id
     */
    private Long tenantId;

    @Schema(description = "省")
    private String province;

    @Schema(description = "省编码")
    private String provinceCode;

    @Schema(description = "市")
    private String city;

    @Schema(description = "市编码")
    private String cityCode;

    @Schema(description = "区")
    private String area;

    @Schema(description = "区编码")
    private String areaCode;

    @Schema(description = "迁移状态 0待确认 1待迁移 2迁移中 3已迁移", example = "1")
    private Integer status;

    @Schema(description = "迁移类型 1门店、2套餐", example = "2")
    private Integer migrationType;

    @Schema(description = "迁移计划开始时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] startTime;

    @Schema(description = "门店迁移完成始时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] storeEndTime;

    @Schema(description = "套餐迁移完成始时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] packageEndTime;

    @Schema(description = "门店状态 0待迁移 1失败 2成功", example = "1")
    private Integer storeStatus;

    @Schema(description = "员工状态 0待迁移 1失败 2成功", example = "1")
    private Integer employeeStatus;

    @Schema(description = "药师状态 0待迁移 1失败 2成功", example = "1")
    private Integer pharmacistStatus;

    @Schema(description = "患者状态 0待迁移 1失败 2成功", example = "2")
    private Integer patientStatus;

    @Schema(description = "门店迁移状态 0待迁移 1失败 2成功", example = "2")
    private Integer storeMigrationStatus;

    @Schema(description = "套餐迁移状态 0待迁移 1失败 2成功", example = "1")
    private Integer packageMigrationStatus;

    @Schema(description = "备注", example = "你说的对")
    private String remark;

    @Schema(description = "扩展信息")
    private String ext;

    @Schema(description = "导入-创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;


    private String createStartTime;

    private String createEndTime;


    public LocalDateTime[] getCreateTime() {
        if (StringUtils.isNoneBlank(createStartTime, createEndTime)) {
            LocalDateTime start = LocalDateTime.parse(createStartTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            LocalDateTime end = LocalDateTime.parse(createEndTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            return new LocalDateTime[]{start, end};
        }
        return createTime;
    }

}