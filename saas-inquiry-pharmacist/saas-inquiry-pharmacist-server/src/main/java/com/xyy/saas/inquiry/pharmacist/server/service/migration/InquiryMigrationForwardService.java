package com.xyy.saas.inquiry.pharmacist.server.service.migration;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.inquiry.pharmacist.enums.ErrorCodeConstants.INQUIRY_MIGRATION_DRUGSTORE_FAIL;
import static com.xyy.saas.inquiry.pharmacist.enums.ErrorCodeConstants.INQUIRY_MIGRATION_EMPLOYEE_FAIL;
import static com.xyy.saas.inquiry.pharmacist.enums.ErrorCodeConstants.INQUIRY_MIGRATION_PACKAGE_FAIL;
import static com.xyy.saas.inquiry.pharmacist.enums.ErrorCodeConstants.INQUIRY_MIGRATION_PATIENT_FAIL;
import static com.xyy.saas.inquiry.pharmacist.enums.ErrorCodeConstants.INQUIRY_MIGRATION_PHARMACIST_FAIL;
import static com.xyy.saas.inquiry.pharmacist.enums.ErrorCodeConstants.INQUIRY_MIGRATION_QUERY_STORE_ERROR;
import static com.xyy.saas.inquiry.pharmacist.enums.ErrorCodeConstants.INQUIRY_MIGRATION_UPDATE_STORE_ERROR;

import cn.hutool.core.collection.CollUtil;
import com.xyy.saas.inquiry.pharmacist.server.config.InquiryPharmacistForwardClient;
import com.xyy.saas.inquiry.pharmacist.server.config.migration.InquiryMigrationForwardClient;
import com.xyy.saas.inquiry.pharmacist.server.controller.admin.pharmacist.vo.forward.InquiryPharmacistForwardRespVO;
import com.xyy.saas.inquiry.pharmacist.server.service.migration.dto.MigrationDrugStoreReqDto;
import com.xyy.saas.inquiry.pharmacist.server.service.migration.dto.MigrationEmployeeRespDto;
import com.xyy.saas.inquiry.pojo.migration.MigrationPackageRespDto;
import com.xyy.saas.inquiry.pharmacist.server.service.pharmacist.dto.PharmacistForwardDetailRespDto;
import com.xyy.saas.inquiry.pojo.ForwardResult;
import com.xyy.saas.inquiry.pojo.migration.MigrationDrugStoreRespDto;
import com.xyy.saas.inquiry.pojo.migration.MigrationPatientDto;
import jakarta.annotation.Resource;
import java.util.List;
import java.util.Optional;
import org.springframework.stereotype.Component;

/**
 * 荷叶老问诊迁移 Service 接口
 *
 * <AUTHOR>
 */
@Component
public class InquiryMigrationForwardService {

    @Resource
    private InquiryMigrationForwardClient inquiryMigrationForwardClient;

    @Resource
    private InquiryPharmacistForwardClient inquiryPharmacistForwardClient;

    /**
     * 查询门店列表
     *
     * @param organSigns
     * @return
     */
    public List<MigrationDrugStoreRespDto> queryDrugStoreLists(List<String> organSigns) {
        if (CollUtil.isEmpty(organSigns)) {
            return List.of();
        }
        ForwardResult<List<MigrationDrugStoreRespDto>> forwardResult = inquiryMigrationForwardClient.queryDrugStoreLists(
            new MigrationDrugStoreReqDto().setOrganSigns(organSigns));

        if (!forwardResult.isSuccess()) {
            throw exception(INQUIRY_MIGRATION_QUERY_STORE_ERROR, forwardResult.getMsg());
        }
        return Optional.ofNullable(forwardResult.getResult()).orElse(List.of());
    }

    /**
     * 更新门店迁移状态
     *
     * @param reqDto
     */
    public void updateDrugStoreMigrationStatus(MigrationDrugStoreReqDto reqDto) {
        ForwardResult<?> result = inquiryMigrationForwardClient.updateDrugStoreMigrationStatus(reqDto);

        if (!result.isSuccess()) {
            throw exception(INQUIRY_MIGRATION_UPDATE_STORE_ERROR, result.getMsg());
        }
    }

    /**
     * 同步门店
     *
     * @param organSign
     * @return
     */
    public MigrationDrugStoreRespDto queryDrugStore(String organSign) {

        ForwardResult<MigrationDrugStoreRespDto> forwardResult = inquiryMigrationForwardClient.queryDrugStore(organSign);

        if (!forwardResult.isSuccess() || forwardResult.getResult() == null) {
            throw exception(INQUIRY_MIGRATION_DRUGSTORE_FAIL, forwardResult.getMsg());
        }
        return forwardResult.getResult();
    }

    /**
     * 同步员工
     */
    public MigrationEmployeeRespDto queryDrugStoreEmployee(String organSign) {

        ForwardResult<MigrationEmployeeRespDto> forwardResult = inquiryMigrationForwardClient.queryDrugStoreEmployee(organSign);

        if (!forwardResult.isSuccess() || forwardResult.getResult() == null) {
            throw exception(INQUIRY_MIGRATION_EMPLOYEE_FAIL, forwardResult.getMsg());
        }
        return forwardResult.getResult();
    }

    /**
     * 查药师列表
     */
    public List<InquiryPharmacistForwardRespVO> queryDrugStorePharmacist(String organSign) {

        ForwardResult<List<InquiryPharmacistForwardRespVO>> forwardResult = inquiryMigrationForwardClient.queryDrugStorePharmacist(organSign);

        if (!forwardResult.isSuccess()) {
            throw exception(INQUIRY_MIGRATION_PHARMACIST_FAIL, forwardResult.getMsg());
        }
        return forwardResult.getResult();
    }

    /**
     * 查药师详情
     */
    public PharmacistForwardDetailRespDto getDrugStorePharmacist(String guid) {

        ForwardResult<PharmacistForwardDetailRespDto> forwardResult = inquiryPharmacistForwardClient.syncUserDoctorPharmacist(guid);

        if (!forwardResult.isSuccess()) {
            throw exception(INQUIRY_MIGRATION_PHARMACIST_FAIL, guid + ":" + forwardResult.getMsg());
        }
        return forwardResult.getResult();
    }


    /**
     * 查询患者列表
     */
    public List<MigrationPatientDto> queryDrugStorePatient(String organSign) {

        ForwardResult<List<MigrationPatientDto>> forwardResult = inquiryMigrationForwardClient.queryDrugStorePatient(organSign);

        if (!forwardResult.isSuccess()) {
            throw exception(INQUIRY_MIGRATION_PATIENT_FAIL, forwardResult.getMsg());
        }
        return forwardResult.getResult();
    }

    /**
     * 获取门店套餐
     */
    public List<MigrationPackageRespDto> queryDrugStorePackage(String organSign) {

        ForwardResult<List<MigrationPackageRespDto>> forwardResult = inquiryMigrationForwardClient.queryDrugStorePackage(organSign);

        if (!forwardResult.isSuccess()) {
            throw exception(INQUIRY_MIGRATION_PACKAGE_FAIL, forwardResult.getMsg());
        }
        return forwardResult.getResult();
    }

}