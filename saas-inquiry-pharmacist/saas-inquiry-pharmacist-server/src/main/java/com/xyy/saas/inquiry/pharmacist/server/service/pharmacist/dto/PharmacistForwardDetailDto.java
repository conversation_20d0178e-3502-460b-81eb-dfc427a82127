package com.xyy.saas.inquiry.pharmacist.server.service.pharmacist.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

@Schema(description = "管理后台 - 药师信息 Response VO")
@Data
public class PharmacistForwardDetailDto implements Serializable {

    /**
     * 编码
     */
    private Long id;

    /**
     * 药师guid
     */
    private String guid;

    /**
     * 药师姓名
     */
    private String docName;

    /**
     * 性别 1男 2女
     */
    private Integer sex;

    /**
     * 身份证号码
     */
    private String idCard;

    /**
     * 手机号
     */
    private String docTel;

    /**
     * 法大大实名认证状态 0: 待认证; 1: 认证完成
     */
    private Integer fddCertifyStatus;

    /**
     * 免验证签类型 1 未完成 2 已完成 3 授权临期
     */
    private Integer visaFreeStatus;

    /**
     * 在线状态：0 未出诊 1 出诊中
     */
    private Byte inquiryStatus;

    /**
     * 药师资格  0 中西药药师   1 西药药师   2 中药药师
     */
    private Integer qualification;

    /**
     * 药师类型 - 执业资格，0平台药师 1药店药师
     */
    private Byte pharmacistType;

    /**
     * 平台药师类型 0兼职 1全职
     */
    private Byte platformPharmacistType;
    /**
     * 绑定门店名称
     */
    private String drugstoreName;

    /**
     * 机构所在省
     */
    private String workingAdress;

    // *************** 详情 基础信息

    /**
     * 民族名称
     */
    private String nationCode;

    /**
     * 学历
     */
    private String docEdu;

    /**
     * 毕业学校
     */
    private String docDegree;

    /**
     * 降级标识 - 走自己绘制
     */
    private Integer downFlag;

    /**
     * 医师擅长专业
     */
    private String professional;

    /**
     * 个人简介
     */
    private String docComment;

    /**
     * 身份证
     */
    private String idCardList;

    // ************** 证件信息

    /**
     * 头像地址
     */
    private String headPortrait;

    /**
     * 注册证编号
     */
    private String pracNo;

    /**
     * 注册证执业单位
     */
    private String workInstName;

    /**
     * 注册证注册日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date docMultiSitedDateStart;
    /**
     * 注册证有效期至
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date docMultiSitedDateEnd;

    /**
     * 注册证照片
     */
    private String docCertList;


    /**
     * 资格证号
     */
    private String certNo;

    /**
     * 资格证取得时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date certRecDate;


    /**
     * 资格证
     */
    private String certDocPracList;

}