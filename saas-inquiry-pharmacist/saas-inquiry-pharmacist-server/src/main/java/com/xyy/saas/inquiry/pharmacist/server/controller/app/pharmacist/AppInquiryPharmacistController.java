package com.xyy.saas.inquiry.pharmacist.server.controller.app.pharmacist;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.hutool.core.util.NumberUtil;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import com.xyy.saas.inquiry.drugstore.api.option.InquiryOptionConfigApi;
import com.xyy.saas.inquiry.drugstore.enums.InquiryOptionTypeEnum;
import com.xyy.saas.inquiry.enums.pharmacist.PharmacistAuditAreaTypeEnum;
import com.xyy.saas.inquiry.pharmacist.server.controller.admin.pharmacist.vo.InquiryPharmacistRespVO;
import com.xyy.saas.inquiry.pharmacist.server.controller.app.pharmacist.vo.PharmacistUpdateInfoVo;
import com.xyy.saas.inquiry.pharmacist.server.service.pharmacist.InquiryPharmacistService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@Tag(name = "APP+PC - 药师基础信息")
@RestController
@RequestMapping(value = {"/admin-api/kernel/pharmacist/inquiry-pharmacist", "/app-api/kernel/pharmacist/inquiry-pharmacist"})
@Validated
public class AppInquiryPharmacistController {

    @Resource
    private InquiryPharmacistService inquiryPharmacistService;

    @DubboReference
    protected InquiryOptionConfigApi inquiryOptionConfigApi;

    @PutMapping("/get-info")
    @Operation(summary = "药师信息查询接口")
    @PreAuthorize("@ss.hasRole('pharmacist')")
    public CommonResult<InquiryPharmacistRespVO> getPharmacistInfo() {
        return success(inquiryPharmacistService.getPharmacistInfo());
    }


    @PostMapping("/update-info")
    @Operation(summary = "药师信息更新接口,执业地区,跳过指纹")
    @PreAuthorize("@ss.hasRole('pharmacist')")
    public CommonResult<Boolean> updatePharmacistInfo(@RequestBody PharmacistUpdateInfoVo updateInfoVo) {
        return success(inquiryPharmacistService.updateInquiryPharmacistInfo(updateInfoVo));
    }


    @PutMapping("/start-receipt")
    @Operation(summary = "药师出诊")
    @PreAuthorize("@ss.hasRole('pharmacist')")
    public CommonResult<Boolean> startReceipt() {
        return success(inquiryPharmacistService.startReceipt());
    }

    @PutMapping("/stop-receipt")
    @Operation(summary = "药师停诊")
    @PreAuthorize("@ss.hasRole('pharmacist')")
    public CommonResult<Boolean> stopReceipt() {
        return success(inquiryPharmacistService.stopReceipt());
    }


    @GetMapping("/log-off-check")
    @Operation(summary = "药师账号注销前检查  true: 可以注销 不可注销时直接展示失败原因")
    public CommonResult<Boolean> logOffCheck() {

        return success(inquiryPharmacistService.logOffCheck());
    }

    @GetMapping("/get-platform-area")
    @Operation(summary = "获取平台药师区域")
    public CommonResult<String> getPlatformArea(Long provinceCode) {

        String optionValue = inquiryOptionConfigApi.getInquiryOptionValue(provinceCode, InquiryOptionTypeEnum.PRES_PHARMACIST_AREA_TYPE);

        PharmacistAuditAreaTypeEnum areaTypeEnum = PharmacistAuditAreaTypeEnum.fromCode(NumberUtil.parseInt(optionValue, null));

        return success(areaTypeEnum.getDescription());
    }
}