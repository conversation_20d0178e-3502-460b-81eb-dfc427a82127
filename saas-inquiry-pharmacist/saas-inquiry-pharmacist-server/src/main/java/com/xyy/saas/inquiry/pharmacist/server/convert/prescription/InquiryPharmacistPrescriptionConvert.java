package com.xyy.saas.inquiry.pharmacist.server.convert.prescription;

import com.xyy.saas.inquiry.enums.inquiry.PrescriptionStatusEnum;
import com.xyy.saas.inquiry.enums.prescription.PrescriptionAuditStatusEnum;
import com.xyy.saas.inquiry.enums.prescription.template.PrescriptionTemplateFieldEnum;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionRespDTO;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionUpdateDTO;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDetailDto;
import com.xyy.saas.inquiry.pharmacist.api.audit.dto.RemotePrescriptionDto;
import com.xyy.saas.inquiry.pharmacist.server.controller.app.audit.vo.InquiryPrescriptionAuditSaveReqVO;
import com.xyy.saas.inquiry.pharmacist.server.controller.app.audit.vo.InquiryPrescriptionAuditVO;
import com.xyy.saas.inquiry.pharmacist.server.controller.app.audit.vo.InquiryPrescriptionReceiveVO;
import com.xyy.saas.inquiry.pharmacist.server.dal.dataobject.pharmacist.InquiryPharmacistDO;
import com.xyy.saas.inquiry.pharmacist.server.service.audit.dto.InquiryPharmacistPrescriptionDTO;
import com.xyy.saas.inquiry.signature.mq.SignaturePassingMessage;
import java.time.LocalDateTime;
import java.util.Objects;
import java.util.Optional;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * @Author:chenxiaoyi
 * @Date:2024/10/12 17:17
 */
@Mapper
public interface InquiryPharmacistPrescriptionConvert {

    InquiryPharmacistPrescriptionConvert INSTANCE = Mappers.getMapper(InquiryPharmacistPrescriptionConvert.class);

    InquiryPharmacistPrescriptionDTO convertDTO(InquiryPrescriptionRespDTO prescription);

    default InquiryPrescriptionUpdateDTO convertSignPassingUpdateDto(SignaturePassingMessage spMessage, InquiryPrescriptionRespDTO prescription) {
        Integer status = PrescriptionTemplateFieldEnum.isAuditEnd(spMessage.getNextField() == null
            ? "" : spMessage.getNextField().getField())
            ? PrescriptionStatusEnum.APPROVAL.getStatusCode()
            : Objects.equals(prescription.getStatus(), PrescriptionStatusEnum.WAITING.getStatusCode())
                ? PrescriptionStatusEnum.WAIT_APPROVAL.getStatusCode() : null;

        prescription.setPrescriptionImgUrl(spMessage.getImgUrl());
        prescription.setPrescriptionPdfUrl(spMessage.getPdfUrl());

        return InquiryPrescriptionUpdateDTO.builder()
            .id(prescription.getId())
            .pref(prescription.getPref())
            .auditLevel(spMessage.getTotalLevel())
            .prescriptionPdfUrl(spMessage.getPdfUrl())
            .prescriptionImgUrl(spMessage.getImgUrl())
            .status(status).build();
    }


    default InquiryPrescriptionReceiveVO convertReceiveVO(InquiryPharmacistPrescriptionDTO prescriptionDTO, InquiryRecordDetailDto inquiryRecordDetail, Long auditRecordId, Integer auditTimeOut) {

        InquiryPrescriptionReceiveVO vo = convertReceiveVO(prescriptionDTO);
        vo.setAuditRecordId(auditRecordId);
        vo.setGestationLactationValue(inquiryRecordDetail.getGestationLactationValue());
        vo.setAllergic(inquiryRecordDetail.getAllergic());
        vo.setLiverKidneyValue(inquiryRecordDetail.getLiverKidneyValue());
        vo.setSlowDisease(inquiryRecordDetail.getSlowDisease());
        vo.setAuditTimeOut(auditTimeOut);
        return vo;
    }

    @Mapping(target = "prescriptionId", source = "id")
    InquiryPrescriptionReceiveVO convertReceiveVO(InquiryPharmacistPrescriptionDTO prescriptionDTO);

    InquiryPharmacistPrescriptionDTO convertDTO(RemotePrescriptionDto remotePrescriptionDto);

    default void fillInquiryDetailReceiveVO(InquiryPrescriptionReceiveVO receiveVO, InquiryRecordDetailDto inquiryRecordDetail) {
        receiveVO.setGestationLactationValue(inquiryRecordDetail.getGestationLactationValue());
        receiveVO.setAllergic(inquiryRecordDetail.getAllergic());
        receiveVO.setLiverKidneyValue(inquiryRecordDetail.getLiverKidneyValue());
        receiveVO.setSlowDisease(inquiryRecordDetail.getSlowDisease());
    }

    default InquiryPrescriptionAuditSaveReqVO convertAuditSaveVo(InquiryPrescriptionAuditVO auditVO, InquiryPrescriptionRespDTO prescription) {
        return InquiryPrescriptionAuditSaveReqVO.builder().id(auditVO.getAuditRecordId())
            .auditWayType(Optional.ofNullable(auditVO.getAuditWayType()).orElse(prescription.getAuditWayType()))
            .auditStatus(PrescriptionAuditStatusEnum.AUDITING.getCode())
            .auditorApprovalTime(LocalDateTime.now())
            .auditorSignatureTime(LocalDateTime.now()).build();
    }

    default InquiryPrescriptionUpdateDTO convertAuditUpdateVo(InquiryPrescriptionAuditVO auditVO, InquiryPrescriptionRespDTO prescription, InquiryPharmacistDO pharmacist) {
        return InquiryPrescriptionUpdateDTO.builder().id(prescription.getId())
            .pharmacistPref(pharmacist.getPref())
            .pharmacistName(pharmacist.getName())
            .auditWayType(Optional.ofNullable(auditVO.getAuditWayType()).orElse(prescription.getAuditWayType()))
            .auditPrescriptionTime(LocalDateTime.now())
            .status(PrescriptionStatusEnum.APPROVAL_ING.getStatusCode()).build();
    }

    default InquiryPrescriptionUpdateDTO convertAuditRejectUpdateVo(InquiryPrescriptionAuditVO auditVO, InquiryPrescriptionRespDTO prescription, InquiryPharmacistDO pharmacist, String url) {
        return InquiryPrescriptionUpdateDTO.builder().id(prescription.getId())
            .status(PrescriptionStatusEnum.APPROVAL_REJECTED.getStatusCode())
            .invalidReason(auditVO.getAuditorRejectedReason())
            .auditPrescriptionTime(LocalDateTime.now())
            .pharmacistName(pharmacist.getName())
            .pharmacistPref(pharmacist.getPref())
            .auditWayType(Optional.ofNullable(auditVO.getAuditWayType()).orElse(prescription.getAuditWayType()))
            .prescriptionPdfUrl(url).prescriptionImgUrl(url).build();
    }
}
