package com.xyy.saas.inquiry.pharmacist.server.controller.app.audit.vo;

import com.xyy.saas.inquiry.enums.doctor.AuditorTypeEnum;
import com.xyy.saas.inquiry.enums.prescription.PrescriptionAuditStatusEnum;
import com.xyy.saas.inquiry.enums.prescription.PrescriptionAuditWayTypeEnum;
import com.xyy.saas.inquiry.pojo.prescription.Coordinate;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;

@Schema(description = " 处方审核 VO")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class InquiryPrescriptionAuditVO {

    @Schema(description = "处方审核id", requiredMode = Schema.RequiredMode.REQUIRED, example = "111")
    @NotNull(message = "处方审核id不可为空")
    private Long auditRecordId;

    @Schema(description = "处方编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "HYWZ100003")
    @NotEmpty(message = "处方号不可为空")
    private String pref;

    @Schema(description = "处方审核状态")
    private PrescriptionAuditStatusEnum auditStatus;


    @Schema(description = "药师驳回原因", example = "不喜欢")
    @Length(max = 1024, message = "驳回原因最大长度1024")
    private String auditorRejectedReason;

    @Schema(description = "审核 (药师)端操作系统类型 前端传入", example = "1")
    private String clientOsType;

    @Schema(description = "审核人员类型", example = "1")
    private AuditorTypeEnum auditorTypeEnum;

    @Schema(description = "审核人员签名坐标", example = "116,39")
    private Coordinate coordinate;

    @Schema(description = "审核人员签名图片", example = "http://127.0.0.1:8080/xxx.png")
    private String auditSignImgUrl;

    /**
     * {@link PrescriptionAuditWayTypeEnum}
     */
    @Schema(description = "审方类别")
    private Integer auditWayType;
}