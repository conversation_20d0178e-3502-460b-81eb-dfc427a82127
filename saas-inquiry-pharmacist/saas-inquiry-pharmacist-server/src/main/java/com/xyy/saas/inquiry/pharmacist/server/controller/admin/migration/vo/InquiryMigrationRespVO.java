package com.xyy.saas.inquiry.pharmacist.server.controller.admin.migration.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.Data;

@Schema(description = "管理后台 - 荷叶老问诊迁移 Response VO")
@Data
@ExcelIgnoreUnannotated
public class InquiryMigrationRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "3139")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "机构编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("机构编码")
    private String organSign;

    @Schema(description = "名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @ExcelProperty("名称")
    private String name;
    /**
     * 迁移后门店id
     */
    private Long tenantId;

    @Schema(description = "省")
    @ExcelProperty("省")
    private String province;

    @Schema(description = "省编码")
    @ExcelProperty("省编码")
    private String provinceCode;

    @Schema(description = "市")
    @ExcelProperty("市")
    private String city;

    @Schema(description = "市编码")
    @ExcelProperty("市编码")
    private String cityCode;

    @Schema(description = "区")
    @ExcelProperty("区")
    private String area;

    @Schema(description = "区编码")
    @ExcelProperty("区编码")
    private String areaCode;

    @Schema(description = "迁移状态 0待确认 1待迁移 2迁移中 3已迁移", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("迁移状态 0待确认 1待迁移 2迁移中 3已迁移")
    private Integer status;

    @Schema(description = "迁移类型 1门店、2套餐", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("迁移类型 1门店、2套餐")
    private Integer migrationType;

    @Schema(description = "迁移计划开始时间")
    @ExcelProperty("迁移计划开始时间")
    private LocalDateTime startTime;

    @Schema(description = "门店迁移完成始时间")
    @ExcelProperty("门店迁移完成始时间")
    private LocalDateTime storeEndTime;

    @Schema(description = "套餐迁移完成始时间")
    @ExcelProperty("套餐迁移完成始时间")
    private LocalDateTime packageEndTime;

    @Schema(description = "门店状态 0待迁移 1失败 2成功", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("门店状态 0待迁移 1失败 2成功")
    private Integer storeStatus;

    @Schema(description = "员工状态 0待迁移 1失败 2成功", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("员工状态 0待迁移 1失败 2成功")
    private Integer employeeStatus;

    @Schema(description = "药师状态 0待迁移 1失败 2成功", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("药师状态 0待迁移 1失败 2成功")
    private Integer pharmacistStatus;

    @Schema(description = "患者状态 0待迁移 1失败 2成功", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("患者状态 0待迁移 1失败 2成功")
    private Integer patientStatus;

    @Schema(description = "门店迁移状态 0待迁移 1失败 2成功", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("门店迁移状态 0待迁移 1失败 2成功")
    private Integer storeMigrationStatus;

    @Schema(description = "套餐迁移状态 0待迁移 1失败 2成功", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("套餐迁移状态 0待迁移 1失败 2成功")
    private Integer packageMigrationStatus;

    @Schema(description = "备注", example = "你说的对")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "扩展信息")
    @ExcelProperty("扩展信息")
    private String ext;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}