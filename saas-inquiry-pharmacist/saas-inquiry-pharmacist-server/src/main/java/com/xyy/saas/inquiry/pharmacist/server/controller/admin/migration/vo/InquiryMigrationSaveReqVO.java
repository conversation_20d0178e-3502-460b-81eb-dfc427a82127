package com.xyy.saas.inquiry.pharmacist.server.controller.admin.migration.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;
import lombok.Data;

@Schema(description = "管理后台 - 荷叶老问诊迁移新增/修改 Request VO")
@Data
public class InquiryMigrationSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "3139")
    private Long id;

    @Schema(description = "机构编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "机构编码不能为空")
    private String organSign;

    @Schema(description = "名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @NotEmpty(message = "名称不能为空")
    private String name;

    /**
     * 迁移后门店id
     */
    private Long tenantId;

    @Schema(description = "省")
    private String province;

    @Schema(description = "省编码")
    private String provinceCode;

    @Schema(description = "市")
    private String city;

    @Schema(description = "市编码")
    private String cityCode;

    @Schema(description = "区")
    private String area;

    @Schema(description = "区编码")
    private String areaCode;

    @Schema(description = "迁移状态 0待确认 1待迁移 2迁移中 3已迁移", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "迁移状态 0待确认 1待迁移 2迁移中 3已迁移不能为空")
    private Integer status;

    @Schema(description = "迁移类型 1门店、2套餐", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "迁移类型 1门店、2套餐不能为空")
    private Integer migrationType;

    @Schema(description = "迁移计划开始时间")
    private LocalDateTime startTime;

    @Schema(description = "门店迁移完成始时间")
    private LocalDateTime storeEndTime;

    @Schema(description = "套餐迁移完成始时间")
    private LocalDateTime packageEndTime;

    @Schema(description = "门店状态 0待迁移 1失败 2成功", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "门店状态 0待迁移 1失败 2成功不能为空")
    private Integer storeStatus;

    @Schema(description = "员工状态 0待迁移 1失败 2成功", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "员工状态 0待迁移 1失败 2成功不能为空")
    private Integer employeeStatus;

    @Schema(description = "药师状态 0待迁移 1失败 2成功", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "药师状态 0待迁移 1失败 2成功不能为空")
    private Integer pharmacistStatus;

    @Schema(description = "患者状态 0待迁移 1失败 2成功", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "患者状态 0待迁移 1失败 2成功不能为空")
    private Integer patientStatus;

    @Schema(description = "门店迁移状态 0待迁移 1失败 2成功", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "门店迁移状态 0待迁移 1失败 2成功不能为空")
    private Integer storeMigrationStatus;

    @Schema(description = "套餐迁移状态 0待迁移 1失败 2成功", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "套餐迁移状态 0待迁移 1失败 2成功不能为空")
    private Integer packageMigrationStatus;

    @Schema(description = "备注", example = "你说的对")
    private String remark;

    @Schema(description = "扩展信息")
    private String ext;

}