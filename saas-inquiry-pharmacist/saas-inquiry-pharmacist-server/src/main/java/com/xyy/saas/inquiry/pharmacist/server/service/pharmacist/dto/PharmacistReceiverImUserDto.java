package com.xyy.saas.inquiry.pharmacist.server.service.pharmacist.dto;

import java.io.Serializable;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Author:chenxiaoyi
 * @Date:2025/07/28 19:55
 */
@Data
@Accessors(chain = true)
public class PharmacistReceiverImUserDto implements Serializable {


    private String imAccount;

    private String pharmacistName;

    private String patientName;

}
