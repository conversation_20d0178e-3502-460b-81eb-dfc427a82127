package com.xyy.saas.inquiry.pharmacist.server.controller.admin.migration;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import cn.iocoder.yudao.framework.idempotent.core.annotation.Idempotent;
import cn.iocoder.yudao.framework.idempotent.core.keyresolver.impl.UserIdempotentKeyResolver;
import com.xyy.saas.inquiry.enums.migration.MigrationStatusEnum;
import com.xyy.saas.inquiry.pharmacist.server.controller.admin.migration.vo.InquiryMigrationBatchReqVO;
import com.xyy.saas.inquiry.pharmacist.server.controller.admin.migration.vo.InquiryMigrationImportReqDto;
import com.xyy.saas.inquiry.pharmacist.server.controller.admin.migration.vo.InquiryMigrationPageReqVO;
import com.xyy.saas.inquiry.pharmacist.server.controller.admin.migration.vo.InquiryMigrationResp1VO;
import com.xyy.saas.inquiry.pharmacist.server.controller.admin.migration.vo.InquiryMigrationResp2VO;
import com.xyy.saas.inquiry.pharmacist.server.controller.admin.migration.vo.InquiryMigrationRespVO;
import com.xyy.saas.inquiry.pharmacist.server.dal.dataobject.migration.InquiryMigrationDO;
import com.xyy.saas.inquiry.pharmacist.server.service.migration.InquiryMigrationService;
import com.xyy.saas.inquiry.pharmacist.server.service.migration.MigrationCoreService;
import com.xyy.saas.inquiry.pojo.excel.ImportResultDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.io.IOException;
import java.util.List;
import java.util.Objects;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


@Tag(name = "管理后台 - 荷叶老问诊迁移")
@RestController
@RequestMapping("/pharmacist/inquiry-migration")
@Validated
public class InquiryMigrationController {

    @Resource
    private InquiryMigrationService inquiryMigrationService;

    @Resource
    private MigrationCoreService migrationCoreService;

    @GetMapping("/test-organ")
    @PermitAll
    public CommonResult<Boolean> testInquiryMigrationOrganSign(String organSign) {
        migrationCoreService.migration(organSign);
        return success(true);
    }


    @GetMapping("/page")
    @Operation(summary = "获得荷叶老问诊迁移分页")
    @PreAuthorize("@ss.hasPermission('saas:inquiry-migration')")
    public CommonResult<PageResult<InquiryMigrationRespVO>> getInquiryMigrationPage(@Valid InquiryMigrationPageReqVO pageReqVO) {
        PageResult<InquiryMigrationDO> pageResult = inquiryMigrationService.getInquiryMigrationPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, InquiryMigrationRespVO.class));
    }

    @PostMapping("/export-excel")
    @Operation(summary = "导出迁移记录 Excel")
    @PreAuthorize("@ss.hasPermission('saas:inquiry-migration')")
    @ApiAccessLog(operateType = EXPORT)
    @Idempotent(timeout = 5) // 参数维度锁住5s
    public void exportInquiryPrescriptionExcel(@Valid @RequestBody InquiryMigrationPageReqVO pageReqVO,
        HttpServletResponse response) throws IOException {

        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);

        List<InquiryMigrationDO> list = inquiryMigrationService.getInquiryMigrationPage(pageReqVO).getList();

        if (Objects.equals(pageReqVO.getStatus(), MigrationStatusEnum.PENDING_CONFIRMATION.getCode())) {
            ExcelUtils.write(response, "待确认迁移记录.xls", MigrationStatusEnum.PENDING_CONFIRMATION.getDesc(), InquiryMigrationResp1VO.class,
                BeanUtils.toBean(list, InquiryMigrationResp1VO.class));
            return;
        }

        ExcelUtils.write(response, "已迁移记录.xls", MigrationStatusEnum.MIGRATED.getDesc(), InquiryMigrationResp2VO.class,
            BeanUtils.toBean(list, InquiryMigrationResp2VO.class));
    }


    @PostMapping("/delete")
    @Operation(summary = "删除荷叶老问诊迁移-支持批量")
    @PreAuthorize("@ss.hasPermission('saas:inquiry-migration')")
    public CommonResult<Boolean> deleteInquiryMigration(@RequestBody InquiryMigrationBatchReqVO batchReqVO) {
        inquiryMigrationService.deleteInquiryMigration(batchReqVO.getIds());
        return success(true);
    }

    @PostMapping("/batch-import")
    @Operation(summary = "批量开通、编辑门店+开套餐")
    @PreAuthorize("@ss.hasPermission('saas:inquiry-migration')")
    @Idempotent(timeout = 30, keyResolver = UserIdempotentKeyResolver.class, message = "请求繁忙,每次导入需间隔30秒再操作") // user维度锁住 默认30s
    public CommonResult<ImportResultDto> batchImportMigration(@RequestBody @Valid InquiryMigrationImportReqDto importReqDto) {
        return success(inquiryMigrationService.batchImportMigration(importReqDto));
    }


    @PostMapping("/migration")
    @Operation(summary = "迁移门店-支持批量")
    @PreAuthorize("@ss.hasPermission('saas:inquiry-migration')")
    @Idempotent(timeout = 10, keyResolver = UserIdempotentKeyResolver.class, message = "请求繁忙,每次操作需间隔10秒再操作") // user维度锁住 默认10s
    public CommonResult<Boolean> batchInquiryMigration(@RequestBody @Valid InquiryMigrationBatchReqVO batchReqVO) {
        inquiryMigrationService.batchInquiryMigration(batchReqVO);
        return success(true);
    }


    @PostMapping("/re-migration")
    @Operation(summary = "重迁门店/套餐-支持批量")
    @PreAuthorize("@ss.hasPermission('saas:inquiry-migration')")
    @Idempotent(timeout = 10, keyResolver = UserIdempotentKeyResolver.class, message = "请求繁忙,每次操作需间隔10秒再操作") // user维度锁住 默认10s
    public CommonResult<Boolean> reInquiryMigration(@RequestBody @Valid InquiryMigrationBatchReqVO batchReqVO) {
        inquiryMigrationService.batchReInquiryMigration(batchReqVO);
        return success(true);
    }


    @PutMapping("/invalidate")
    @Operation(summary = "作废迁移-单个")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('saas:inquiry-migration')")
    public CommonResult<Boolean> invalidateInquiryMigration(@RequestParam("id") Long id) {
        inquiryMigrationService.invalidateInquiryMigration(id);
        return success(true);
    }


}