package com.xyy.saas.inquiry.pharmacist.server.controller.app.audit.vo;

import com.xyy.saas.inquiry.enums.prescription.PrescriptionAuditWayTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Description: 审方类别请求参数
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class InquiryPrescriptionAuditWayReqVO implements Serializable {

    @Schema(description = "处方编号")
    @NotEmpty
    private String pref;

    /**
     * {@link PrescriptionAuditWayTypeEnum}
     */
    @Schema(description = "审方类别")
    @NotNull
    private Integer auditWayType;
}
