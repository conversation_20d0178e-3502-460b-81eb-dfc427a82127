package com.xyy.saas.inquiry.pharmacist.server.controller.admin.migration.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.xyy.saas.inquiry.pojo.excel.ImportExcelVoDto;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Getter;

/**
 * @Author:chenxia<PERSON>i
 * @Date:2025/01/22 15:49
 */
@Getter
public class InquiryMigrationExcelVO extends ImportExcelVoDto {

    @ExcelProperty(value = "门店ID", index = 0)
    @Size(max = 64, message = "门店ID超出最大长度64限制")
    @NotBlank(message = "门店ID不能为空")
    private String organSign;

    @ExcelProperty(value = "门店名", index = 1)
    @Size(max = 64, message = "门店名超出最大长度64限制")
    @NotBlank(message = "门店名不能为空")
    private String name;


    public void setOrganSign(String organSign) {
        this.organSign = organSign;
    }

    public void setName(String name) {
        this.name = name;
    }
}
