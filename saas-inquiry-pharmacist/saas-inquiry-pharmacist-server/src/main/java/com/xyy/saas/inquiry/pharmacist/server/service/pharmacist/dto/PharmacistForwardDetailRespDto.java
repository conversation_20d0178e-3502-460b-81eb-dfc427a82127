package com.xyy.saas.inquiry.pharmacist.server.service.pharmacist.dto;

import com.xyy.saas.inquiry.pojo.forward.ForwardCaInfo;
import com.xyy.saas.inquiry.pojo.forward.ForwardPersonInfo;
import java.io.Serializable;
import lombok.Data;

@Data
public class PharmacistForwardDetailRespDto implements Serializable {

    /**
     * 药师详情
     */
    private PharmacistForwardDetailDto dto;
    /**
     * ca信息
     */
    private ForwardCaInfo caInfo;
    /**
     * person信息
     */
    private ForwardPersonInfo personInfo;

}
