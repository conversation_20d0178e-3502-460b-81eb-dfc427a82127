package com.xyy.saas.inquiry.pharmacist.server.controller.app.audit.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 处方审核记录 Response VO")
@Data
@ExcelIgnoreUnannotated
public class InquiryPrescriptionAuditRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "11803")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "处方编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("处方编号")
    private String pref;

    @Schema(description = "审核人类型 1:医生 2:平台药师 3:药店药师", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("审核人类型 1:医生 2:平台药师 3:药店药师")
    private Integer auditorType;

    @Schema(description = "记录审批级数")
    @ExcelProperty("记录审批级数")
    private Integer auditLevel;

    @Schema(description = "审核状态 1:待审核 2:审核通过 3:审核驳回", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("审核状态 1:待审核 2:审核通过 3:审核驳回")
    private Integer auditStatus;

    @Schema(description = "审核人(药师)id", example = "28777")
    @ExcelProperty("审核人(药师)id")
    private Long auditorId;

    @Schema(description = "审核人(药师)名称", example = "芋艿")
    @ExcelProperty("审核人(药师)名称")
    private String auditorName;

    @Schema(description = "审方类型 1:图文 2:视频 3:电话", example = "1")
    @ExcelProperty("审方类型 1:图文 2:视频 3:电话")
    private Integer auditApprovalType;

    @Schema(description = "领单时间")
    @ExcelProperty("领单时间")
    private LocalDateTime auditorReceiveTime;

    @Schema(description = "药师审批时间")
    @ExcelProperty("药师审批时间")
    private LocalDateTime auditorApprovalTime;

    @Schema(description = "药师驳回原因", example = "不喜欢")
    @ExcelProperty("药师驳回原因")
    private String auditorRejectedReason;

    @Schema(description = "药师签名")
    @ExcelProperty("药师签名")
    private String auditorCaSign;

    @Schema(description = "药师签名图片", example = "https://www.iocoder.cn")
    @ExcelProperty("药师签名图片")
    private String auditorSignImgUrl;

    @Schema(description = "签章状态 1:待签章 2:发起签章 3:已签章", example = "1")
    @ExcelProperty("签章状态 1:待签章 2:发起签章 3:已签章")
    private Integer signatureStatus;

    @Schema(description = "审核人 (药师)发起签名时间")
    @ExcelProperty("审核人 (药师)发起签名时间")
    private LocalDateTime auditorSignatureTime;

    @Schema(description = "审核人 (药师)签名回调时间")
    @ExcelProperty("审核人 (药师)签名回调时间")
    private LocalDateTime auditorCallbackTime;

    @Schema(description = "审方端客户端类型 0、app  1、pc  2、小程序 ", example = "2")
    @ExcelProperty("审方端客户端类型 0、app  1、pc  2、小程序 ")
    private Integer clientChannelType;

    @Schema(description = "审核 (药师)端操作系统类型 前端传入", example = "1")
    @ExcelProperty("审核 (药师)端操作系统类型 前端传入")
    private String clientOsType;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}