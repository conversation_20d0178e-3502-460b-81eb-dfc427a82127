package com.xyy.saas.inquiry.pharmacist.server.controller.admin.migration.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.xyy.saas.inquiry.enums.migration.MigrationActionEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;
import jakarta.validation.constraints.AssertTrue;
import lombok.Data;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

@Schema(description = "管理后台 - 荷叶老问诊迁移 批量操作 VO")
@Data
@ToString(callSuper = true)
public class InquiryMigrationBatchReqVO {

    @Schema(description = "门店迁移ids")
    private List<Long> ids;


    @Schema(description = "迁移类型 1门店、2套餐", example = "2")
    private Integer migrationType;


    /**
     * {@link  MigrationActionEnum}
     */
    @Schema(description = "迁移类型 1立即迁移 2生成迁移计划", example = "2")
    private Integer migrationAction;


    @Schema(description = "计划开始迁移时间", example = "2")
    private String startTime;

    public LocalDateTime getStartTime() {
        return StringUtils.isBlank(startTime) ? null : LocalDateTime.parse(startTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }

    // 校验方法：如果迁移类型是2，开始时间不能为空且必须大于当前时间
    @AssertTrue(message = "计划开始迁移时间不能为空且必须大于当前时间")
    @JsonIgnore
    public boolean isStartTimeValid() {
        // 如果不是生成迁移计划（migrationAction != 2），则跳过校验
        if (migrationAction == null || !Objects.equals(MigrationActionEnum.GENERATE_MIGRATION_PLAN.getCode(), migrationAction)) {
            return true;
        }

        // 如果是生成迁移计划，检查startTime是否为空且大于当前时间
        return getStartTime() != null && getStartTime().isAfter(LocalDateTime.now());
    }
}