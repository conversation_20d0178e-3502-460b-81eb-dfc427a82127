package com.xyy.saas.inquiry.pharmacist.server.controller.admin.migration.vo;

import cn.iocoder.yudao.framework.excel.core.annotations.DictFormat;
import cn.iocoder.yudao.framework.excel.core.convert.DictConvert;
import cn.iocoder.yudao.module.system.enums.DictTypeConstants;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.Data;

@Data
@ExcelIgnoreUnannotated
public class InquiryMigrationResp1VO {

    @Schema(description = "机构编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("门店ID")
    private String organSign;

    @Schema(description = "门店名", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @ExcelProperty("门店名")
    private String name;

    @Schema(description = "省")
    @ExcelProperty("省")
    private String province;

    @Schema(description = "市")
    @ExcelProperty("市")
    private String city;

    @Schema(description = "区")
    @ExcelProperty("区")
    private String area;

    @Schema(description = "迁移状态 0待确认 1待迁移 2迁移中 3已迁移", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty(value = "迁移状态", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.MIGRATION_STATUS)
    private Integer status;

    @Schema(description = "备注", example = "你说的对")
    @ExcelProperty("待确认原因")
    private String remark;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("导入时间")
    private LocalDateTime createTime;

}