package com.xyy.saas.inquiry.pharmacist.server.service.pharmacist;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantUserRelationDto;
import com.xyy.saas.inquiry.pharmacist.server.controller.admin.pharmacist.vo.InquiryPharmacistPageReqVO;
import com.xyy.saas.inquiry.pharmacist.server.controller.admin.pharmacist.vo.InquiryPharmacistRespVO;
import com.xyy.saas.inquiry.pharmacist.server.controller.admin.pharmacist.vo.InquiryPharmacistSaveReqVO;
import com.xyy.saas.inquiry.pharmacist.server.controller.admin.pharmacist.vo.InquiryPharmacistUpdateStatusReqVO;
import com.xyy.saas.inquiry.pharmacist.server.controller.app.pharmacist.vo.PharmacistUpdateInfoVo;
import com.xyy.saas.inquiry.pharmacist.server.dal.dataobject.pharmacist.InquiryPharmacistDO;
import com.xyy.saas.inquiry.pharmacist.server.service.pharmacist.dto.PharmacistQueryDto;
import jakarta.validation.Valid;
import java.util.List;

/**
 * 药师信息 Service 接口
 *
 * <AUTHOR>
 */
public interface InquiryPharmacistService {

    /**
     * 创建药师信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    InquiryPharmacistDO createInquiryPharmacistSystem(InquiryPharmacistSaveReqVO createReqVO);

    /**
     * 更新药师信息
     *
     * @param updateReqVO 更新信息
     */
    void updateInquiryPharmacistSystem(@Valid InquiryPharmacistSaveReqVO updateReqVO);

    /**
     * 删除药师信息
     *
     * @param id 编号
     */
    void deleteInquiryPharmacist(Long id);


    /**
     * 校验药师信息是否存在
     *
     * @param id 药师id
     * @return 药师DO
     */
    InquiryPharmacistDO validateInquiryPharmacistExists(Long id);

    /**
     * 获得药师信息
     *
     * @param id 编号
     * @return 药师信息
     */
    InquiryPharmacistRespVO getInquiryPharmacistVo(Long id);


    InquiryPharmacistDO getInquiryPharmacistById(Long id);

    /**
     * 根据手机号获取药师信息
     *
     * @param mobile
     * @return
     */
    InquiryPharmacistRespVO getInquiryPharmacistVoByMobile(String mobile);

    /**
     * 获得药师信息分页 - 系统
     *
     * @param pageReqVO 分页查询
     * @return 药师信息分页
     */
    PageResult<InquiryPharmacistRespVO> pagePharmacistSystem(InquiryPharmacistPageReqVO pageReqVO);

    /**
     * 审核药师信息
     *
     * @param updateReqVO 状态VO
     */
    void auditInquiryPharmacist(InquiryPharmacistUpdateStatusReqVO updateReqVO);


    /**
     * 获得药师信息分页 - 门店
     *
     * @param pageReqVO 分页查询
     * @return 药师信息分页
     */
    PageResult<InquiryPharmacistRespVO> pagePharmacistStore(InquiryPharmacistPageReqVO pageReqVO);

    /**
     * 创建药师信息-申请
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createInquiryPharmacistStore(InquiryPharmacistSaveReqVO createReqVO);

    /**
     * 更新药师信息-门店
     *
     * @param updateReqVO 更新信息
     */
    void updateInquiryPharmacistStore(InquiryPharmacistSaveReqVO updateReqVO);

    /**
     * 获取药师绑定门店列表
     *
     * @param pageReqVO 请求vo
     * @return 门店分页列表
     */
    // PageResult<TenantDto> pagePharmacistBindStore(InquiryPharmacistPageReqVO pageReqVO);

    /**
     * 根据药师编码查询药师信息
     *
     * @param pref
     * @return
     */
    InquiryPharmacistDO getPharmacistByPref(String pref);

    /**
     * 根据userid查询药师信息
     *
     * @param userId
     * @return
     */
    InquiryPharmacistDO getPharmacistByUserId(Long userId);

    /**
     * 根据手机号查询药师信息
     *
     * @param userId
     * @return
     */
    InquiryPharmacistDO getPharmacistByMobile(String mobile);

    /**
     * 根据userid查询药师信息
     *
     * @param userId
     * @return
     */
    InquiryPharmacistDO getRequiredPharmacistByUserId(Long userId);

    /**
     * 根据药师编码查询药师信息
     *
     * @param userId getRequiredPharmacistByUserId
     * @return
     */
    InquiryPharmacistDO getRequiredApprovedPharmacistByUserId(Long userId);

    /**
     * 查询在线药师信息
     *
     * @param userId userId
     * @return
     */
    InquiryPharmacistDO getRequiredOnLinePharmacistByUserId(Long userId);

    /**
     * 获取药师的绑定门店列表
     *
     * @return
     */
    List<TenantUserRelationDto> getPharmacistBindStoreList();

    /**
     * 查询药师列表
     *
     * @param qualifications
     * @return
     */
    List<InquiryPharmacistDO> getPharmacistList(PharmacistQueryDto qualifications);

    /**
     * 药师出诊
     */
    boolean startReceipt();

    /**
     * 药师停诊
     */
    boolean stopReceipt();

    /**
     * 获取当前登录用户药师信息
     *
     * @return 药师信息
     */
    InquiryPharmacistRespVO getPharmacistInfo();

    /**
     * 药师账号注销前检查
     *
     * @return 校验结果
     */
    Boolean logOffCheck();

    /**
     * 跟新药师信息
     *
     * @param updateInfoVo
     * @return
     */
    Boolean updateInquiryPharmacistInfo(PharmacistUpdateInfoVo updateInfoVo);

}