package com.xyy.saas.inquiry.pharmacist.server.controller.admin.migration.vo;

import cn.iocoder.yudao.framework.excel.core.annotations.DictFormat;
import cn.iocoder.yudao.framework.excel.core.convert.DictConvert;
import cn.iocoder.yudao.module.system.enums.DictTypeConstants;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.Data;

@Data
@ExcelIgnoreUnannotated
public class InquiryMigrationResp2VO {

    @Schema(description = "机构编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("门店ID")
    private String organSign;

    @Schema(description = "门店名", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @ExcelProperty("门店名")
    private String name;

    @Schema(description = "省")
    @ExcelProperty("省")
    private String province;

    @Schema(description = "市")
    @ExcelProperty("市")
    private String city;

    @Schema(description = "区")
    @ExcelProperty("区")
    private String area;

    @ExcelProperty(value = "门店迁移", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.MIGRATION_POINT_STATUS)
    private Integer storeStatus;

    @ExcelProperty(value = "员工迁移", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.MIGRATION_POINT_STATUS)
    private Integer employeeStatus;

    @ExcelProperty(value = "药师迁移", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.MIGRATION_POINT_STATUS)
    private Integer pharmacistStatus;

    @ExcelProperty(value = "患者迁移", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.MIGRATION_POINT_STATUS)
    private Integer patientStatus;

    @ExcelProperty(value = "门店迁移状态", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.MIGRATION_POINT_STATUS)
    private Integer storeMigrationStatus;

    @ExcelProperty(value = "套餐迁移状态", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.MIGRATION_POINT_STATUS)
    private Integer packageMigrationStatus;

    @ExcelProperty("门店迁移时间")
    private LocalDateTime storeEndTime;

    @ExcelProperty("套餐迁移时间")
    private LocalDateTime packageEndTime;

}