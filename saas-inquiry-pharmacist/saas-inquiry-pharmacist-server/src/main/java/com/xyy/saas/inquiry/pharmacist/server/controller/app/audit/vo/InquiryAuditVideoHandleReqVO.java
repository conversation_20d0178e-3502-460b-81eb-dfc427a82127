package com.xyy.saas.inquiry.pharmacist.server.controller.app.audit.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.io.Serializable;

/**
 * @Author: xucao
 * @DateTime: 2025/7/25 18:33
 * @Description: 远程审方视频请求处理回执保存参数
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class InquiryAuditVideoHandleReqVO implements Serializable {

    @Schema(description = "处方编号")
    private String pref;

    @Schema(description = "处理状态 1:接听 2:挂断")
    private Integer handleStatus;

    @Schema(description = "视频发起方动作 1:发起 2:取消")
    private Integer startActionType;
}
