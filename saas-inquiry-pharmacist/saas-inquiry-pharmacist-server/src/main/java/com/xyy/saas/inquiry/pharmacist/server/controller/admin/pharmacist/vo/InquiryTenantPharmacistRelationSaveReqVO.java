package com.xyy.saas.inquiry.pharmacist.server.controller.admin.pharmacist.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Schema(description = "管理后台 - 门店药师关系新增/修改 Request VO")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InquiryTenantPharmacistRelationSaveReqVO {

    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "2618")
    private Long id;

    @Schema(description = "门店ID", example = "31948")
    private Long tenantId;

    @Schema(description = "药师ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "31948")
    private Long pharmacistId;

    @Schema(description = "药师状态 0启用 1禁用", example = "1")
    private Integer status;

    @Schema(description = "门店IDs")
    private List<Long> tenantIds;


}