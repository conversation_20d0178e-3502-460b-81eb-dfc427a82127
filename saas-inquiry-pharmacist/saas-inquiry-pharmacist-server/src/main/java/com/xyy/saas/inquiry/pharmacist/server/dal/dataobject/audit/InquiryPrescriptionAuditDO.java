package com.xyy.saas.inquiry.pharmacist.server.dal.dataobject.audit;

import cn.iocoder.yudao.framework.tenant.core.db.TenantBaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xyy.saas.inquiry.enums.doctor.AuditorTypeEnum;
import com.xyy.saas.inquiry.enums.prescription.PrescriptionAuditStatusEnum;
import com.xyy.saas.inquiry.enums.prescription.PrescriptionAuditWayTypeEnum;
import com.xyy.saas.inquiry.enums.signature.SignatureStatusEnum;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 处方审核记录 DO
 *
 * <AUTHOR>
 */
@TableName("saas_inquiry_prescription_audit")
@KeySequence("saas_inquiry_prescription_audit_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class InquiryPrescriptionAuditDO extends TenantBaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 处方编号
     */
    private String pref;
    /**
     * 审核人类型 1:医生 2:平台药师 3:药店药师 {@link AuditorTypeEnum}
     */
    private Integer auditorType;
    /**
     * 记录审批级数
     */
    private Integer auditLevel;
    /**
     * 审核状态 1:待审核 2:审核通过 3:审核驳回 {@link PrescriptionAuditStatusEnum}
     */
    private Integer auditStatus;
    /**
     * 审核人(药师)userId
     */
    private Long auditorId;
    /**
     * 审核人(药师)名称
     */
    private String auditorName;
    /**
     * 审方类型 1:图文 2:视频 3:电话 {@link com.xyy.saas.inquiry.enums.inquiry.InquiryWayTypeEnum}
     */
    private Integer auditApprovalType;

    /**
     * 审方方式类别 1、荷叶审方  2、远程审方 {@link PrescriptionAuditWayTypeEnum}
     */
    private Integer auditWayType;
    /**
     * 领单时间
     */
    private LocalDateTime auditorReceiveTime;
    /**
     * 药师审批时间
     */
    private LocalDateTime auditorApprovalTime;
    /**
     * 药师驳回原因
     */
    private String auditorRejectedReason;
    /**
     * 药师签名
     */
    private String auditorCaSign;
    /**
     * 药师签名图片
     */
    private String auditorSignImgUrl;
    /**
     * 签章状态 1:待签章 2:发起签章 3:已签章 {@link SignatureStatusEnum}
     */
    private Integer signatureStatus;
    /**
     * 审核人 (药师)发起签名时间
     */
    private LocalDateTime auditorSignatureTime;
    /**
     * 审核人 (药师)签名回调时间
     */
    private LocalDateTime auditorCallbackTime;
    /**
     * 审方端客户端类型 0、app  1、pc  2、小程序 {@link com.xyy.saas.inquiry.enums.inquiry.ClientChannelTypeEnum}
     */
    private Integer clientChannelType;
    /**
     * 审核 (药师)端操作系统类型 前端传入
     */
    private String clientOsType;

}