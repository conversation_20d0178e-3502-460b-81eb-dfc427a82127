package com.xyy.saas.inquiry.pharmacist.server.dal.mysql.migration;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xyy.saas.inquiry.enums.migration.MigrationStatusEnum;
import com.xyy.saas.inquiry.pharmacist.server.controller.admin.migration.vo.InquiryMigrationPageReqVO;
import com.xyy.saas.inquiry.pharmacist.server.dal.dataobject.migration.InquiryMigrationDO;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import java.util.Objects;
import org.apache.ibatis.annotations.Mapper;
import org.jetbrains.annotations.NotNull;

/**
 * 荷叶老问诊迁移 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InquiryMigrationMapper extends BaseMapperX<InquiryMigrationDO> {

    default PageResult<InquiryMigrationDO> selectPage(InquiryMigrationPageReqVO reqVO) {
        LambdaQueryWrapperX<InquiryMigrationDO> queryWrapper = getQueryWrapperX(reqVO);
        return selectPage(reqVO, queryWrapper);
    }

    default List<InquiryMigrationDO> queryList(InquiryMigrationPageReqVO reqVO) {
        LambdaQueryWrapperX<InquiryMigrationDO> queryWrapper = getQueryWrapperX(reqVO);
        return selectList(queryWrapper);
    }

    @NotNull
    private static LambdaQueryWrapperX<InquiryMigrationDO> getQueryWrapperX(InquiryMigrationPageReqVO reqVO) {
        LambdaQueryWrapperX<InquiryMigrationDO> queryWrapper = new LambdaQueryWrapperX<InquiryMigrationDO>()
            .eqIfPresent(InquiryMigrationDO::getOrganSign, reqVO.getOrganSign())
            .inIfPresent(InquiryMigrationDO::getOrganSign, reqVO.getOrganSigns())
            .inIfPresent(InquiryMigrationDO::getId, reqVO.getIds())
            .likeIfPresent(InquiryMigrationDO::getName, reqVO.getName())
            .eqIfPresent(InquiryMigrationDO::getProvince, reqVO.getProvince())
            .eqIfPresent(InquiryMigrationDO::getProvinceCode, reqVO.getProvinceCode())
            .eqIfPresent(InquiryMigrationDO::getCity, reqVO.getCity())
            .eqIfPresent(InquiryMigrationDO::getCityCode, reqVO.getCityCode())
            .eqIfPresent(InquiryMigrationDO::getArea, reqVO.getArea())
            .eqIfPresent(InquiryMigrationDO::getAreaCode, reqVO.getAreaCode())
            .eqIfPresent(InquiryMigrationDO::getStatus, reqVO.getStatus())
            .eqIfPresent(InquiryMigrationDO::getMigrationType, reqVO.getMigrationType())
            .betweenIfPresent(InquiryMigrationDO::getStartTime, reqVO.getStartTime())
            .betweenIfPresent(InquiryMigrationDO::getStoreEndTime, reqVO.getStoreEndTime())
            .betweenIfPresent(InquiryMigrationDO::getPackageEndTime, reqVO.getPackageEndTime())
            .eqIfPresent(InquiryMigrationDO::getStoreStatus, reqVO.getStoreStatus())
            .eqIfPresent(InquiryMigrationDO::getEmployeeStatus, reqVO.getEmployeeStatus())
            .eqIfPresent(InquiryMigrationDO::getPharmacistStatus, reqVO.getPharmacistStatus())
            .eqIfPresent(InquiryMigrationDO::getPatientStatus, reqVO.getPatientStatus())
            .eqIfPresent(InquiryMigrationDO::getStoreMigrationStatus, reqVO.getStoreMigrationStatus())
            .eqIfPresent(InquiryMigrationDO::getPackageMigrationStatus, reqVO.getPackageMigrationStatus())
            .eqIfPresent(InquiryMigrationDO::getRemark, reqVO.getRemark())
            .eqIfPresent(InquiryMigrationDO::getExt, reqVO.getExt())
            .betweenIfPresent(InquiryMigrationDO::getCreateTime, reqVO.getCreateTime());

        if (Objects.equals(MigrationStatusEnum.PENDING_CONFIRMATION.getCode(), reqVO.getStatus())) {
            queryWrapper.orderByDesc(InquiryMigrationDO::getCreateTime)
                .orderByDesc(InquiryMigrationDO::getId);  // 添加次要排序条件;
        } else if (Objects.equals(MigrationStatusEnum.PENDING_MIGRATION.getCode(), reqVO.getStatus())) {
            queryWrapper.orderByAsc(InquiryMigrationDO::getStartTime)
                .orderByAsc(InquiryMigrationDO::getId); // 添加次要排序条件;
        } else {
            // 已迁移-根据修改时间倒排
            queryWrapper.orderByDesc(InquiryMigrationDO::getUpdateTime)
                .orderByDesc(InquiryMigrationDO::getId);  // 添加次要排序条件;
        }
        return queryWrapper;
    }

    default int countPendingMigrations(LocalDateTime startTime) {

        LocalDateTime startOfDay = startTime.toLocalDate().atStartOfDay();
        LocalDateTime endOfDay = startTime.toLocalDate().atTime(LocalTime.MAX);

        return Math.toIntExact(selectCount(new LambdaQueryWrapperX<InquiryMigrationDO>()
            .eq(InquiryMigrationDO::getStatus, MigrationStatusEnum.PENDING_MIGRATION.getCode())
            .ge(InquiryMigrationDO::getStartTime, startOfDay)
            .lt(InquiryMigrationDO::getStartTime, endOfDay)));
    }


    default List<InquiryMigrationDO> selectMigrationList(Integer status, LocalDateTime startTime) {
        LocalDate date = startTime.toLocalDate();
        LocalDateTime startOfDay = date.atStartOfDay();
        LocalDateTime endOfDay = date.atTime(LocalTime.MAX);
        // 要使用 >= 和 < 否则会查出第二天0点的数据
        return selectList(new LambdaQueryWrapperX<InquiryMigrationDO>()
            .eq(InquiryMigrationDO::getStatus, status)
            .ge(InquiryMigrationDO::getStartTime, startOfDay)
            .lt(InquiryMigrationDO::getStartTime, endOfDay));
    }
}