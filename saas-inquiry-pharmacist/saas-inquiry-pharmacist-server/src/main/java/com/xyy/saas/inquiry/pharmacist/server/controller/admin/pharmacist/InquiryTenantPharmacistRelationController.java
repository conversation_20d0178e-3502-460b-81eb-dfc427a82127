//package com.xyy.saas.inquiry.pharmacist.server.controller.admin.pharmacist;
//
//import cn.iocoder.yudao.framework.common.pojo.CommonResult;
//import cn.iocoder.yudao.framework.tenant.core.aop.TenantIgnore;
//import com.xyy.saas.inquiry.pharmacist.server.controller.admin.pharmacist.vo.InquiryTenantPharmacistRelationBindReqVO;
//import com.xyy.saas.inquiry.pharmacist.server.controller.admin.pharmacist.vo.InquiryTenantPharmacistRelationSaveReqVO;
//import com.xyy.saas.inquiry.pharmacist.server.service.pharmacist.InquiryTenantPharmacistRelationService;
//import io.swagger.v3.oas.annotations.Operation;
//import io.swagger.v3.oas.annotations.tags.Tag;
//import jakarta.annotation.Resource;
//import jakarta.validation.Valid;
//import org.springframework.security.access.prepost.PreAuthorize;
//import org.springframework.validation.annotation.Validated;
//import org.springframework.web.bind.annotation.*;
//
//import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
//
//
//@Tag(name = "管理后台 - 门店药师关系")
//@RestController
//@RequestMapping("/pharmacist/inquiry-tenant-pharmacist-relation")
//@Validated
//public class InquiryTenantPharmacistRelationController {
//
//    @Resource
//    private InquiryTenantPharmacistRelationService inquiryTenantPharmacistRelationService;
//
//
//    @PutMapping("/store/update")
//    @Operation(summary = "更新门店药师状态")
//    @PreAuthorize("@ss.hasPermission('saas:inquiry-pharmacist:update')")
//    public CommonResult<Boolean> updateInquiryTenantPharmacistRelation(@Valid @RequestBody InquiryTenantPharmacistRelationSaveReqVO updateReqVO) {
//        inquiryTenantPharmacistRelationService.updateInquiryTenantPharmacistRelation(updateReqVO);
//        return success(true);
//    }
//
//
//    @PostMapping("/bind")
//    @Operation(summary = "创建门店药师关系")
//    @PreAuthorize("@ss.hasPermission('saas:inquiry-tenant-pharmacist-relation:bind')")
//    @TenantIgnore
//    public CommonResult<Boolean> bindInquiryTenantPharmacistRelation(@Valid  @RequestBody InquiryTenantPharmacistRelationBindReqVO bindReqVO) {
//        inquiryTenantPharmacistRelationService.bindInquiryTenantPharmacistRelation(bindReqVO);
//        return success(true);
//    }
//
//    @PostMapping("/unbind")
//    @Operation(summary = "解绑门店药师关系")
//    @PreAuthorize("@ss.hasPermission('saas:inquiry-tenant-pharmacist-relation:unbind')")
//    @TenantIgnore
//    public CommonResult<Boolean> unbindInquiryTenantPharmacistRelation(@Valid  @RequestBody InquiryTenantPharmacistRelationBindReqVO bindReqVO) {
//        inquiryTenantPharmacistRelationService.unbindInquiryTenantPharmacistRelation(bindReqVO);
//        return success(true);
//    }
//
//
////    @GetMapping("/store/page")
////    @Operation(summary = "获得门店药师关系分页- 门店")
////    @PreAuthorize("@ss.hasPermission('saas:inquiry-tenant-pharmacist-relation:query')")
////    public CommonResult<PageResult<InquiryTenantPharmacistRelationRespVO>> getInquiryTenantPharmacistRelationPage(@Valid InquiryTenantPharmacistRelationPageReqVO pageReqVO) {
////        PageResult<InquiryTenantPharmacistRelationDO> pageResult = inquiryTenantPharmacistRelationService.getInquiryTenantPharmacistRelationPage(pageReqVO);
////        return success(BeanUtils.toBean(pageResult, InquiryTenantPharmacistRelationRespVO.class));
////    }
////
////    @GetMapping("/system/page")
////    @Operation(summary = "获得门店药师关系分页 - 超管")
////    @PreAuthorize("@ss.hasPermission('saas:inquiry-tenant-pharmacist-relation:query')")
////    public CommonResult<PageResult<InquiryTenantPharmacistRelationRespVO>> getInquiryTenantPharmacistRelationPage(@Valid InquiryTenantPharmacistRelationPageReqVO pageReqVO) {
////        PageResult<InquiryTenantPharmacistRelationDO> pageResult = inquiryTenantPharmacistRelationService.getInquiryTenantPharmacistRelationPage(pageReqVO);
////        return success(BeanUtils.toBean(pageResult, InquiryTenantPharmacistRelationRespVO.class));
////    }
//
////
////    @PutMapping("/update")
////    @Operation(summary = "更新门店药师关系")
////    @PreAuthorize("@ss.hasPermission('saas:inquiry-tenant-pharmacist-relation:update')")
////    public CommonResult<Boolean> updateInquiryTenantPharmacistRelation(@Valid @RequestBody InquiryTenantPharmacistRelationSaveReqVO updateReqVO) {
////        inquiryTenantPharmacistRelationService.updateInquiryTenantPharmacistRelation(updateReqVO);
////        return success(true);
////    }
////
////    @DeleteMapping("/delete")
////    @Operation(summary = "删除门店药师关系")
////    @Parameter(name = "id", description = "编号", required = true)
////    @PreAuthorize("@ss.hasPermission('saas:inquiry-tenant-pharmacist-relation:delete')")
////    public CommonResult<Boolean> deleteInquiryTenantPharmacistRelation(@RequestParam("id") Long id) {
////        inquiryTenantPharmacistRelationService.deleteInquiryTenantPharmacistRelation(id);
////        return success(true);
////    }
////
////    @GetMapping("/get")
////    @Operation(summary = "获得门店药师关系")
////    @Parameter(name = "id", description = "编号", required = true, example = "1024")
////    @PreAuthorize("@ss.hasPermission('saas:inquiry-tenant-pharmacist-relation:query')")
////    public CommonResult<InquiryTenantPharmacistRelationRespVO> getInquiryTenantPharmacistRelation(@RequestParam("id") Long id) {
////        InquiryTenantPharmacistRelationDO inquiryTenantPharmacistRelation = inquiryTenantPharmacistRelationService.getInquiryTenantPharmacistRelation(id);
////        return success(BeanUtils.toBean(inquiryTenantPharmacistRelation, InquiryTenantPharmacistRelationRespVO.class));
////    }
//
////    @GetMapping("/page")
////    @Operation(summary = "获得门店药师关系分页")
////    @PreAuthorize("@ss.hasPermission('saas:inquiry-tenant-pharmacist-relation:query')")
////    public CommonResult<PageResult<InquiryTenantPharmacistRelationRespVO>> getInquiryTenantPharmacistRelationPage(@Valid InquiryTenantPharmacistRelationPageReqVO pageReqVO) {
////        PageResult<InquiryTenantPharmacistRelationDO> pageResult = inquiryTenantPharmacistRelationService.getInquiryTenantPharmacistRelationPage(pageReqVO);
////        return success(BeanUtils.toBean(pageResult, InquiryTenantPharmacistRelationRespVO.class));
////    }
////
////    @GetMapping("/export-excel")
////    @Operation(summary = "导出门店药师关系 Excel")
////    @PreAuthorize("@ss.hasPermission('saas:inquiry-tenant-pharmacist-relation:export')")
////    @ApiAccessLog(operateType = EXPORT)
////    public void exportInquiryTenantPharmacistRelationExcel(@Valid InquiryTenantPharmacistRelationPageReqVO pageReqVO,
////              HttpServletResponse response) throws IOException {
////        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
////        List<InquiryTenantPharmacistRelationDO> list = inquiryTenantPharmacistRelationService.getInquiryTenantPharmacistRelationPage(pageReqVO).getList();
////        // 导出 Excel
////        ExcelUtils.write(response, "门店药师关系.xls", "数据", InquiryTenantPharmacistRelationRespVO.class,
////                        BeanUtils.toBean(list, InquiryTenantPharmacistRelationRespVO.class));
////    }
//
//}