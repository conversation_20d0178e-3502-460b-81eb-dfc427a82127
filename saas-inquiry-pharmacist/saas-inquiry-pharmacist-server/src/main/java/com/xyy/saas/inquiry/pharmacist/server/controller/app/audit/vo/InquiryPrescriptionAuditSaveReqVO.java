package com.xyy.saas.inquiry.pharmacist.server.controller.app.audit.vo;

import com.xyy.saas.inquiry.enums.prescription.PrescriptionAuditWayTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Schema(description = "管理后台 - 处方审核记录新增/修改 Request VO")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class InquiryPrescriptionAuditSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "11803")
    private Long id;

    @Schema(description = "处方编号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String pref;

    /**
     * 门店id 为空取当前环境门店
     */
    private Long tenantId;

    @Schema(description = "审核人类型 1:医生 2:平台药师 3:药店药师", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
//    @NotNull(message = "审核人类型 1:医生 2:平台药师 3:药店药师不能为空")
    private Integer auditorType;

    @Schema(description = "记录审批级数")
    private Integer auditLevel;

    @Schema(description = "审核状态 1:待审核 2:审核通过 3:审核驳回", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "审核状态 1:待审核 2:审核通过 3:审核驳回不能为空")
    private Integer auditStatus;

    @Schema(description = "审核人(药师)id", example = "28777")
    private Long auditorId;

    @Schema(description = "审核人(药师)名称", example = "芋艿")
    private String auditorName;

    @Schema(description = "审方类型 1:图文 2:视频 3:电话", example = "1")
    private Integer auditApprovalType;

    @Schema(description = "领单时间")
    private LocalDateTime auditorReceiveTime;

    @Schema(description = "药师审批时间")
    private LocalDateTime auditorApprovalTime;

    @Schema(description = "药师驳回原因", example = "不喜欢")
    private String auditorRejectedReason;

    @Schema(description = "药师签名")
    private String auditorCaSign;

    @Schema(description = "药师签名图片", example = "https://www.iocoder.cn")
    private String auditorSignImgUrl;

    @Schema(description = "签章状态 1:待签章 2:发起签章 3:已签章", example = "1")
    private Integer signatureStatus;

    @Schema(description = "审核人 (药师)发起签名时间")
    private LocalDateTime auditorSignatureTime;

    @Schema(description = "审核人 (药师)签名回调时间")
    private LocalDateTime auditorCallbackTime;

    @Schema(description = "审方端客户端类型 0、app  1、pc  2、小程序 ", example = "2")
    private Integer clientChannelType;

    @Schema(description = "审核 (药师)端操作系统类型 前端传入", example = "1")
    private String clientOsType;

    /**
     * {@link PrescriptionAuditWayTypeEnum}
     */
    @Schema(description = "审方方式类别 1、荷叶审方  2、远程审方", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer auditWayType;
}