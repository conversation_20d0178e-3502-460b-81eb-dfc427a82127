package com.xyy.saas.inquiry.pharmacist.server.mq.consumer.user;

import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.framework.tenant.core.util.TenantUtils;
import cn.iocoder.yudao.module.system.api.user.AdminUserApi;
import cn.iocoder.yudao.module.system.api.user.dto.AdminUserRespDTO;
import com.xyy.saas.eventbus.rocketmq.annotation.EventBusConsumer;
import com.xyy.saas.eventbus.rocketmq.annotation.EventBusListener;
import com.xyy.saas.inquiry.mq.user.UserBaseInfoChangeEvent;
import com.xyy.saas.inquiry.mq.user.UserBaseInfoDto;
import com.xyy.saas.inquiry.pharmacist.server.dal.dataobject.pharmacist.InquiryPharmacistDO;
import com.xyy.saas.inquiry.pharmacist.server.dal.mysql.pharmacist.InquiryPharmacistMapper;
import com.xyy.saas.inquiry.signature.api.ca.InquirySignatureCaAuthApi;
import jakarta.annotation.Resource;
import java.util.Objects;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

/**
 * @Desc 用户信息修改,
 * <AUTHOR>
 */
@Component
@Slf4j
@EventBusConsumer(groupId = "com_xyy_saas_inquiry_pharmacist_server_mq_consumer_user_UserBaseInfoChangePharmacistMQConsumer",
    topic = UserBaseInfoChangeEvent.TOPIC)
public class UserBaseInfoChangePharmacistMQConsumer {

    @Resource
    private InquiryPharmacistMapper inquiryPharmacistMapper;

    @Resource
    private AdminUserApi adminUserApi;

    @DubboReference
    private InquirySignatureCaAuthApi inquirySignatureCaAuthApi;

    @EventBusListener
    public void pharmacistUserBaseInfoUpdateMQConsumer(UserBaseInfoChangeEvent userBaseInfoChangeEvent) {
        UserBaseInfoDto userBaseInfoDto = userBaseInfoChangeEvent.getMsg();
        if (userBaseInfoDto == null || userBaseInfoDto.getUserId() == null) {
            return;
        }
        InquiryPharmacistDO pharmacistDO = inquiryPharmacistMapper.selectOne(InquiryPharmacistDO::getUserId, userBaseInfoDto.getUserId());
        if (pharmacistDO == null) {
            return;
        }

        Long tenantId = Optional.ofNullable(userBaseInfoDto.getTenantId()).orElse(TenantContextHolder.getTenantId());
        AdminUserRespDTO userBaseInfo = TenantUtils.execute(tenantId, () -> adminUserApi.getUserBaseInfo(userBaseInfoDto.getUserId()));

        boolean change = !StringUtils.equals(userBaseInfo.getNickname(), pharmacistDO.getName())
            || (StringUtils.isNotBlank(userBaseInfo.getIdCard()) && !StringUtils.equals(userBaseInfo.getIdCard(), pharmacistDO.getIdCard()))
            || !StringUtils.equals(userBaseInfo.getMobile(), pharmacistDO.getMobile());

        // 判断是否修改
        if (change || !Objects.equals(userBaseInfo.getSex(), pharmacistDO.getSex())) {
            log.info("用户基础信息修改-药师,userId:{},nickName:{}", userBaseInfo.getId(), userBaseInfoDto.getNickname());
            inquiryPharmacistMapper.updateById(InquiryPharmacistDO.builder().id(pharmacistDO.getId()).name(userBaseInfo.getNickname()).idCard(userBaseInfo.getIdCard()).mobile(userBaseInfo.getMobile()).sex(userBaseInfo.getSex()).build());
        }

        if (change) {
            log.info("用户三要素修改,重置CA认证数据,userId:{},nickName:{}", userBaseInfo.getId(), userBaseInfoDto.getNickname());
            inquirySignatureCaAuthApi.resetCaAuth(pharmacistDO.getUserId());
        }
    }

}
