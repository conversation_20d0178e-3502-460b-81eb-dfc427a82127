package com.xyy.saas.inquiry.pharmacist.server.controller.admin.pharmacist;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.tenant.core.aop.TenantIgnore;
import com.xyy.saas.inquiry.pharmacist.server.controller.admin.pharmacist.vo.InquiryPharmacistPageReqVO;
import com.xyy.saas.inquiry.pharmacist.server.controller.admin.pharmacist.vo.InquiryPharmacistRespVO;
import com.xyy.saas.inquiry.pharmacist.server.controller.admin.pharmacist.vo.InquiryPharmacistSaveReqVO;
import com.xyy.saas.inquiry.pharmacist.server.controller.admin.pharmacist.vo.InquiryPharmacistUpdateStatusReqVO;
import com.xyy.saas.inquiry.pharmacist.server.service.pharmacist.InquiryPharmacistService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


@Tag(name = "管理后台 - 药师信息")
@RestController
@RequestMapping("/pharmacist/inquiry-pharmacist")
@Validated
public class InquiryPharmacistController {

    @Resource
    private InquiryPharmacistService inquiryPharmacistService;

    // ================= 超级管理员 =================

    @PostMapping("/system/create")
    @Operation(summary = "创建药师信息-系统")
    @PreAuthorize("@ss.hasPermission('saas:inquiry-pharmacist:create')")
    public CommonResult<?> createInquiryPharmacistSystem(@Valid @RequestBody InquiryPharmacistSaveReqVO createReqVO) {
        return success(inquiryPharmacistService.createInquiryPharmacistSystem(createReqVO));
    }

    @PutMapping("/system/update")
    @Operation(summary = "更新药师信息")
    @PreAuthorize("@ss.hasPermission('saas:inquiry-pharmacist:update')")
    public CommonResult<Boolean> updateInquiryPharmacistSystem(@Valid @RequestBody InquiryPharmacistSaveReqVO updateReqVO) {
        inquiryPharmacistService.updateInquiryPharmacistSystem(updateReqVO);
        return success(true);
    }

    @PutMapping("/system/audit")
    @Operation(summary = "审核药师")
    @PreAuthorize("@ss.hasPermission('saas:inquiry-pharmacist:audit')")
    public CommonResult<Boolean> auditInquiryPharmacist(@Valid @RequestBody InquiryPharmacistUpdateStatusReqVO updateReqVO) {
        inquiryPharmacistService.auditInquiryPharmacist(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/system/delete")
    @Operation(summary = "删除药师信息")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('saas:inquiry-pharmacist:delete')")
    public CommonResult<Boolean> deleteInquiryPharmacist(@RequestParam("id") Long id) {
        inquiryPharmacistService.deleteInquiryPharmacist(id);
        return success(true);
    }


    @GetMapping("/system/page")
    @Operation(summary = "获得药师信息分页-系统")
    @PreAuthorize("@ss.hasPermission('saas:inquiry-pharmacist:query')")
    @TenantIgnore
    public CommonResult<PageResult<InquiryPharmacistRespVO>> pagePharmacistSystem(@Valid InquiryPharmacistPageReqVO pageReqVO) {
        PageResult<InquiryPharmacistRespVO> pageResult = inquiryPharmacistService.pagePharmacistSystem(pageReqVO);
        return success(pageResult);
    }


    @GetMapping("/get")
    @Operation(summary = "获得药师信息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('saas:inquiry-pharmacist:query')")
    public CommonResult<InquiryPharmacistRespVO> getInquiryPharmacist(@RequestParam("id") Long id) {
        InquiryPharmacistRespVO inquiryPharmacist = inquiryPharmacistService.getInquiryPharmacistVo(id);
        return success(inquiryPharmacist);
    }

    @GetMapping("/get-by-mobile")
    @Operation(summary = "获得药师信息-根据手机号")
    @Parameter(name = "mobile", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('saas:inquiry-pharmacist:query')")
    public CommonResult<InquiryPharmacistRespVO> getInquiryPharmacist(@RequestParam("mobile") String mobile) {
        InquiryPharmacistRespVO inquiryPharmacist = inquiryPharmacistService.getInquiryPharmacistVoByMobile(mobile);
        return success(inquiryPharmacist);
    }

//
//    @GetMapping("/export-excel")
//    @Operation(summary = "导出药师信息 Excel")
//    @PreAuthorize("@ss.hasPermission('saas:inquiry-pharmacist:export')")
//    @ApiAccessLog(operateType = EXPORT)
//    public void exportInquiryPharmacistExcel(@Valid InquiryPharmacistPageReqVO pageReqVO,
//              HttpServletResponse response) throws IOException {
//        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
//        List<InquiryPharmacistDO> list = inquiryPharmacistService.getInquiryPharmacistPage(pageReqVO).getList();
//        // 导出 Excel
//        ExcelUtils.write(response, "药师信息.xls", "数据", InquiryPharmacistRespVO.class,
//                        BeanUtils.toBean(list, InquiryPharmacistRespVO.class));
//    }

    // =========== 门店 ===========


    @GetMapping("/store/page")
    @Operation(summary = "获得药师信息分页-门店")
    @PreAuthorize("@ss.hasPermission('saas:inquiry-pharmacist:query')")
    public CommonResult<PageResult<InquiryPharmacistRespVO>> pagePharmacistStore(@Valid InquiryPharmacistPageReqVO pageReqVO) {
        PageResult<InquiryPharmacistRespVO> pageResult = inquiryPharmacistService.pagePharmacistStore(pageReqVO);
        return success(pageResult);
    }

    // @GetMapping("/store/bindPage")
    // @Operation(summary = "获得药师绑定门店信息")
    // @PreAuthorize("@ss.hasPermission('saas:inquiry-pharmacist:query')")
    // public CommonResult<PageResult<TenantDto>> pagePharmacistBindStore(@Valid InquiryPharmacistPageReqVO pageReqVO) {
    //     PageResult<TenantDto> pageResult = inquiryPharmacistService.pagePharmacistBindStore(pageReqVO);
    //     return success(pageResult);
    // }


    @PostMapping("/store/create")
    @Operation(summary = "创建药师信息-门店")
    @PreAuthorize("@ss.hasPermission('saas:inquiry-pharmacist:create')")
    public CommonResult<Long> createInquiryPharmacistStore(@Valid @RequestBody InquiryPharmacistSaveReqVO createReqVO) {
        return success(inquiryPharmacistService.createInquiryPharmacistStore(createReqVO));
    }

    @PutMapping("/store/update")
    @Operation(summary = "更新药师信息-门店")
    @PreAuthorize("@ss.hasPermission('saas:inquiry-pharmacist:update')")
    public CommonResult<Boolean> updateInquiryPharmacistStore(@Valid @RequestBody InquiryPharmacistSaveReqVO updateReqVO) {
        inquiryPharmacistService.updateInquiryPharmacistStore(updateReqVO);
        return success(true);
    }

}