package com.xyy.saas.inquiry.pharmacist.server.service.pharmacist;

import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import com.xyy.saas.inquiry.pharmacist.server.controller.admin.pharmacist.vo.InquiryTenantPharmacistRelationBindReqVO;
import com.xyy.saas.inquiry.pharmacist.server.controller.admin.pharmacist.vo.InquiryTenantPharmacistRelationSaveReqVO;
import com.xyy.saas.inquiry.pharmacist.server.convert.pharmacist.InquiryTenantPharmacistRelationConvert;
import com.xyy.saas.inquiry.pharmacist.server.dal.dataobject.pharmacist.InquiryTenantPharmacistRelationDO;
import com.xyy.saas.inquiry.pharmacist.server.dal.mysql.pharmacist.InquiryTenantPharmacistRelationMapper;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.inquiry.pharmacist.enums.ErrorCodeConstants.INQUIRY_TENANT_PHARMACIST_RELATION_NOT_EXISTS;

/**
 * 门店药师关系 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class InquiryTenantPharmacistRelationServiceImpl implements InquiryTenantPharmacistRelationService {

    @Resource
    private InquiryTenantPharmacistRelationMapper inquiryTenantPharmacistRelationMapper;

    @Resource
    @Lazy
    private InquiryPharmacistService inquiryPharmacistService;


    @Override
    public Long saveInquiryTenantPharmacistRelation(InquiryTenantPharmacistRelationSaveReqVO createReqVO) {
        // 插入
        InquiryTenantPharmacistRelationDO inquiryTenantPharmacistRelation = BeanUtils.toBean(createReqVO, InquiryTenantPharmacistRelationDO.class);
        // 先删再增
        inquiryTenantPharmacistRelationMapper.deleteByPharmacistId(createReqVO.getPharmacistId());
        inquiryTenantPharmacistRelationMapper.insert(inquiryTenantPharmacistRelation);
        // 返回
        return inquiryTenantPharmacistRelation.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void bindInquiryTenantPharmacistRelation(InquiryTenantPharmacistRelationBindReqVO bindReqVO) {
        // 校验药师
        inquiryPharmacistService.validateInquiryPharmacistExists(bindReqVO.getPharmacistId());
        // 删除绑定关系
        inquiryTenantPharmacistRelationMapper.deleteByPharmacistIdTenantIds(bindReqVO.getPharmacistId(), bindReqVO.getTenantIds());
        // 新增绑定关系
        inquiryTenantPharmacistRelationMapper.insertBatch(InquiryTenantPharmacistRelationConvert.INSTANCE.convertBindBatch(bindReqVO));
    }

    @Override
    public void unbindInquiryTenantPharmacistRelation(InquiryTenantPharmacistRelationBindReqVO bindReqVO) {
        // 校验药师
        inquiryPharmacistService.validateInquiryPharmacistExists(bindReqVO.getPharmacistId());
        // 删除绑定关系
        inquiryTenantPharmacistRelationMapper.deleteByPharmacistIdTenantIds(bindReqVO.getPharmacistId(), bindReqVO.getTenantIds());
    }

    @Override
    public void updateInquiryTenantPharmacistRelation(InquiryTenantPharmacistRelationSaveReqVO updateReqVO) {
        // 校验存在
        validateInquiryTenantPharmacistRelationExists(updateReqVO.getId());
        // 更新
        InquiryTenantPharmacistRelationDO updateObj = BeanUtils.toBean(updateReqVO, InquiryTenantPharmacistRelationDO.class);
        inquiryTenantPharmacistRelationMapper.updateById(updateObj);
    }

    @Override
    public void deleteInquiryTenantPharmacistRelation(Long id) {
        // 校验存在
        validateInquiryTenantPharmacistRelationExists(id);
        // 删除
        inquiryTenantPharmacistRelationMapper.deleteById(id);
    }

    private void validateInquiryTenantPharmacistRelationExists(Long id) {
        if (inquiryTenantPharmacistRelationMapper.selectById(id) == null) {
            throw exception(INQUIRY_TENANT_PHARMACIST_RELATION_NOT_EXISTS);
        }
    }

    @Override
    public InquiryTenantPharmacistRelationDO getInquiryTenantPharmacistRelation(Long id) {
        return inquiryTenantPharmacistRelationMapper.selectById(id);
    }


}