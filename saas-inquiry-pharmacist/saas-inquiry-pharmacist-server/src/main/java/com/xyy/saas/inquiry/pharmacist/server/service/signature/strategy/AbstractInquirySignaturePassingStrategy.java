package com.xyy.saas.inquiry.pharmacist.server.service.signature.strategy;

import cn.hutool.core.util.ObjectUtil;
import com.xyy.saas.inquiry.hospital.api.prescription.InquiryPrescriptionApi;
import com.xyy.saas.inquiry.patient.api.inquiry.InquiryApi;
import com.xyy.saas.inquiry.pharmacist.server.service.pharmacist.InquiryPharmacistImService;
import com.xyy.saas.inquiry.pharmacist.server.service.signature.dto.SignaturePassingHandleDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 签章回传处理策略
 *
 * @Author:chenxiaoyi
 * @Date:2024/12/03 19:30
 */
@Component
@Slf4j
public abstract class AbstractInquirySignaturePassingStrategy {

    @DubboReference
    protected InquiryPrescriptionApi inquiryPrescriptionApi;

    @DubboReference
    protected InquiryApi inquiryApi;

    @Autowired
    protected InquiryPharmacistImService inquiryPharmacistImService;

    /**
     * 处理当前节点任务 eg: 修改自身特殊逻辑 审查记录、同步三方等
     */
    public void handleSelf(SignaturePassingHandleDto sphDto) {

    }

    /**
     * 处理下级节点 eg： 推送审方池 消息通知 自动自绘 作为上一级
     */
    public void handleNext(SignaturePassingHandleDto sphDto) {
        if (ObjectUtil.isNull(sphDto.getSpMessage()) || ObjectUtil.isNull(sphDto.getPrescription())) {
            return;
        }
        executeNext(sphDto);
    }

    public abstract void executeNext(SignaturePassingHandleDto sphDto);


}
