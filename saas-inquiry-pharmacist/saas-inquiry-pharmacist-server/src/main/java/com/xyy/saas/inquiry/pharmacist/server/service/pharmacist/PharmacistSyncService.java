package com.xyy.saas.inquiry.pharmacist.server.service.pharmacist;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.pharmacist.server.controller.admin.pharmacist.vo.InquiryPharmacistRespVO;
import com.xyy.saas.inquiry.pharmacist.server.controller.admin.pharmacist.vo.InquiryPharmacistSaveReqVO;
import com.xyy.saas.inquiry.pharmacist.server.controller.admin.pharmacist.vo.forward.InquiryPharmacistForwardRespVO;
import com.xyy.saas.inquiry.pharmacist.server.service.pharmacist.dto.PharmacistSyncQueryDto;

/**
 * @Author:chenxiaoyi
 * @Date:2025/03/03 13:40
 */
public interface PharmacistSyncService {

    CommonResult<PageResult<InquiryPharmacistForwardRespVO>> queryUserDoctorPharmacistLists(PharmacistSyncQueryDto dto);


    CommonResult<InquiryPharmacistRespVO> queryUserDoctorPharmacist(String guid);

    CommonResult<Long> createInquiryPharmacist(InquiryPharmacistSaveReqVO createReqVO);

    void handlePharmacistImgUpload(InquiryPharmacistSaveReqVO detailRespVO);
}
