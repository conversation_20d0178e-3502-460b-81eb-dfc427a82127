package com.xyy.saas.inquiry.pharmacist.server.service.signature.strategy;

import com.xyy.saas.inquiry.enums.doctor.AuditorTypeEnum;
import com.xyy.saas.inquiry.enums.system.RoleCodeEnum;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionUpdateDTO;
import com.xyy.saas.inquiry.pharmacist.server.service.audit.strategy.OfflineAuditStrategy;
import com.xyy.saas.inquiry.pharmacist.server.service.signature.dto.SignaturePassingHandleDto;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 核对签章
 *
 * @Author:chenxiaoyi
 * @Date:2024/12/03 19:30
 */
@Component("checkSign")
@Slf4j
public class CheckSignaturePassingStrategy extends AbstractInquirySignaturePassingStrategy {

    @Resource
    private OfflineAuditStrategy offlineAuditStrategy;

    @Override
    public void executeNext(SignaturePassingHandleDto sphDto) {
        // 更新处方审核人类型
        inquiryPrescriptionApi.updateInquiryPrescription(InquiryPrescriptionUpdateDTO.builder().id(sphDto.getPrescription().getId()).auditorType(AuditorTypeEnum.CHECK.getCode()).build());

        // 核对 目前直接审核签章
        offlineAuditStrategy.autoAuditPrescription(sphDto.getPrescription(), RoleCodeEnum.CHECK, AuditorTypeEnum.CHECK);

    }
}
