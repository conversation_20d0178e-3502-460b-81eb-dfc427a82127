package com.xyy.saas.inquiry.pharmacist.server.controller.app.audit.vo;

import com.xyy.saas.inquiry.enums.prescription.PrescriptionAuditWayTypeEnum;
import com.xyy.saas.inquiry.pojo.parmacist.PharmacistExtDto;
import com.xyy.saas.inquiry.pojo.prescription.PrescriptionExtDto;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;

@Schema(description = " 处方领取出参 VO")
@Data
public class InquiryPrescriptionReceiveVO {

    @Schema(description = "处方编号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String pref;

    @Schema(description = "处方id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long prescriptionId;

    @Schema(description = "审方记录ID", example = "27739")
    private Long auditRecordId;

    @Schema(description = "问诊单号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String inquiryPref;

    @Schema(description = "用药类型：0西药，1中药", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private Integer medicineType;

    @Schema(description = "过敏史  eg：青霉素|头孢", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<String> allergic;

    @Schema(description = "诊断编码", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<String> diagnosisCode;

    @Schema(description = "诊断说明", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<String> diagnosisName;


    @Schema(description = "肝肾功能异常  0、无  1、肝功能异常  2、肾功能异常  3、肝肾功能异常", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer liverKidneyValue;

    @Schema(description = "妊娠哺乳期   0、否  1、妊娠期   2、哺乳期  3、妊娠哺乳期", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer gestationLactationValue;

    @Schema(description = "慢病病情需要 0 否  1是", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer slowDisease;

    @Schema(description = "处方笺图片url", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://www.iocoder.cn")
    private String prescriptionImgUrl;

    @Schema(description = "处方笺PDFurl", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://www.iocoder.cn")
    private String prescriptionPdfUrl;

    @Schema(description = "问诊开始时间")
    private LocalDateTime inquiryStartTime;

    @Schema(description = "问诊结束时间")
    private LocalDateTime inquiryEndTime;

    @Schema(description = "互联网医院名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "21843")
    private String hospitalName;

    @Schema(description = "医师姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    private String doctorName;

    @Schema(description = "医师出方时间")
    private LocalDateTime outPrescriptionTime;

    @Schema(description = "问诊业务类型 1、药店问诊  2、远程审方", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer inquiryBizType;

    /**
     * {@link PrescriptionAuditWayTypeEnum}
     */
    @Schema(description = "审方方式类别 1、荷叶审方  2、远程审方", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer auditWayType;

    @Schema(description = "科室名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    private String deptName;

    @Schema(description = "患者姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    private String patientName;

    @Schema(description = "患者性别：1 男 2 女", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer patientSex;

    @Schema(description = "患者年龄", requiredMode = Schema.RequiredMode.REQUIRED)
    private String patientAge;

    @Schema(description = "处方拓展字段", requiredMode = Schema.RequiredMode.REQUIRED)
    private PrescriptionExtDto ext;

    @Schema(description = "处方类型", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer prescriptionType;
    /**
     * 目前仅 PC端使用
     */
    // @Schema(description = "处方审核是否视频")
    // private boolean prescriptionAuditVideo;

    @Schema(description = "处方审核超时时间,单位秒")
    private Integer auditTimeOut;

    @Schema(description = "当前处方是否可远程审方")
    private boolean canRemoteAudit;

    @Schema(description = "药师扩展信息")
    private PharmacistExtDto pharmacistExt;
}