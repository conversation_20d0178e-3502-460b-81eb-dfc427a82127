package com.xyy.saas.inquiry.pharmacist.server.controller.admin.pharmacist;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.pharmacist.server.controller.admin.pharmacist.vo.InquiryPharmacistRespVO;
import com.xyy.saas.inquiry.pharmacist.server.controller.admin.pharmacist.vo.InquiryPharmacistSaveReqVO;
import com.xyy.saas.inquiry.pharmacist.server.controller.admin.pharmacist.vo.forward.InquiryPharmacistForwardRespVO;
import com.xyy.saas.inquiry.pharmacist.server.service.pharmacist.PharmacistSyncService;
import com.xyy.saas.inquiry.pharmacist.server.service.pharmacist.dto.PharmacistSyncQueryDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


@Tag(name = "管理后台 - 药师信息")
@RestController
@RequestMapping("/pharmacist/inquiry-pharmacist")
@Validated
public class InquiryPharmacistSyncController {

    @Resource
    private PharmacistSyncService pharmacistSyncService;

    @GetMapping("/page-sync")
    @Operation(summary = "获得旧系统药师信息分页")
    @PreAuthorize("@ss.hasPermission('saas:inquiry-pharmacist:query')")
    public CommonResult<PageResult<InquiryPharmacistForwardRespVO>> queryUserDoctorPharmacistLists(PharmacistSyncQueryDto dto) {
        return pharmacistSyncService.queryUserDoctorPharmacistLists(dto);
    }


    @GetMapping("/query-sync")
    @Operation(summary = "获得旧系统药师信息")
    @PreAuthorize("@ss.hasPermission('saas:inquiry-pharmacist:query')")
    public CommonResult<InquiryPharmacistRespVO> queryUserDoctorPharmacist(@RequestParam(value = "guid") String guid) {
        return pharmacistSyncService.queryUserDoctorPharmacist(guid);
    }

    @PostMapping("/sync-create")
    @Operation(summary = "同步创建药师信息")
    @PreAuthorize("@ss.hasPermission('saas:inquiry-pharmacist:sync')")
    public CommonResult<Long> createInquiryPharmacist(@Valid @RequestBody InquiryPharmacistSaveReqVO createReqVO) {
        return pharmacistSyncService.createInquiryPharmacist(createReqVO);
    }


}