package com.xyy.saas.inquiry.pharmacist.server.constant;

/**
 * @Author:ch<PERSON><PERSON><PERSON><PERSON>
 * @Date:2024/12/12 13:55
 */
public class PrescriptionAuditConstant {

    /**
     * 药师审核超时时间 单位:秒 默认60s
     */
    public static final String PHARMACIST_AUDIT_TIMEOUT = "pharmacist.audit.timeout";

    /**
     * 药师视频审核超时时间 单位:秒 默认30*60s
     */
    public static final String PHARMACIST_VIDEO_AUDIT_TIMEOUT = "pharmacist.video.audit.timeout";

    /**
     * 药师远程审核超时时间 单位:秒 默认180s
     */
    public static final String PHARMACIST_REMOTE_AUDIT_TIMEOUT = "pharmacist.remote.audit.timeout";

    /**
     * 处方变更线下审方开关 0-是 1-否
     */
    public static final String PRESCRIPTION_CHANGE_OFFLINE_AUDIT_SWITCH = "pharmacist.prescription.change.offline.audit.switch";

    /**
     * 处方变更线下审方 延迟时间 单位:秒
     */
    public static final String PRESCRIPTION_CHANGE_OFFLINE_DELAY_TIME = "pharmacist.prescription.change.offline.delay.time";

    /**
     * 处方变更线下审方查数据库分页大小
     */
    public static final String PRESCRIPTION_CHANGE_OFFLINE_PAGE_SIZE = "pharmacist.prescription.change.offline.page_size";

    /**
     * 处方修改远程审方类型 延迟时间 单位:毫秒
     */
    public static final String PRESCRIPTION_CHANGE_REMOTE_AUDIT_DELAY_TIME = "pharmacist.prescription.change.remote.audit.delay.time";
}
