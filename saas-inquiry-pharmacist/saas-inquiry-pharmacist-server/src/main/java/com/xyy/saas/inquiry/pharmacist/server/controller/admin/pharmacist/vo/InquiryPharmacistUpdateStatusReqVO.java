package com.xyy.saas.inquiry.pharmacist.server.controller.admin.pharmacist.vo;

import cn.iocoder.yudao.framework.common.validation.InEnum;
import com.xyy.saas.inquiry.enums.doctor.AuditStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "管理后台 - 药师更新状态 Request VO")
@Data
@Accessors(chain = true)
public class InquiryPharmacistUpdateStatusReqVO {

    @Schema(description = "药师id", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @NotNull(message = "药师id不能为空")
    private Long id;

    @Schema(description = "审核状态 0、待审核  1、审核通过  2、审核驳回", example = "1")
    @InEnum(value = AuditStatusEnum.class, message = "修改状态必须是 {value}")
    private Integer auditStatus;


    @Schema(description = "备注", example = "xx")
    private String remark;

}
