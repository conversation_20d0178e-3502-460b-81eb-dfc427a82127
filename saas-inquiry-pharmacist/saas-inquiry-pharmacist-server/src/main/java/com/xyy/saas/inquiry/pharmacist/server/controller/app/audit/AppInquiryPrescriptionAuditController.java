package com.xyy.saas.inquiry.pharmacist.server.controller.app.audit;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.idempotent.core.annotation.Idempotent;
import cn.iocoder.yudao.framework.idempotent.core.keyresolver.impl.UserIdempotentKeyResolver;
import com.xyy.saas.inquiry.enums.pharmacist.RemoteAuditVideoActionEnum;
import com.xyy.saas.inquiry.enums.prescription.PrescriptionAuditStatusEnum;
import com.xyy.saas.inquiry.pharmacist.server.controller.app.audit.vo.InquiryAuditVideoHandleReqVO;
import com.xyy.saas.inquiry.pharmacist.server.controller.app.audit.vo.InquiryPrescriptionAuditPageReqVO;
import com.xyy.saas.inquiry.pharmacist.server.controller.app.audit.vo.InquiryPrescriptionAuditRespVO;
import com.xyy.saas.inquiry.pharmacist.server.controller.app.audit.vo.InquiryPrescriptionAuditVO;
import com.xyy.saas.inquiry.pharmacist.server.controller.app.audit.vo.InquiryPrescriptionReceiveVO;
import com.xyy.saas.inquiry.pharmacist.server.dal.dataobject.audit.InquiryPrescriptionAuditDO;
import com.xyy.saas.inquiry.pharmacist.server.service.audit.InquiryPrescriptionAuditService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.concurrent.TimeUnit;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


@Tag(name = "APP+PC - 处方审核核心接口")
@RestController
@RequestMapping(value = {"/admin-api/kernel/pharmacist/prescription-audit", "/app-api/kernel/pharmacist/prescription-audit"})
@Validated
public class AppInquiryPrescriptionAuditController {

    @Resource
    private InquiryPrescriptionAuditService prescriptionAuditService;


    @GetMapping("/wait-receive-count")
    @Operation(summary = "获取当前药师待审核处方数量")
    @Idempotent(timeout = 300, timeUnit = TimeUnit.MILLISECONDS, keyResolver = UserIdempotentKeyResolver.class) // user维度锁住 默认300ms
    @PreAuthorize("@ss.hasRole('pharmacist')")
    public CommonResult<Long> waitReceiveCount() {
        return prescriptionAuditService.waitReceiveCount();
    }

    @GetMapping("/receive-prescription")
    @Operation(summary = "领取一个待审核的处方")
    @Idempotent(keyResolver = UserIdempotentKeyResolver.class) // user维度锁住 默认1s
    @PreAuthorize("@ss.hasRole('pharmacist')")
    public CommonResult<InquiryPrescriptionReceiveVO> receivePrescription(@RequestParam(required = false) String pref) {
        return prescriptionAuditService.receivePrescription(pref);
    }

    @GetMapping("/audit-timeout")
    @Operation(summary = "获取处方超时时间-单位秒")
    @PreAuthorize("@ss.hasRole('pharmacist')")
    public CommonResult<Integer> getAuditTimeout() {
        return prescriptionAuditService.getAuditTimeout();
    }


    @PostMapping("/audit-pass")
    @Operation(summary = "审核通过")
    @Idempotent // 参数维度锁住 默认1s
    @PreAuthorize("@ss.hasRole('pharmacist')")
    public CommonResult<Boolean> auditPass(@RequestBody @Valid InquiryPrescriptionAuditVO auditVO) {
        auditVO.setAuditStatus(PrescriptionAuditStatusEnum.APPROVED);
        return prescriptionAuditService.auditPass(auditVO);
    }

    @PostMapping("/audit-reject")
    @Operation(summary = "审核驳回")
    @Idempotent // 参数维度锁住 默认1s
    @PreAuthorize("@ss.hasRole('pharmacist')")
    public CommonResult<Boolean> auditReject(@RequestBody @Valid InquiryPrescriptionAuditVO auditVO) {
        auditVO.setAuditStatus(PrescriptionAuditStatusEnum.REJECTED);
        return prescriptionAuditService.auditReject(auditVO);
    }

    @GetMapping("/audit-can-video-call")
    @Operation(summary = "审核是否可发起视频通话")
    public CommonResult<Boolean> auditCanVideoCall(@RequestParam("pref") String pref) {
        return CommonResult.success(prescriptionAuditService.auditCanVideoCall(pref));
    }

    @PutMapping("/audit-start-video-call")
    @Operation(summary = "审核发起视频通话")
    public CommonResult<Boolean> auditStartVideoCall(@RequestParam("pref") String pref, @RequestParam("startActionType") Integer startActionType) {
        return prescriptionAuditService.auditStartVideoCall(
            InquiryAuditVideoHandleReqVO.builder().pref(pref).startActionType(ObjectUtil.isEmpty(startActionType) ? RemoteAuditVideoActionEnum.INIT.getCode() : startActionType).build()
        );
    }

    @PutMapping("/audit-video-call-handle")
    @Operation(summary = "视频呼叫处理")
    public CommonResult<Boolean> auditVideoCallHandle(@Valid @RequestBody InquiryAuditVideoHandleReqVO reqVO) {
        return prescriptionAuditService.auditVideoCallHandle(reqVO);
    }

    // @PutMapping("/change-audit-type")
    // @Operation(summary = "变更审方类别")
    // @PreAuthorize("@ss.hasRole('pharmacist')")
    // @Idempotent(timeout = 3, keyResolver = ExpressionIdempotentKeyResolver.class, keyArg = "#reqVO.pref", message = "请求频繁，请稍后再试")
    // public CommonResult<String> changeAuditType(@Valid @RequestBody InquiryPrescriptionAuditWayReqVO reqVO) {
    //     return prescriptionAuditService.changeAuditType(reqVO);
    // }


    @GetMapping("/get")
    @Operation(summary = "获得处方审核记录")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasRole('pharmacist')")
    public CommonResult<InquiryPrescriptionAuditRespVO> getPrescriptionAudit(@RequestParam("id") Long id) {
        InquiryPrescriptionAuditDO prescriptionAudit = prescriptionAuditService.getPrescriptionAudit(id);
        return success(BeanUtils.toBean(prescriptionAudit, InquiryPrescriptionAuditRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得处方审核记录分页")
    @PreAuthorize("@ss.hasRole('pharmacist')")
    public CommonResult<PageResult<InquiryPrescriptionAuditRespVO>> getPrescriptionAuditPage(@Valid InquiryPrescriptionAuditPageReqVO pageReqVO) {
        PageResult<InquiryPrescriptionAuditDO> pageResult = prescriptionAuditService.getPrescriptionAuditPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, InquiryPrescriptionAuditRespVO.class));
    }


}