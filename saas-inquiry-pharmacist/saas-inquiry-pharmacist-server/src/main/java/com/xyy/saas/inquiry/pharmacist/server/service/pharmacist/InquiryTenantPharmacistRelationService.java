package com.xyy.saas.inquiry.pharmacist.server.service.pharmacist;

import com.xyy.saas.inquiry.pharmacist.server.controller.admin.pharmacist.vo.InquiryTenantPharmacistRelationBindReqVO;
import com.xyy.saas.inquiry.pharmacist.server.controller.admin.pharmacist.vo.InquiryTenantPharmacistRelationSaveReqVO;
import com.xyy.saas.inquiry.pharmacist.server.dal.dataobject.pharmacist.InquiryTenantPharmacistRelationDO;
import jakarta.validation.Valid;

/**
 * 门店药师关系 Service 接口
 *
 * <AUTHOR>
 */
public interface InquiryTenantPharmacistRelationService {

    /**
     * 创建门店药师关系
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long saveInquiryTenantPharmacistRelation(@Valid InquiryTenantPharmacistRelationSaveReqVO createReqVO);

    /**
     * 更新门店药师关系
     *
     * @param updateReqVO 更新信息
     */
    void updateInquiryTenantPharmacistRelation(@Valid InquiryTenantPharmacistRelationSaveReqVO updateReqVO);

    /**
     * 删除门店药师关系
     *
     * @param id 编号
     */
    void deleteInquiryTenantPharmacistRelation(Long id);

    /**
     * 获得门店药师关系
     *
     * @param id 编号
     * @return 门店药师关系
     */
    InquiryTenantPharmacistRelationDO getInquiryTenantPharmacistRelation(Long id);

    /**
     * 药师绑定门店
     *
     * @param createReqVO 绑定关系
     * @return
     */
    void bindInquiryTenantPharmacistRelation(InquiryTenantPharmacistRelationBindReqVO bindReqVO);

    /**
     * 药师解绑门店
     *
     * @param bindReqVO 绑定关系
     */
    void unbindInquiryTenantPharmacistRelation(InquiryTenantPharmacistRelationBindReqVO bindReqVO);
}