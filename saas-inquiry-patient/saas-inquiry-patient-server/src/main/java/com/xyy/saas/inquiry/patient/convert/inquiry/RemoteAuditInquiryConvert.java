package com.xyy.saas.inquiry.patient.convert.inquiry;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import com.xyy.saas.inquiry.drugstore.api.option.dto.InquiryOptionConfigRespDto;
import com.xyy.saas.inquiry.drugstore.api.tenant.dto.TenantDeductCostDto;
import com.xyy.saas.inquiry.enums.inquiry.InquiryStatusEnum;
import com.xyy.saas.inquiry.enums.inquiry.InquiryWayTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.PrescriptionStatusEnum;
import com.xyy.saas.inquiry.enums.tenant.TenantTypeEnum;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.RemotePrescriptionDTO;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDetailDto;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import com.xyy.saas.inquiry.patient.controller.app.inquiry.vo.DiagnosisItemVO;
import com.xyy.saas.inquiry.patient.controller.app.inquiry.vo.RemoteAuditBaseReqVO;
import com.xyy.saas.inquiry.patient.controller.app.inquiry.vo.RemoteAuditInquiryReqVO;
import com.xyy.saas.inquiry.patient.dal.dataobject.patient.InquiryPatientInfoDO;
import com.xyy.saas.inquiry.pojo.TenantDto;
import com.xyy.saas.inquiry.pojo.inquiry.InquiryDetailExtDto;
import com.xyy.saas.inquiry.util.PrefUtil;
import java.time.LocalDateTime;
import java.util.Objects;
import java.util.Optional;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * @Author: xucao
 * @DateTime: 2025/4/22 13:15
 * @Description: 远程审方问诊转换器
 **/
@Mapper
public interface RemoteAuditInquiryConvert {

    RemoteAuditInquiryConvert INSTANCE = Mappers.getMapper(RemoteAuditInquiryConvert.class);

    default InquiryRecordDto convertRemoteAuditInquiryReqVO2InquiryDTO(RemoteAuditInquiryReqVO remoteAuditInquiryReqVO, InquiryPatientInfoDO patientInfoDO, TenantDto tenantDto, InquiryOptionConfigRespDto optionConfig) {
        InquiryRecordDto inquiryRecordDto = BeanUtil.copyProperties(remoteAuditInquiryReqVO, InquiryRecordDto.class);
        inquiryRecordDto.setPref(PrefUtil.getInquiryPref());
        inquiryRecordDto.setTenantId(tenantDto.getId());
        inquiryRecordDto.setTenantName(tenantDto.getName());
        inquiryRecordDto.setInquiryStatus(InquiryStatusEnum.ENDED.getStatusCode());
        RemoteAuditBaseReqVO remoteAud = remoteAuditInquiryReqVO.getRemoteAuditBaseReqVO();
        // 赋值患者信息
        BeanUtils.copyProperties(remoteAud.getPatient(), inquiryRecordDto);
        inquiryRecordDto.setPatientPref(patientInfoDO.getPref());
        inquiryRecordDto.setHospitalName(remoteAud.getHospitalName());
        inquiryRecordDto.setDoctorName(remoteAud.getDoctorName());
        inquiryRecordDto.setMedicineType(remoteAud.getMedicineType());
        inquiryRecordDto.setStartTime(LocalDateTime.now());
        InquiryRecordDetailDto recordDetailDto = convertVO2DetailDTO(remoteAud, inquiryRecordDto);

        recordDetailDto.setExt(recordDetailDto.getExt() == null ? new InquiryDetailExtDto() : recordDetailDto.getExt());
        recordDetailDto.getExt().setPrescriptionAuditVideo(optionConfig != null && BooleanUtil.isTrue(optionConfig.getProcRemoteAuditPrescriptionVideo()));
        // 开启远程视频审方
        inquiryRecordDto.setInquiryWayType(recordDetailDto.getExt().isPrescriptionAuditVideo() ? InquiryWayTypeEnum.VIDEO.getCode() : InquiryWayTypeEnum.TEXT.getCode());

        inquiryRecordDto.setInquiryRecordDetailDto(recordDetailDto);
        return inquiryRecordDto;
    }

    default InquiryRecordDetailDto convertVO2DetailDTO(RemoteAuditBaseReqVO remoteAuditBaseReqVO, InquiryRecordDto inquiryRecordDto) {
        InquiryRecordDetailDto detailDto = new InquiryRecordDetailDto();
        BeanUtils.copyProperties(inquiryRecordDto, detailDto);
        detailDto.setInquiryPref(inquiryRecordDto.getPref());
        Optional.ofNullable(remoteAuditBaseReqVO.getDiagnosis()).ifPresent(diagnosis -> {
            detailDto.setDiagnosisCode(diagnosis.stream().map(DiagnosisItemVO::getDiagnosisCode).toList());
            detailDto.setDiagnosisName(diagnosis.stream().map(DiagnosisItemVO::getDiagnosisName).toList());
        });
        return detailDto;
    }

    default RemotePrescriptionDTO convert2PrescriptionDTO(RemoteAuditInquiryReqVO remoteAuditInquiryReqVO, InquiryRecordDto inquiryRecordDto, TenantDto tenantDto, TenantDeductCostDto tenantDeductCostDto) {
        RemotePrescriptionDTO remotePrescriptionDTO = BeanUtil.copyProperties(remoteAuditInquiryReqVO, RemotePrescriptionDTO.class);
        BeanUtils.copyProperties(inquiryRecordDto, remotePrescriptionDTO);
        remotePrescriptionDTO.setInquiryPref(inquiryRecordDto.getPref());
        remotePrescriptionDTO.setPref(PrefUtil.getPrescriptionPref());
        remotePrescriptionDTO.setCostId(tenantDeductCostDto.getCostId());
        remotePrescriptionDTO.setStatus(PrescriptionStatusEnum.WAIT_APPROVAL.getStatusCode());
        RemoteAuditBaseReqVO remoteAud = remoteAuditInquiryReqVO.getRemoteAuditBaseReqVO();
        remotePrescriptionDTO.setPrescriptionImgUrl(remoteAud.getPrescriptionImg());
        remotePrescriptionDTO.setThirdPrescriptionNo(remoteAud.getThirdPrescriptionNo());
        remotePrescriptionDTO.setOutPrescriptionTime(remoteAud.getOutPrescriptionTime());
        Optional.ofNullable(remoteAud.getDiagnosis()).ifPresent(diagnosis -> {
            remotePrescriptionDTO.setDiagnosisCode(diagnosis.stream().map(DiagnosisItemVO::getDiagnosisCode).toList());
            remotePrescriptionDTO.setDiagnosisName(diagnosis.stream().map(DiagnosisItemVO::getDiagnosisName).toList());
        });
        remotePrescriptionDTO.extGet().setHeadTenantId(Objects.equals(tenantDto.getWzTenantType(), TenantTypeEnum.SINGLE_STORE) ? null : tenantDto.getHeadTenantId());
        remotePrescriptionDTO.extGet().setRemotePrescriptionImg(remoteAud.getPrescriptionImg());
        remotePrescriptionDTO.extGet().setPrescriptionAuditVideo(inquiryRecordDto.getInquiryRecordDetailDto().getExt().isPrescriptionAuditVideo());
        return remotePrescriptionDTO;
    }


}
