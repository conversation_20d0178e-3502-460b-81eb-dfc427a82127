package com.xyy.saas.inquiry.patient.convert.inquiry;

import cn.iocoder.yudao.module.system.enums.common.SexEnum;
import com.xyy.saas.inquiry.enums.doctor.AuditStatusEnum;
import com.xyy.saas.inquiry.enums.im.ImEventPushEnum;
import com.xyy.saas.inquiry.enums.im.ImSourceTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.MedicineTypeEnum;
import com.xyy.saas.inquiry.enums.patient.LiverKidneyFuncEnum;
import com.xyy.saas.inquiry.enums.patient.PregnancyLactationEnum;
import com.xyy.saas.inquiry.hospital.api.doctor.dto.InquiryDoctorCardInfoDto;
import com.xyy.saas.inquiry.im.api.message.dto.ImApplyMessageExtDto;
import com.xyy.saas.inquiry.im.api.message.dto.ImApplyMessageExtDto.DrugInfo;
import com.xyy.saas.inquiry.im.api.message.dto.ImApplyMessageExtDto.InquiryProductInfo;
import com.xyy.saas.inquiry.im.api.message.dto.ImDoctorCardMessageExtDto;
import com.xyy.saas.inquiry.im.api.message.dto.ImEventMessageExtDto;
import com.xyy.saas.inquiry.im.api.message.dto.ImEventMessageExtDto.EventInfo;
import com.xyy.saas.inquiry.im.api.message.dto.InquiryImMessageDto;
import com.xyy.saas.inquiry.patient.controller.app.inquiry.vo.InquiryRespVO;
import com.xyy.saas.inquiry.patient.dal.dataobject.inquiry.InquiryRecordDO;
import com.xyy.saas.inquiry.patient.dal.dataobject.inquiry.InquiryRecordDetailDO;
import com.xyy.saas.inquiry.patient.dal.dataobject.inquiry.QuestionAnswerInfoDO;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import com.xyy.saas.inquiry.patient.dal.dataobject.third.ThirdPartyPreInquiryDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * @Author: xucao
 * @Date: 2025/2/11 15:07
 * @Description: 问诊消息相关转换器
 */
@Mapper
public interface InquiryImConvert {

    InquiryImConvert INSTANCE = Mappers.getMapper(InquiryImConvert.class);

    final String doctorReceptionMsg = "您好,很高兴为您服务,根据国家互联网诊疗相关规定,线上处方仅适用于复诊续方。您提交的信息我已仔细阅读,我将根据您的病情为您开具处方。";


    /**
     * 问诊单一问一答消息转换器
     * @param questionAnswer
     * @param doctorIm
     * @param patientIm
     * @param inquiryPref
     * @return
     */
    default List<InquiryImMessageDto> convertQuestionAnswer2MessageList(QuestionAnswerInfoDO questionAnswer, String doctorIm, String patientIm, String inquiryPref) {
        List<InquiryImMessageDto> messageDtos = new ArrayList<>();
        messageDtos.add(InquiryImMessageDto.builder().msg(questionAnswer.getQuestion()).fromAccount(doctorIm).inquiryPref(inquiryPref).sendTime(questionAnswer.getQuestionTime()).toAccount(patientIm).build());
        messageDtos.add(InquiryImMessageDto.builder().msg(questionAnswer.getAnswer()).fromAccount(patientIm).inquiryPref(inquiryPref).sendTime(questionAnswer.getAnswerTime()).toAccount(doctorIm).build());
        return messageDtos;
    }

    /**
     * 用药申请单消息
     * @param inquiryRecordDO
     * @param doctorIm
     * @param patientIm
     * @param inquiryDetail
     * @return
     */
    default InquiryImMessageDto convertTOApplyMessage(InquiryRecordDO inquiryRecordDO, String doctorIm, String patientIm, InquiryRecordDetailDO inquiryDetail) {
        return InquiryImMessageDto.builder().extDto(convert2ApplyExtDto(inquiryDetail,inquiryRecordDO)).fromAccount(patientIm).inquiryPref(inquiryRecordDO.getPref()).sendTime(inquiryRecordDO.getCreateTime()).toAccount(doctorIm)
            .msg(convertTOMessage(inquiryRecordDO, inquiryDetail)).build();
    }

    /**
     * 用药申请单消息体内容
     * @param inquiryRecordDO
     * @param inquiryDetail
     * @return
     */
    default String convertTOMessage(InquiryRecordDO inquiryRecordDO, InquiryRecordDetailDO inquiryDetail) {
        StringBuilder sb = new StringBuilder();
        inquiryDetail.getPreDrugDetail().getInquiryProductInfos().forEach(item -> {
            sb.append(item.getCommonName()).append("X").append(item.getQuantity()).append(";");
        });
        // 女性患者需要拼接上妊娠哺乳期问题和随访回答
        String pregnancyMsg = SexEnum.FEMALE.getSex().equals(inquiryDetail.getPatientSex()) ? "<p>妊娠哺乳期：" + PregnancyLactationEnum.getDescByCode(inquiryDetail.getGestationLactationValue()) +  "</p>" : "";
        String medicineMsg = "<p>用药申请</p>" + "<p>患者信息：" + inquiryDetail.getPatientName() + " " + SexEnum.getDesc(inquiryDetail.getPatientSex()) + " " + inquiryDetail.getPatientAge() + "</p>" + "<p>病情描述：" + String.join(", ",
            inquiryDetail.getMainSuit()) + "</p>" + "<p>诊断：" + String.join(", ", inquiryDetail.getDiagnosisName()) + "</p>" + "<p>过敏史：" + String.join(", ", inquiryDetail.getAllergic()) + "</p>" + "<p>肝肾功能："
            + LiverKidneyFuncEnum.getDesc(inquiryDetail.getLiverKidneyValue()) + "</p>" + pregnancyMsg + "<p>用药类型：" + MedicineTypeEnum.getMedicineTypeName(inquiryRecordDO.getMedicineType()) + "</p>" + "<p>预购药品：" + sb + "</p>" + "<p>共"
            + inquiryDetail.getPreDrugDetail().getInquiryProductInfos().size() + "种药品" + "</p>";
        return medicineMsg;
    }

    /**
     * 用药申请单卡片消息EXT信息
     * @param inquiryRecordDetailDto
     * @return
     */
    default ImApplyMessageExtDto convert2ApplyExtDto(InquiryRecordDetailDO inquiryRecordDetailDto,InquiryRecordDO inquiryRecordDO) {
        return ImApplyMessageExtDto.builder().drugInfo(
                DrugInfo.builder().allergicItem(inquiryRecordDetailDto.getAllergic()).diagnosisName(inquiryRecordDetailDto.getDiagnosisName()).gestationLactationValue(inquiryRecordDetailDto.getGestationLactationValue())
                    .patientName(inquiryRecordDetailDto.getPatientName()).patientSex(inquiryRecordDetailDto.getPatientSex()).patientAge(inquiryRecordDetailDto.getPatientAge()).mainSuit(inquiryRecordDetailDto.getMainSuit())
                    .liverKidneyValue(inquiryRecordDetailDto.getLiverKidneyValue()).inquiryProductInfos(
                        inquiryRecordDetailDto.getPreDrugDetail().getInquiryProductInfos().stream().map(product -> InquiryProductInfo.builder().productName(product.getCommonName()).productNum(product.getQuantity()).build()).toList())
                    .medicineType(inquiryRecordDO.getMedicineType()).build())
            .inquiryPref(inquiryRecordDetailDto.getInquiryPref()).sourceType(ImSourceTypeEnum.NotifyMedicine.getCode()).build();
    }

    /**
     * 医生卡片消息
     * @param inquiryRecordDO
     * @param doctorIm
     * @param patientIm
     * @param cardInfoDto
     * @return
     */
    default InquiryImMessageDto convertTODoctorCardMessage(InquiryRecordDO inquiryRecordDO, String doctorIm, String patientIm, InquiryDoctorCardInfoDto cardInfoDto){
        return InquiryImMessageDto.builder().extDto(convert2DoctorCardExtDto(inquiryRecordDO,cardInfoDto)).fromAccount(doctorIm).inquiryPref(inquiryRecordDO.getPref()).sendTime(LocalDateTime.now()).toAccount(patientIm)
            .msg(ImSourceTypeEnum.CardDoc.getDescription()).build();
    }

    /**
     * 医生卡片消息EXT信息
     * @param inquiryRecordDO
     * @param cardInfoDto
     * @return
     */
    default ImDoctorCardMessageExtDto convert2DoctorCardExtDto(InquiryRecordDO inquiryRecordDO, InquiryDoctorCardInfoDto cardInfoDto) {
        return ImDoctorCardMessageExtDto.builder().doctorInfo(convert2DoctorCardInfo(cardInfoDto))
            .inquiryPref(inquiryRecordDO.getPref()).sourceType(ImSourceTypeEnum.CardDoc.getCode()).build();
    }


    ImDoctorCardMessageExtDto.DoctorInfo convert2DoctorCardInfo(InquiryDoctorCardInfoDto cardInfoDto);

    /**
     * 医生接诊消息
     * @param inquiryRecordDO
     * @param doctorIm
     * @param patientIm
     * @return
     */
    default InquiryImMessageDto convertTODoctorReceptionMessage(InquiryRecordDO inquiryRecordDO, String doctorIm, String patientIm){
        return InquiryImMessageDto.builder().fromAccount(doctorIm).inquiryPref(inquiryRecordDO.getPref()).toAccount(patientIm)
            .msg(doctorReceptionMsg).build();
    }

    /**
     * 问诊变更推送医生事件消息
     * @param doctorImAccount
     * @param eventEnum
     * @return
     */
    default InquiryImMessageDto convertSystemMsgForInquiryChangeToDoctor(String doctorImAccount , ImEventPushEnum eventEnum) {
        return InquiryImMessageDto.builder().toAccount(doctorImAccount).msg(eventEnum.getDesc()).extDto(ImEventMessageExtDto.builder().eventType(eventEnum.getCode()).build()).build();
    }

    /**
     * 问诊变更批量推送医生事件消息
     * @param doctorImList
     * @param eventEnum
     * @return
     */
    default InquiryImMessageDto convertBatchSystemMsgForInquiryChangeToDoctor(List<String> doctorImList, ImEventPushEnum eventEnum) {
        return InquiryImMessageDto.builder().toAccountList(doctorImList).msg(eventEnum.getDesc()).extDto(ImEventMessageExtDto.builder().eventType(eventEnum.getCode()).build()).build();
    }

    default InquiryImMessageDto convert2PreInquiryAuditEvent(String patientIm, ImEventPushEnum imEventPushEnum, ThirdPartyPreInquiryDO preInquiryDO , AuditStatusEnum auditStatusEnum, InquiryRespVO inquiryRespVO){
        return InquiryImMessageDto.builder().toAccount(patientIm).msg(imEventPushEnum.getDesc()).extDto(ImEventMessageExtDto.builder().eventType(imEventPushEnum.getCode()).eventInfo(
            EventInfo.builder().inquiryPref(inquiryRespVO.getInquiryPref()).autoInquiry(inquiryRespVO.getAutoInquiry()).preInquiryPref(preInquiryDO.getPref()).auditStatus(auditStatusEnum.getCode()).build()
        ).build()).build();
    }

    default InquiryImMessageDto convert2PreInquiryPushEvent(List<String> employeeImList, ImEventPushEnum imEventPushEnum, ThirdPartyPreInquiryDO preInquiryDO){
        return InquiryImMessageDto.builder().toAccountList(employeeImList).msg(imEventPushEnum.getDesc()).extDto(ImEventMessageExtDto.builder().eventType(imEventPushEnum.getCode()).eventInfo(
            EventInfo.builder().preInquiryPref(preInquiryDO.getPref()).build()
        ).build()).build();
    }
}
