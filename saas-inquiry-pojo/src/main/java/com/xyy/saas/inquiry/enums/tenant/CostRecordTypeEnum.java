package com.xyy.saas.inquiry.enums.tenant;

import cn.iocoder.yudao.framework.common.core.IntArrayValuable;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.math.BigDecimal;
import java.util.Arrays;

/**
 * @Desc 问诊额度记录类型枚举
 * <AUTHOR>
 */
@Getter
@RequiredArgsConstructor
public enum CostRecordTypeEnum implements IntArrayValuable {

    UNKNOWN(-1, "未知") {
        @Override
        public Long convertCost(Long cost) {
            if (cost == null) {
                return null;
            }
            return BigDecimal.valueOf(cost).abs().longValue();
        }
    },

    NORMAL(0, "正常") {
        @Override
        public Long convertCost(Long cost) {
            if (cost == null) {
                return null;
            }
            return BigDecimal.valueOf(cost).abs().longValue();
        }
    },
    STOP(1, "暂停") {
        @Override
        public Long convertCost(Long cost) {
            if (cost == null) {
                return null;
            }
            return BigDecimal.valueOf(cost).abs().longValue();
        }
    },

    REFUND(2, "退款") {
        @Override
        public Long convertCost(Long cost) {
            if (cost == null) {
                return null;
            }
            return BigDecimal.valueOf(cost).abs().negate().longValue();
        }
    },

    ABANDONED(3, "废弃") {
        @Override
        public Long convertCost(Long cost) {
            if (cost == null) {
                return null;
            }
            return BigDecimal.valueOf(cost).abs().negate().longValue();
        }
    },

    INQUIRY(4, "问诊") {
        @Override
        public Long convertCost(Long cost) {
            if (cost == null) {
                return null;
            }
            return BigDecimal.valueOf(cost).abs().negate().longValue();
        }
    },

    INQUIRY_CANAL(5, "问诊取消") {
        @Override
        public Long convertCost(Long cost) {
            if (cost == null) {
                return null;
            }
            return BigDecimal.valueOf(cost).abs().longValue();
        }
    },

    AUDIT_PASS(6, "审核") {
        @Override
        public Long convertCost(Long cost) {
            if (cost == null) {
                return null;
            }
            return BigDecimal.valueOf(cost).abs().negate().longValue();
        }
    },

    AUDIT_REJECT(7, "审核驳回") {
        @Override
        public Long convertCost(Long cost) {
            if (cost == null) {
                return null;
            }
            return BigDecimal.valueOf(cost).abs().negate().longValue();
        }
    },

    AUDIT_CANAL(8, "审核取消") {
        @Override
        public Long convertCost(Long cost) {
            if (cost == null) {
                return null;
            }
            return BigDecimal.valueOf(cost).abs().longValue();
        }
    },

    MANUAL_DEDUCTION(9, "手动扣除") {
        @Override
        public Long convertCost(Long cost) {
            if (cost == null) {
                return null;
            }
            return BigDecimal.valueOf(cost).abs().negate().longValue();
        }
    },
    MANUAL_ADD(10, "手动新增") {
        @Override
        public Long convertCost(Long cost) {
            if (cost == null) {
                return null;
            }
            return BigDecimal.valueOf(cost).abs().longValue();
        }
    },
    ;

    private final Integer code;

    private final String desc;

    /**
     * 转换当前额度 正负值
     *
     * @param cost 额度
     * @return
     */
    public abstract Long convertCost(Long cost);

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(CostRecordTypeEnum::getCode).toArray();

    @Override
    public int[] array() {
        return ARRAYS;
    }

    /**
     * 根据code获取枚举值
     *
     * @param code 枚举值code
     * @return 枚举值
     */
    public static CostRecordTypeEnum fromCode(int code) {
        return Arrays.stream(values())
            .filter(type -> type.getCode() == code)
            .findFirst()
            .orElse(UNKNOWN);
    }
}
