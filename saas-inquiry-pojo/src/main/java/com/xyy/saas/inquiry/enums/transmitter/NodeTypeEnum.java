package com.xyy.saas.inquiry.enums.transmitter;

import com.xyy.saas.inquiry.pojo.transmitter.TransmissionReqDataBaseDTO;
import com.xyy.saas.inquiry.pojo.transmitter.drug.SignInTransmitterDTO;
import com.xyy.saas.inquiry.pojo.transmitter.his.RegistrationTransmitterDTO;
import com.xyy.saas.inquiry.pojo.transmitter.internet.PrescriptionSupervisionConditionTransmitterDTO;
import com.xyy.saas.inquiry.pojo.transmitter.internet.PrescriptionTransmitterDTO;
import java.util.Objects;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum NodeTypeEnum {
    /**
     * ------------------------------------------------------公共节点----------------------------------------------------------------------
     */
    //公共节点
    COMMON(9999901, null, "公共节点", null),

    //获取token节点
    GET_TOKEN(9999902, null, "获取token节点", null),


    FUN_CONFIG(10000, null, "通用-功能配置", null),

    //写上注释分隔，表名这块属于互联网监管节点
    /**
     * ------------------------------------------------------互联网监管----------------------------------------------------------------------
     */

    INTERNET_SUPERVISION_CONDITION(29999, PrescriptionSupervisionConditionTransmitterDTO.class, "互联网监管-监管条件", OrganTypeEnum.INTERNET_SUPERVISION),

    // 互联网监管-在线挂号
    INTERNET_SUPERVISION_REGISTRATION_FEEDBACK(30000, PrescriptionTransmitterDTO.class, "互联网监管-在线挂号", OrganTypeEnum.INTERNET_SUPERVISION),

    // 互联网监管-上传门诊病例
    INTERNET_SUPERVISION_UPLOAD_OUT_PATIENT_CASE(30001, PrescriptionTransmitterDTO.class, "互联网监管-上传门诊病例", OrganTypeEnum.INTERNET_SUPERVISION),

    // 互联网监管-线上处方点评
    INTERNET_SUPERVISION_ONLINE_PRESCRIPTION_COMMENT(30002, PrescriptionTransmitterDTO.class, "互联网监管-线上处方点评", OrganTypeEnum.INTERNET_SUPERVISION),

    // 互联网监管-诊疗结算
    INTERNET_SUPERVISION_TREATMENT_SETTLEMENT(30003, PrescriptionTransmitterDTO.class, "互联网监管-诊疗结算", OrganTypeEnum.INTERNET_SUPERVISION),

    // 互联网监管-派药
    INTERNET_SUPERVISION_SEND_DRUG(30004, PrescriptionTransmitterDTO.class, "互联网监管-派药", OrganTypeEnum.INTERNET_SUPERVISION),

    // 互联网监管-机构信息采集接口
    INTERNET_SUPERVISION_INSTITUTION_INFO_COLLECTION(30005, PrescriptionTransmitterDTO.class, "互联网监管-机构信息采集接口", OrganTypeEnum.INTERNET_SUPERVISION),

    // 互联网监管-上传派药反馈
    // INTERNET_SUPERVISION_SEND_DRUG_FEEDBACK(30007, "互联网监管-上传派药反馈", OrganTypeEnum.INTERNET_SUPERVISION),

    // 互联网监管-上传机构满意度
    // INTERNET_SUPERVISION_UPLOAD_ORGAN_SATISFACTION(30006, "互联网监管-上传机构满意度", OrganTypeEnum.INTERNET_SUPERVISION),

    // 互联网监管-上传整体服务满意度
    // INTERNET_SUPERVISION_UPLOAD_OVERALL_SERVICE_SATISFACTION(30007, "互联网监管-上传整体服务满意度", OrganTypeEnum.INTERNET_SUPERVISION),

    // 互联网监管-医师诊疗业务备案
    INTERNET_SUPERVISION_PHYSICIAN_DIAGNOSIS_TREATMENT_RECORD(30008, PrescriptionTransmitterDTO.class, "互联网监管-医师诊疗业务备案", OrganTypeEnum.INTERNET_SUPERVISION),
    // 互联网监管-医疗人员信息上报
    INTERNET_SUPERVISION_MEDICAL_PERSONNEL_INFORMATION_REPORT(30009, PrescriptionTransmitterDTO.class, "互联网监管-医疗人员信息上报", OrganTypeEnum.INTERNET_SUPERVISION),
    // 互联网监管-在线复诊信息上报
    // INTERNET_SUPERVISION_ONLINE_REDIAGNOSIS_INFORMATION_REPORT(30011, PrescriptionTransmitterDTO.class, "互联网监管-在线复诊信息上报", OrganTypeEnum.INTERNET_SUPERVISION),
    // 互联网监管-在线处方信息上报
    INTERNET_SUPERVISION_ONLINE_PRESCRIPTION_INFORMATION_REPORT(30012, PrescriptionTransmitterDTO.class, "互联网监管-在线处方信息上报", OrganTypeEnum.INTERNET_SUPERVISION),

    // 互联网监管-上传电子处方
    // INTERNET_SUPERVISION_ELE_PRESCRIPTION_UPLOAD(30015, PrescriptionTransmitterDTO.class, "互联网监管-上传电子处方", OrganTypeEnum.INTERNET_SUPERVISION),

    /**
     * ------------------------------------------------------医保对接----------------------------------------------------------------------
     */

    MEDICARE_SIGN(9001, RegistrationTransmitterDTO.class, "医保对接-签到", OrganTypeEnum.MEDICAL_INSURANCE),

    // 医保对接-人员信息查询
    MEDICARE_PERSON_INFO_QUERY(1101, RegistrationTransmitterDTO.class, "医保对接-人员信息查询", OrganTypeEnum.MEDICAL_INSURANCE),

    // 医保对接-挂号撤销
    MEDICARE_REGISTRATION_CANCEL(2202, RegistrationTransmitterDTO.class, "医保对接-挂号撤销", OrganTypeEnum.MEDICAL_INSURANCE),

    // 医保-上传电子处方
    MEDICAL_ELE_PRESCRIPTION_UPLOAD(30015, PrescriptionTransmitterDTO.class, "医保对接-上传电子处方", OrganTypeEnum.MEDICAL_INSURANCE),

    /**
     * ------------------------------------------------------医保对接----------------------------------------------------------------------
     */

    /**
     * ------------------------------------------------------HIS对接----------------------------------------------------------------------
     */
    // HIS对接 查询挂号信息
    HIS_REGISTRATION_QUERY(40001, RegistrationTransmitterDTO.class, "HIS对接-挂号信息查询", OrganTypeEnum.HIS),

    // HIS对接 处方流转
    // HIS_PRESCRIPTION_TRANSFER(40002, "HIS对接-处方流转", OrganTypeEnum.HIS),

    // HIS对接 处方上传
    // HIS_PRESCRIPTION_UPLOAD(40003, "HIS对接-处方上传", OrganTypeEnum.HIS),

    // HIS对接 查看处方
    // HIS_PRESCRIPTION_CHECK(40004, "HIS对接-查看处方", OrganTypeEnum.HIS),

    // HIS对接 更改预约订单状态
    HIS_PRESCRIPTION_ORDER_STATUS(40005, RegistrationTransmitterDTO.class, "HIS对接-更改预约订单状态", OrganTypeEnum.HIS),

    /**
     * ------------------------------------------------------药监----------------------------------------------------------------------
     */
    DRUG_SUPERVISION_PHARMACIST_SIGN_IN(50001, SignInTransmitterDTO.class, "药监对接-药师签到", OrganTypeEnum.DRUG_SUPERVISION),


    DRUG_SUPERVISION_PHARMACIST_SIGN_OUT(50002, SignInTransmitterDTO.class, "药监对接-药师签退", OrganTypeEnum.DRUG_SUPERVISION),

    // 药监对接-在线处方信息上报
    DRUG_SUPERVISION_PRESCRIPTION_INFO_REPORT(50003, PrescriptionTransmitterDTO.class, "药监对接-在线处方信息上报", OrganTypeEnum.INTERNET_SUPERVISION),

    // 药监对接-在线处方明细信息上报
    DRUG_SUPERVISION_PRESCRIPTION_DETAIL_REPORT(50004, PrescriptionTransmitterDTO.class, "药监对接-在线处方明细信息上报", OrganTypeEnum.INTERNET_SUPERVISION),

    // 药监对接-处方明细延迟上报
    DRUG_SUPERVISION_PRESCRIPTION_DETAIL_REPORT_LATER(50005, PrescriptionTransmitterDTO.class, "药监对接-处方明细延迟上报", OrganTypeEnum.DRUG_SUPERVISION),

    // 药监对接- 处方明细 - 无触发场景仅作依赖任务使用
    DRUG_SUPERVISION_PRESCRIPTION_DETAIL_REPORT_NO_DEPEND(50006,PrescriptionTransmitterDTO.class,"药监对接-在线处方明细信息上报-无触发场景",OrganTypeEnum.DRUG_SUPERVISION),


    ;

    private final Integer code;
    private final Class<? extends TransmissionReqDataBaseDTO> reqClazz;
    private final String desc;
    private final OrganTypeEnum organType;

    public static NodeTypeEnum fromCode(Integer code) {
        for (NodeTypeEnum item : values()) {
            if (Objects.equals(item.getCode(), code)) {
                return item;
            }
        }
        return null;
    }
}