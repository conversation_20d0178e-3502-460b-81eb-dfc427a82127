package com.xyy.saas.inquiry.pojo.parmacist;

import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Author:chenxiaoyi
 * @Date:2025/05/14 11:20
 */
@Data
@Schema(description = "药师扩展信息ext")
@Accessors(chain = true)
public class PharmacistExtDto implements Serializable {

    private List<Integer> servicePackIds;
    /**
     * 是否需要人脸审核 默认否 {@link cn.iocoder.yudao.framework.common.enums.CommonStatusEnum}
     */
    @Schema(description = "普通审方是否需要人脸审核 0是 1否")
    private Integer faceAudit = CommonStatusEnum.DISABLE.getStatus();

    /**
     * 是否需要人脸审核 默认是 {@link cn.iocoder.yudao.framework.common.enums.CommonStatusEnum}
     */
    @Schema(description = "远程审方是否需要人脸审核 0是 1否")
    private Integer remoteFaceAudit = CommonStatusEnum.ENABLE.getStatus();

}
