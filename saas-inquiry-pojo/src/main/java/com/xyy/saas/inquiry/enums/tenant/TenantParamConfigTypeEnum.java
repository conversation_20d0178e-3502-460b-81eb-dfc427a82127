package com.xyy.saas.inquiry.enums.tenant;

import cn.iocoder.yudao.framework.common.core.IntArrayValuable;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;
import java.util.Objects;

/**
 * @Desc 门店参数配置类型枚举
 * <AUTHOR>
 */
@Getter
@RequiredArgsConstructor
public enum TenantParamConfigTypeEnum implements IntArrayValuable {

    // 0 -  10000  门店基础参数配置
    @Deprecated
    LL(0, "ll", "经纬度", "0,0", "经纬度(百度地图)"),

    // 100001 -  20000  门店问诊参数参数配置

    INQUIRY_QR_CODE(10001, "inquiryQrcode", "小程序店铺二维码", "url", "小程序店铺二维码"),

    INQUIRY_MP_CODE(10002, "inquiryMpCode", "问诊二维码", "url", "问诊公众号二维码"),

    INQUIRY_SERVER(10003, "inquiryServer", "荷叶问诊服务", "CommonStatusEnum", "荷叶问诊服务"),

    INQUIRY_PRES_AUDIT_TYPE(10004, "inquiryPresAuditType", "审方类型", "PrescriptionAuditTypeEnum", "门店审方类型"),

    INQUIRY_WESTERN_MEDICINE_BRING(10005, "inquiryWesternMedicineBring", "问诊带出上次药品-西药", "CommonStatusEnum", "西药问诊是否带出上次药品"),

    INQUIRY_CHINESE_MEDICINE_BRING(10006, "inquiryChineseMedicineBring", "问诊带出上次药品-中药", "CommonStatusEnum", "中药问诊是否带出上次药品"),

    @Deprecated
    INQUIRY_DISTANCE_LIMIT_SWITCH(10007, "distanceLimitSwitch", "小程序问诊离店使用", "CommonStatusEnum", "小程序问诊离店使用"),

    INQUIRY_PRES_AUDIT_SWITCH(10008, "inquiryPresAuditSwitch", "小程序问诊需门店审核", "CommonStatusEnum", "小程序问诊需门店审核"),

    // 20001 - 30000 处方笺配置

    INQUIRY_PRES_DATE_TYPE(20001, "inquiryPresDateType", "处方日期格式", "PrescriptionDateTypeEnum", "处方日期格式"),

    // 30001 - 40000 处方相关

    PRESCRIPTION_REMOTE_TO_HEAD_SWITCH(30001, "prescriptionRemoteToHeadSwitch", "远程审方流向总部药师", "CommonStatusEnum", "远程审方流向总部药师"),

    PRESCRIPTION_REMOTE_AUDIT_TYPE(30002, "prescriptionRemoteAuditType", "远程审方审方类型", "PrescriptionRemoteAuditTypeEnum", "远程审方审方类型"),

    ;

    private final int type;

    private final String field;

    private final String name;

    /**
     * 枚举类，可能没有
     */
    private final String enumDesc;

    private final String description;

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(TenantParamConfigTypeEnum::getType).toArray();

    @Override
    public int[] array() {
        return ARRAYS;
    }

    public static TenantParamConfigTypeEnum fromType(int type) {
        return Arrays.stream(values())
            .filter(value -> Objects.equals(value.getType(), type))
            .findFirst()
            .orElse(null);
    }

}