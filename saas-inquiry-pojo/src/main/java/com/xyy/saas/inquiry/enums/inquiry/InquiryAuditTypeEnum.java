package com.xyy.saas.inquiry.enums.inquiry;

import cn.iocoder.yudao.framework.common.core.IntArrayValuable;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;
import java.util.Objects;
import java.util.stream.Stream;

/**
 * @Desc 问诊审方类型
 */
@Getter
@RequiredArgsConstructor
public enum InquiryAuditTypeEnum implements IntArrayValuable {

    DRUGSTORE(1, "门店审方"),

    PLATFORM(2, "平台审方"),

    CHAIN(3, "连锁审方"),
    ;

    private final int code;

    private final String desc;

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(InquiryAuditTypeEnum::getCode).toArray();

    @Override
    public int[] array() {
        return ARRAYS;
    }

    /**
     * 根据code获取枚举值
     *
     * @param code 枚举值code
     * @return 枚举值
     */
    public static InquiryAuditTypeEnum fromCode(Integer code) {
        return Arrays.stream(values())
            .filter(type -> Objects.equals(type.getCode(), code))
            .findFirst()
            .orElse(DRUGSTORE);
    }

    /**
     * 是否是门店审方，将连锁审方和门店审方合并至一起
     *
     * @param auditType
     * @return
     */
    public static boolean isStoreInquiryAuditType(Integer auditType) {
        return Stream.of(DRUGSTORE.getCode(), CHAIN.getCode()).toList().contains(auditType);
    }
}