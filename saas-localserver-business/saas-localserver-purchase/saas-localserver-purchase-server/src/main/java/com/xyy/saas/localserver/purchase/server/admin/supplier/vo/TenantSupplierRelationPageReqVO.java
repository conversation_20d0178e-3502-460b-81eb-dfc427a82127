package com.xyy.saas.localserver.purchase.server.admin.supplier.vo;

import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 管理后台 - 租户-供应商-关联关系分页 Request VO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "管理后台 - 租户-供应商-关联关系分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TenantSupplierRelationPageReqVO extends PageParam {

    /** 供应商编号 */
    @Schema(description = "供应商编号")
    private String supplierGuid;

    /** 首营状态 */
    @Schema(description = "首营状态：1-审核中，2-审核通过，3-审核未通过", example = "2")
    private Integer status;

    /** 创建时间 */
    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}