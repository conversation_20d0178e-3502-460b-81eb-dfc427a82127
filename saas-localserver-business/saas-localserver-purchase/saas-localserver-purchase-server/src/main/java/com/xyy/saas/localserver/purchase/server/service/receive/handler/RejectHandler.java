package com.xyy.saas.localserver.purchase.server.service.receive.handler;

import com.xyy.saas.localserver.purchase.api.receive.dto.ReceiveBillDTO;
import com.xyy.saas.localserver.purchase.api.receive.dto.ReceiveBillDetailDTO;
import com.xyy.saas.localserver.purchase.enums.receive.ReceiveBillTypeEnum;
import java.util.List;

/**
 * 拒收处理器接口
 * 用于处理不同类型的收货单拒收业务逻辑
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface RejectHandler {

    /**
     * 获取支持的收货单类型
     * 返回该处理器可以处理的收货单类型列表
     *
     * @return 支持的收货单类型列表
     */
    List<ReceiveBillTypeEnum> supportReceiveBillTypes();

    /**
     * 处理拒收业务
     * 处理流程：
     * 1. 根据收货单类型进行相应的拒收处理
     * 2. 更新相关业务状态
     * 3. 记录拒收信息
     *
     * @param receiveBill 收货单信息
     * @param details     拒收的详情列表
     */
    void handle(ReceiveBillDTO receiveBill, List<ReceiveBillDetailDTO> details);
}