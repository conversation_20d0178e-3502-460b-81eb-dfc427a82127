package com.xyy.saas.localserver.purchase.server.convert.supplier;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.localserver.purchase.api.supplier.dto.SupplierDTO;
import com.xyy.saas.localserver.purchase.api.supplier.dto.SupplierPageReqDTO;
import com.xyy.saas.localserver.purchase.server.admin.supplier.vo.SupplierPageReqVO;
import com.xyy.saas.localserver.purchase.server.admin.supplier.vo.SupplierRespVO;
import com.xyy.saas.localserver.purchase.server.admin.supplier.vo.SupplierSaveReqVO;
import com.xyy.saas.localserver.purchase.server.dal.dataobject.supplier.SupplierDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 供应商转换器
 * <p>
 * 主要功能：
 * 1. 基础转换 - VO/DO/DTO之间的转换
 */
@Mapper
public interface SupplierConvert {

    SupplierConvert INSTANCE = Mappers.getMapper(SupplierConvert.class);

    // ========== 1. 基础转换方法 ==========

    /**
     * DTO 转 DO
     * 
     * @param supplierDTO 供应商DTO
     * @return 供应商DO
     */
    SupplierDO convert2DO(SupplierDTO supplierDTO);

    /**
     * DO 转 DTO
     * 
     * @param supplier 供应商DO
     * @return 供应商DTO
     */
    SupplierDTO convert2DTO(SupplierDO supplier);

    /**
     * DO 转 DTO
     * 
     * @param pageResult 供应商DO分页结果
     * @return 供应商DTO分页结果
     */
    PageResult<SupplierDTO> convert2DTO(PageResult<SupplierDO> pageResult);

    /**
     * VO 转 DTO
     * 
     * @param saveReqVO 供应商保存请求VO
     * @return 供应商DTO
     */
    SupplierDTO convert2DTO(SupplierSaveReqVO saveReqVO);

    /**
     * VO 转 DTO
     * 
     * @param pageReqVO 供应商分页查询VO
     * @return 供应商分页查询DTO
     */
    SupplierPageReqDTO convert2DTO(SupplierPageReqVO pageReqVO);

    /**
     * DTO 转 VO
     * 
     * @param supplierDTO 供应商DTO
     * @return 供应商响应VO
     */
    SupplierRespVO convert2VO(SupplierDTO supplierDTO);

    /**
     * DTO列表 转 VO列表
     * 
     * @param list 供应商DTO列表
     * @return 供应商响应VO列表
     */
    List<SupplierRespVO> convert2VOList(List<SupplierDTO> list);

    /**
     * DTO 转 VO
     * 
     * @param pageResultDTO 供应商DTO分页结果
     * @return 供应商响应VO分页结果
     */
    PageResult<SupplierRespVO> convert2VO(PageResult<SupplierDTO> pageResultDTO);
}
