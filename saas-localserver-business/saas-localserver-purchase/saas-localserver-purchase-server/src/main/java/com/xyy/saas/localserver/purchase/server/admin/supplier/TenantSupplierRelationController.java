package com.xyy.saas.localserver.purchase.server.admin.supplier;

import com.xyy.saas.localserver.purchase.api.supplier.dto.TenantSupplierRelationDTO;
import com.xyy.saas.localserver.purchase.api.supplier.dto.TenantSupplierRelationPageReqDTO;
import com.xyy.saas.localserver.purchase.server.admin.supplier.vo.TenantSupplierRelationPageReqVO;
import com.xyy.saas.localserver.purchase.server.admin.supplier.vo.TenantSupplierRelationRespVO;
import com.xyy.saas.localserver.purchase.server.admin.supplier.vo.TenantSupplierRelationSaveReqVO;
import com.xyy.saas.localserver.purchase.server.convert.supplier.TenantSupplierRelationConvert;
import com.xyy.saas.localserver.purchase.server.service.supplier.TenantSupplierRelationService;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

@Tag(name = "管理后台 - 租户-供应商-关联关系")
@RestController
@RequestMapping("/saas/purchase/tenant-supplier-relation")
@Validated
public class TenantSupplierRelationController {

        @Resource
        private TenantSupplierRelationService tenantSupplierRelationService;

        /**
         * 创建租户-供应商关联关系
         *
         * <p>
         * 该接口用于创建新的租户与供应商之间的关联关系，主要处理以下步骤：
         * </p>
         * <ol>
         * <li>将请求对象转换为DTO对象</li>
         * <li>调用Service层创建关联关系</li>
         * </ol>
         *
         * @param createReqVO 创建关联关系的请求对象，包含租户和供应商的关联信息
         * @return 关联关系编号
         */
        @PostMapping("/create")
        @Operation(summary = "创建租户-供应商-关联关系")
        @PreAuthorize("@ss.hasPermission('saas:purchase:tenant-supplier-relation:create')")
        public CommonResult<Long> createTenantSupplierRelation(
                        @Valid @RequestBody TenantSupplierRelationSaveReqVO createReqVO) {
                TenantSupplierRelationDTO tenantSupplierRelationDTO = TenantSupplierRelationConvert.INSTANCE
                                .convert2DTO(createReqVO);

                return success(tenantSupplierRelationService.createTenantSupplierRelation(tenantSupplierRelationDTO));
        }

        /**
         * 更新租户-供应商关联关系
         *
         * <p>
         * 该接口用于更新已存在的租户与供应商之间的关联关系，主要处理以下步骤：
         * </p>
         * <ol>
         * <li>将请求对象转换为DTO对象</li>
         * <li>调用Service层更新关联关系</li>
         * </ol>
         *
         * @param updateReqVO 更新关联关系的请求对象，包含需要更新的关联信息
         * @return 是否更新成功
         */
        @PutMapping("/update")
        @Operation(summary = "更新租户-供应商-关联关系")
        @PreAuthorize("@ss.hasPermission('saas:purchase:tenant-supplier-relation:update')")
        public CommonResult<Boolean> updateTenantSupplierRelation(
                        @Valid @RequestBody TenantSupplierRelationSaveReqVO updateReqVO) {
                TenantSupplierRelationDTO tenantSupplierRelation = TenantSupplierRelationConvert.INSTANCE
                                .convert2DTO(updateReqVO);

                tenantSupplierRelationService.updateTenantSupplierRelation(tenantSupplierRelation);
                return success(true);
        }

        /**
         * 删除租户-供应商关联关系
         *
         * <p>
         * 该接口用于删除指定的租户与供应商之间的关联关系，主要处理以下步骤：
         * </p>
         * <ol>
         * <li>调用Service层删除关联关系</li>
         * </ol>
         *
         * @param id 关联关系编号
         * @return 是否删除成功
         */
        @DeleteMapping("/delete")
        @Operation(summary = "删除租户-供应商-关联关系")
        @Parameter(name = "id", description = "编号", required = true)
        @PreAuthorize("@ss.hasPermission('saas:purchase:tenant-supplier-relation:delete')")
        public CommonResult<Boolean> deleteTenantSupplierRelation(@RequestParam("id") Long id) {
                tenantSupplierRelationService.deleteTenantSupplierRelation(id);
                return success(true);
        }

        /**
         * 获取租户-供应商关联关系
         *
         * <p>
         * 该接口用于获取指定的租户与供应商之间的关联关系详情，主要处理以下步骤：
         * </p>
         * <ol>
         * <li>调用Service层查询关联关系</li>
         * <li>将DTO对象转换为VO对象返回</li>
         * </ol>
         *
         * @param id 关联关系编号
         * @return 关联关系详细信息
         */
        @GetMapping("/get")
        @Operation(summary = "获得租户-供应商-关联关系")
        @Parameter(name = "id", description = "编号", required = true, example = "1024")
        @PreAuthorize("@ss.hasPermission('saas:purchase:tenant-supplier-relation:query')")
        public CommonResult<TenantSupplierRelationRespVO> getTenantSupplierRelation(@RequestParam("id") Long id) {
                TenantSupplierRelationDTO tenantSupplierRelationDTO = tenantSupplierRelationService
                                .getTenantSupplierRelation(id);
                return success(TenantSupplierRelationConvert.INSTANCE.convert2VO(tenantSupplierRelationDTO));
        }

        /**
         * 获取租户-供应商关联关系分页
         *
         * <p>
         * 该接口用于分页查询租户与供应商之间的关联关系，主要处理以下步骤：
         * </p>
         * <ol>
         * <li>将请求对象转换为DTO对象</li>
         * <li>调用Service层进行分页查询</li>
         * <li>将查询结果转换为VO对象返回</li>
         * </ol>
         *
         * @param pageReqVO 分页查询条件
         * @return 分页查询结果
         */
        @GetMapping("/page")
        @Operation(summary = "获得租户-供应商-关联关系分页")
        @PreAuthorize("@ss.hasPermission('saas:purchase:tenant-supplier-relation:query')")
        public CommonResult<PageResult<TenantSupplierRelationRespVO>> getTenantSupplierRelationPage(
                        @Valid TenantSupplierRelationPageReqVO pageReqVO) {
                TenantSupplierRelationPageReqDTO pageReqDTO = TenantSupplierRelationConvert.INSTANCE
                                .convert2DTO(pageReqVO);
                PageResult<TenantSupplierRelationDTO> pageResult = tenantSupplierRelationService
                                .getTenantSupplierRelationPage(pageReqDTO);
                return success(TenantSupplierRelationConvert.INSTANCE.convert2VO(pageResult));
        }

        /**
         * 导出租户-供应商关联关系Excel
         *
         * <p>
         * 该接口用于导出租户与供应商之间的关联关系到Excel文件，主要处理以下步骤：
         * </p>
         * <ol>
         * <li>设置不分页查询</li>
         * <li>查询所有符合条件的关联关系</li>
         * <li>将数据导出为Excel文件</li>
         * </ol>
         *
         * @param pageReqVO 查询条件
         * @param response  HTTP响应对象
         * @throws IOException 导出过程中的IO异常
         */
        @GetMapping("/export-excel")
        @Operation(summary = "导出租户-供应商-关联关系 Excel")
        @PreAuthorize("@ss.hasPermission('saas:purchase:tenant-supplier-relation:export')")
        @ApiAccessLog(operateType = EXPORT)
        public void exportTenantSupplierRelationExcel(@Valid TenantSupplierRelationPageReqVO pageReqVO,
                        HttpServletResponse response) throws IOException {
                pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
                TenantSupplierRelationPageReqDTO pageReqDTO = TenantSupplierRelationConvert.INSTANCE
                                .convert2DTO(pageReqVO);
                List<TenantSupplierRelationDTO> list = tenantSupplierRelationService
                                .getTenantSupplierRelationPage(pageReqDTO)
                                .getList();
                // 导出 Excel
                ExcelUtils.write(response, "租户-供应商-关联关系.xls", "数据", TenantSupplierRelationRespVO.class,
                                TenantSupplierRelationConvert.INSTANCE.convert2VOList(list));
        }

}