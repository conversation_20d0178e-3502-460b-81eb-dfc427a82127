package com.xyy.saas.localserver.purchase.server.service.receive.handler.impl;

import cn.hutool.core.lang.Assert;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import com.xyy.saas.localserver.purchase.api.receive.dto.ReceiveBillDTO;
import com.xyy.saas.localserver.purchase.api.receive.dto.ReceiveBillDetailDTO;
import com.xyy.saas.localserver.purchase.enums.receive.ReceiveBillTypeEnum;
import com.xyy.saas.localserver.purchase.server.admin.receive.vo.ReceiveBillSaveReqVO;
import com.xyy.saas.localserver.purchase.server.convert.receive.ReceiveBillConvert;
import com.xyy.saas.localserver.purchase.server.protocol.CloudServiceClient;
import com.xyy.saas.localserver.purchase.server.protocol.receive.ReceiveClient;
import com.xyy.saas.localserver.purchase.server.service.receive.handler.RejectHandler;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;
import java.util.List;
import static com.xyy.saas.localserver.purchase.enums.receive.ReceiveBillTypeEnum.*;

/**
 * 门店租户拒收处理器
 * 处理门店租户的收货单拒收业务，包括配送收货和要货收货两种类型
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
public class StoreRejectHandler implements RejectHandler {

    @Resource
    private CloudServiceClient cloudServiceClient;

    /**
     * 获取支持的收货单类型
     * 支持以下类型：
     * 1. 配送收货单
     * 2. 要货收货单
     *
     * @return 支持的收货单类型列表
     */
    @Override
    public List<ReceiveBillTypeEnum> supportReceiveBillTypes() {
        return List.of(
                DISTRIBUTION_RECEIVE,
                REQUISITION_RECEIVE);
    }

    /**
     * 处理门店租户拒收业务
     * 处理流程：
     * 1. 创建总部收货单：将门店拒收信息转换为总部收货单
     * 2. 记录消息表：保存拒收相关信息
     * 3. 调用云端接口：生成总部收货单并自动完成
     *
     * @param receiveBill 收货单信息
     * @param details     拒收的详情列表
     * @throws IllegalStateException 当云端接口调用失败时抛出
     */
    @Override
    public void handle(ReceiveBillDTO receiveBill, List<ReceiveBillDetailDTO> details) {
        // 1. 创建总部收货单：将门店拒收信息转换为总部收货单
        ReceiveBillSaveReqVO headReceiveBill = ReceiveBillConvert.INSTANCE.generateStoreRejectWarehouseBill(receiveBill,
                details);

        // 2. 记录消息表
        // TODO: 实现消息记录逻辑

        // 3. 调用云端接口：生成总部收货单并自动完成
        CommonResult<Boolean> result = cloudServiceClient
                .call(() -> cloudServiceClient.getClient(ReceiveClient.class).rejectWarehousing(headReceiveBill));
        Assert.state(result.isSuccess(), result.getMsg());
        Assert.state(result.getData(), result.getMsg());
    }
}