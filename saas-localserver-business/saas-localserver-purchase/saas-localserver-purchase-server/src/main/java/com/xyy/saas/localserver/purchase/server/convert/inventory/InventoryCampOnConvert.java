package com.xyy.saas.localserver.purchase.server.convert.inventory;

import com.xyy.saas.localserver.entity.enums.bill.BillTypeEnum;
import com.xyy.saas.localserver.inventory.api.campon.dto.InventoryCampOnDTO;
import com.xyy.saas.localserver.inventory.api.campon.dto.InventoryCampOnQueryDTO;
import com.xyy.saas.localserver.inventory.api.campon.dto.InventorySimpleReleaseDTO;
import com.xyy.saas.localserver.inventory.enums.InventorySelectEnum;
import com.xyy.saas.localserver.purchase.api.purchase.dto.PurchaseBillDTO;
import com.xyy.saas.localserver.purchase.api.returned.dto.ReturnBillDTO;
import com.xyy.saas.localserver.purchase.enums.purchase.PurchaseBillTypeEnum;
import com.xyy.saas.localserver.purchase.enums.returned.ReturnBillTypeEnum;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.EnumMap;
import java.util.Optional;

/**
 * 库存预占单转换器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper(uses = { InventorySelectItemConvert.class })
public interface InventoryCampOnConvert {

        InventoryCampOnConvert INSTANCE = Mappers.getMapper(InventoryCampOnConvert.class);

        /**
         * 将退货单转换为库存预占信息
         *
         * @param returnBill 退货单
         * @return 库存预占信息
         */
        default InventoryCampOnDTO convert2InventoryCampOn(ReturnBillDTO returnBill) {
                final InventoryCampOnDTO campOnDTO = InventoryCampOnDTO.builder()
                                .billNo(returnBill.getBillNo())
                                .billType(BillTypeEnum.RETURN.billType)
                                .allowSelectPartial(ReturnBillTypeEnum.fromCode(returnBill.getBillType())
                                                .getAllowPartialCampOn())
                                .selectMap(new EnumMap<>(InventorySelectEnum.class)) // 使用EnumMap优化性能
                                .build();

                Optional.ofNullable(returnBill.getDetails())
                                .ifPresent(details -> details.forEach(detail -> InventorySelectItemConvert.INSTANCE
                                                .convert2SelectItem(detail, campOnDTO.getSelectMap())));

                return campOnDTO;
        }

        /**
         * 将采购单转换为库存预占信息
         *
         * @param purchaseBill 采购单
         * @param tenantId     租户ID
         * @return 库存预占信息
         */
        default InventoryCampOnDTO convert2InventoryCampOn(PurchaseBillDTO purchaseBill, Long tenantId) {
                PurchaseBillTypeEnum purchaseBillType = PurchaseBillTypeEnum.fromCode(purchaseBill.getBillType());
                final InventoryCampOnDTO campOnDTO = InventoryCampOnDTO.builder()
                                .tenantId(tenantId)
                                .billNo(purchaseBill.getPurchaseBillNo())
                                .billType(purchaseBillType.getBillType().billType)
                                .allowSelectPartial(purchaseBillType.getAllowPartialCampOn())
                                .selectMap(new EnumMap<>(InventorySelectEnum.class)) // 使用EnumMap优化性能
                                .build();

                Optional.ofNullable(purchaseBill.getDetails())
                                .ifPresent(details -> details.forEach(detail -> InventorySelectItemConvert.INSTANCE
                                                .convert2SelectItem(detail, campOnDTO.getSelectMap())));

                return campOnDTO;
        }

        /**
         * 转换为释放预占对象
         *
         * @param returnBill 退货单
         * @return 释放预占对象
         */
        default InventorySimpleReleaseDTO convert2ReleaseCampOnDto(ReturnBillDTO returnBill) {
                return InventorySimpleReleaseDTO.builder()
                                .sourceNo(returnBill.getBillNo())
                                .tenantId(returnBill.getTenantId())
                                .build();
        }

        /**
         * 调剂单转换为释放预占对象
         *
         * @param returnBill 退货单
         * @return 释放预占对象
         */
        default InventorySimpleReleaseDTO convertAllocation2ReleaseCampOnDto(ReturnBillDTO returnBill) {
                return InventorySimpleReleaseDTO.builder()
                                .sourceNo(returnBill.getPurchaseBillNo())
                                .tenantId(returnBill.getTenantId())
                                .build();
        }

        /**
         * 转换为查询预占对象
         *
         * @param returnBill 退货单
         * @return 查询预占对象
         */
        default InventoryCampOnQueryDTO convert2QueryCampOnDto(ReturnBillDTO returnBill) {
                return InventoryCampOnQueryDTO.builder()
                                .sourceNo(returnBill.getBillNo())
                                .tenantId(returnBill.getTenantId())
                                .build();
        }

}