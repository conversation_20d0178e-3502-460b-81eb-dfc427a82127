package com.xyy.saas.localserver.purchase.server.service.extend.impl;

import com.xyy.saas.localserver.purchase.api.extend.dto.PurchaseInvoiceDTO;
import com.xyy.saas.localserver.purchase.api.extend.dto.PurchaseInvoicePageReqDTO;
import com.xyy.saas.localserver.purchase.server.convert.extend.PurchaseInvoiceConvert;
import com.xyy.saas.localserver.purchase.server.dal.dataobject.extend.PurchaseInvoiceDO;
import com.xyy.saas.localserver.purchase.server.dal.mysql.extend.PurchaseInvoiceMapper;
import com.xyy.saas.localserver.purchase.server.service.extend.PurchaseInvoiceService;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import jakarta.annotation.Resource;

/**
 * 采购-发票信息 Service 实现类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
@Validated
public class PurchaseInvoiceServiceImpl implements PurchaseInvoiceService {

    @Resource
    private PurchaseInvoiceMapper purchaseInvoiceMapper;

    /**
     * 创建采购-发票信息
     *
     * @param createDTO 创建信息
     * @return 编号
     */
    @Override
    public Long createPurchaseInvoice(PurchaseInvoiceDTO createDTO) {
        // 1. 将 DTO 转换为 DO
        PurchaseInvoiceDO invoice = PurchaseInvoiceConvert.INSTANCE.convert2DO(createDTO);
        // 2. 插入数据
        purchaseInvoiceMapper.insert(invoice);
        // 3. 返回主键
        return invoice.getId();
    }

    /**
     * 更新采购-发票信息
     *
     * @param updateDTO 更新信息
     */
    @Override
    public void updatePurchaseInvoice(PurchaseInvoiceDTO updateDTO) {
        // 1. 校验存在
        validatePurchaseInvoiceExists(updateDTO.getId());
        // 2. 将 DTO 转换为 DO
        PurchaseInvoiceDO updateObj = PurchaseInvoiceConvert.INSTANCE.convert2DO(updateDTO);
        // 3. 更新数据
        purchaseInvoiceMapper.updateById(updateObj);
    }

    /**
     * 删除采购-发票信息
     *
     * @param id 编号
     */
    @Override
    public void deletePurchaseInvoice(Long id) {
        // 1. 校验存在
        validatePurchaseInvoiceExists(id);
        // 2. 删除数据
        purchaseInvoiceMapper.deleteById(id);
    }

    /**
     * 获得采购-发票信息
     *
     * @param id 编号
     * @return 采购-发票信息
     */
    @Override
    public PurchaseInvoiceDTO getPurchaseInvoice(Long id) {
        // 1. 查询数据
        PurchaseInvoiceDO invoice = purchaseInvoiceMapper.selectById(id);
        // 2. 转换结果
        return PurchaseInvoiceConvert.INSTANCE.convert2DTO(invoice);
    }

    /**
     * 获得采购-发票信息分页
     *
     * @param pageReqDTO 分页查询参数
     * @return 采购-发票信息分页
     */
    @Override
    public PageResult<PurchaseInvoiceDTO> getPurchaseInvoicePage(PurchaseInvoicePageReqDTO pageReqDTO) {
        // 1. 分页查询
        PageResult<PurchaseInvoiceDO> pageResult = purchaseInvoiceMapper.selectPage(pageReqDTO);
        // 2. 转换结果
        return PurchaseInvoiceConvert.INSTANCE.convert2DTO(pageResult);
    }

    private void validatePurchaseInvoiceExists(Long id) {
        if (purchaseInvoiceMapper.selectById(id) == null) {
            throw new RuntimeException("采购-发票信息不存在");
        }
    }

}