package com.xyy.saas.localserver.purchase.server.service.receive.handler.stockin;

import com.xyy.saas.localserver.entity.enums.tenant.TenantTypeEnum;
import com.xyy.saas.localserver.inventory.api.change.InventoryChangeDetailApi;
import com.xyy.saas.localserver.inventory.api.change.dto.InventoryChangeDetailDTO;
import com.xyy.saas.localserver.inventory.api.change.dto.InventoryChangeDetailQueryDTO;
import com.xyy.saas.localserver.inventory.api.stock.dto.InventorySelectItemDTO;
import com.xyy.saas.localserver.inventory.api.tracecode.dto.TraceCodeChangeDTO;
import com.xyy.saas.localserver.inventory.enums.InventorySelectEnum;
import com.xyy.saas.localserver.purchase.api.receive.dto.ReceiveBillDTO;
import com.xyy.saas.localserver.purchase.api.receive.dto.ReceiveBillDetailDTO;
import com.xyy.saas.localserver.purchase.enums.receive.ReceiveBillTypeEnum;
import com.xyy.saas.localserver.purchase.server.convert.inventory.InventorySelectItemConvert;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 库存入库处理器抽象基类
 * 
 * <p>
 * 该抽象类定义了库存入库处理的通用流程和公共方法，
 * 各种收货单类型的具体处理器继承此类并实现特定的业务逻辑。
 * </p>
 * 
 * <p>
 * 主要职责：
 * </p>
 * <ul>
 * <li>定义库存入库处理的标准流程</li>
 * <li>提供通用的数据查询和处理方法</li>
 * <li>封装库存明细项的构建逻辑</li>
 * <li>处理追溯码和批次信息的映射</li>
 * </ul>
 * 
 * <p>
 * 处理流程：
 * </p>
 * <ol>
 * <li>接收处理请求</li>
 * <li>处理收货单明细</li>
 * <li>清理缓存</li>
 * <li>返回处理结果</li>
 * </ol>
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
public abstract class AbstractStockInHandler {

    /** 库存变更明细API */
    @Resource
    protected InventoryChangeDetailApi inventoryChangeDetailApi;

    /** 库存选择项转换器 */
    @Resource
    protected InventorySelectItemConvert inventorySelectItemConvert;

    /** 库存变更明细缓存（避免重复查询） */
    private final Map<String, List<InventoryChangeDetailDTO>> changeDetailCache = new ConcurrentHashMap<>();

    // ========== 抽象方法 ==========

    /**
     * 获取支持的收货单类型
     * 
     * @return 支持的收货单类型集合
     */
    public abstract Set<ReceiveBillTypeEnum> getSupportedBillTypes();

    /**
     * 获取支持的租户类型
     * 
     * @return 支持的租户类型集合
     */
    public abstract Set<TenantTypeEnum> getSupportedTenantTypes();

    /**
     * 根据入库单处理库存入库数据
     * 
     * <p>
     * 子类需要实现此方法来处理具体的收货明细逻辑。
     * </p>
     * 
     * @param receiveBill 收货单
     * @param selectMap   库存选择项集合
     */
    protected abstract void processStockInItemsByReceiveBill(ReceiveBillDTO receiveBill,
            Map<InventorySelectEnum, List<InventorySelectItemDTO>> selectMap);

    // ========== 公共方法 ==========

    /**
     * 处理库存入库参数
     * 
     * <p>
     * 这是模板方法，定义了库存入库处理的标准流程：
     * 1. 处理收货单明细
     * 2. 清理缓存
     * </p>
     * 
     * @param receiveBill 收货单
     * @param selectMap   库存选择项集合
     */
    public final void processStockInItems(ReceiveBillDTO receiveBill,
            Map<InventorySelectEnum, List<InventorySelectItemDTO>> selectMap) {
        long startTime = System.currentTimeMillis();
        String billNo = receiveBill.getBillNo();

        log.info("开始处理库存入库 - 单据号: {}, 类型: {}, 租户类型: {}",
                billNo,
                ReceiveBillTypeEnum.fromCode(receiveBill.getBillType()),
                TenantTypeEnum.fromCode(receiveBill.getTenantType()));

        // 1. 处理收货单明细
        processStockInItemsByReceiveBill(receiveBill, selectMap);

        // 2. 清理缓存
        clearCache();

        long costTime = System.currentTimeMillis() - startTime;

        // 记录性能警告
        if (costTime > 5000) {
            log.warn("库存入库处理耗时过长 - 单据号: {}, 耗时: {}ms", billNo, costTime);
        }
    }

    /**
     * 查询库存变更明细（带缓存）
     *
     * @param dataSource 数据源
     * @param billNo     单据号
     * @param tenantId   租户ID
     * @return 库存变更明细列表
     */
    protected List<InventoryChangeDetailDTO> queryInventoryChangeDetails(DataSource dataSource, String billNo,
            Long tenantId) {
        if (StringUtils.isBlank(billNo) || tenantId == null) {
            log.warn("查询库存变更明细参数无效 - billNo: {}, tenantId: {}", billNo, tenantId);
            return Collections.emptyList();
        }

        // 使用缓存避免重复查询
        String cacheKey = billNo + ":" + tenantId;
        return changeDetailCache.computeIfAbsent(cacheKey,
                key -> executeInventoryChangeDetailQuery(dataSource, billNo, tenantId));
    }

    /**
     * 按商品编码和批号分组库存变更明细
     * 
     * @param changeDetails 库存变更明细列表
     * @return 分组后的映射，key为"商品编码:批号"
     */
    protected Map<String, List<InventoryChangeDetailDTO>> groupChangeDetailsByProductAndLot(
            List<InventoryChangeDetailDTO> changeDetails) {

        if (CollectionUtils.isEmpty(changeDetails)) {
            return Collections.emptyMap();
        }

        try {
            return changeDetails.parallelStream()
                    .filter(detail -> StringUtils.isNotBlank(detail.getProductPref()) &&
                            StringUtils.isNotBlank(detail.getLotNo()))
                    .collect(Collectors.groupingBy(
                            detail -> buildDetailKey(detail.getProductPref(), detail.getLotNo()),
                            Collectors.toList()));
        } catch (NullPointerException e) {
            log.warn("库存变更明细数据包含空值，已过滤处理 - 错误: {}", e.getMessage());
            return Collections.emptyMap();
        }
    }

    /**
     * 构建明细键
     * 
     * @param productPref 商品编码
     * @param lotNo       批号
     * @return 明细键
     */
    protected String buildDetailKey(String productPref, String lotNo) {
        return productPref + ":" + lotNo;
    }

    /**
     * 过滤指定追踪前缀的追溯码
     * 
     * @param traceCodes 追溯码列表
     * @param tracePref  追踪前缀
     * @return 过滤后的追溯码列表
     */
    protected List<TraceCodeChangeDTO> filterTraceCodesByTracePref(List<TraceCodeChangeDTO> traceCodes,
            String tracePref) {
        return inventorySelectItemConvert.filterTraceCodesByTracePref(traceCodes, tracePref);
    }

    /**
     * 处理合格和不合格数量分配
     * 
     * <p>
     * 按照先进先出原则分配合格和不合格数量到不同的货位。
     * </p>
     * 
     * @param detail      收货明细
     * @param changeStock 可变更库存数量
     * @return 货位库存变更映射
     */
    protected Map<String, BigDecimal> allocateQualifiedAndUnqualifiedQuantity(ReceiveBillDetailDTO detail,
            BigDecimal changeStock) {
        return inventorySelectItemConvert.allocateQualifiedAndUnqualifiedQuantity(detail, changeStock);
    }

    /**
     * 更新剩余数量
     * 
     * <p>
     * 根据已分配的数量更新合格和不合格的剩余数量，并判断是否已分配完毕。
     * </p>
     * 
     * @param positionChangeStock 货位库存变更映射
     * @param quantities          数量数组[合格数量, 不合格数量]
     * @return 是否已分配完毕
     */
    protected boolean updateRemainingQuantities(Map<String, BigDecimal> positionChangeStock,
            BigDecimal[] quantities) {
        return inventorySelectItemConvert.updateRemainingQuantities(positionChangeStock, quantities);
    }

    /**
     * 获取收货明细中的追溯码
     * 
     * @param detail 收货明细
     * @return 追溯码列表
     */
    protected List<TraceCodeChangeDTO> getDetailTraceCodes(ReceiveBillDetailDTO detail) {
        return inventorySelectItemConvert.getDetailTraceCodes(detail);
    }

    /**
     * 根据源单据批次按先进先出原则分配入库批次
     * 处理流程：
     * 1. 获取对应的库存变更明细
     * 2. 构建追踪前缀到变更数量的映射
     * 3. 按先进先出分配数量
     *
     * @param receiveBill                收货单信息
     * @param detail                     收货明细
     * @param groupedDetails             分组后的库存变更明细
     * @param selectMap                  库存选择项Map
     * @param withHead2StoreBatchMapping 是否启用总部批次到门店批次映射
     */
    protected void allocateStockInItemsBySourceFIFO(ReceiveBillDTO receiveBill,
            ReceiveBillDetailDTO detail,
            Map<String, List<InventoryChangeDetailDTO>> groupedDetails,
            Map<InventorySelectEnum, List<InventorySelectItemDTO>> selectMap,
            boolean withHead2StoreBatchMapping) {

        String productPref = detail.getProductPref();
        String lotNo = detail.getLotNo();
        String detailKey = buildDetailKey(productPref, lotNo);

        // 1. 获取对应的库存变更明细
        List<InventoryChangeDetailDTO> detailChanges = groupedDetails.get(detailKey);
        if (CollectionUtils.isEmpty(detailChanges)) {
            log.warn("未找到对应的库存变更明细 - 商品: {}, 批号: {}", productPref, lotNo);
            return;
        }

        // 2. 构建追踪前缀到变更数量的映射
        Map<String, BigDecimal> traceChangeStock = new HashMap<>();
        for (InventoryChangeDetailDTO change : detailChanges) {
            String tracePref = change.getTracePref();
            if (StringUtils.isNotBlank(tracePref)) {
                traceChangeStock.put(tracePref, change.getInNumber());
            }
        }

        if (traceChangeStock.isEmpty()) {
            log.warn("未找到有效的追踪前缀 - 商品: {}, 批号: {}", productPref, lotNo);
            return;
        }

        // 3. 获取需要分配的数量
        BigDecimal[] quantities = getQualifiedAndUnqualifiedQuantities(detail);

        // 4. 按照批次时间排序实现先进先出
        List<String> sortedTracePrefList = new ArrayList<>(traceChangeStock.keySet());

        // 5. 分配数量到各个批次
        for (String rootTracePref : sortedTracePrefList) {
            if (allocateItem(receiveBill, selectMap, detail, traceChangeStock,
                    withHead2StoreBatchMapping, rootTracePref, quantities)) {
                break;
            }
        }

        // 6. 记录处理完成的日志
        log.debug("完成收货明细处理 - 商品: {}, 批号: {}", productPref, lotNo);
    }

    // ========== 私有方法 ==========

    /**
     * 执行库存变更明细查询
     *
     * @param dataSource 数据源
     * @param billNo     单据号
     * @param tenantId   租户ID
     * @return 库存变更明细列表
     */
    private List<InventoryChangeDetailDTO> executeInventoryChangeDetailQuery(DataSource dataSource, String billNo,
            Long tenantId) {
        long startTime = System.currentTimeMillis();

        List<InventoryChangeDetailDTO> result = switch (dataSource) {
            case LOCAL -> inventoryChangeDetailApi.getChangeDetail(
                    InventoryChangeDetailQueryDTO.builder()
                            .billNo(billNo)
                            .tenantId(tenantId)
                            .build());
            case CLOUD -> {
                // TODO 调用云端接口替换此实现
                log.debug("待实现云端查询逻辑 billNo:{}", billNo);
                yield Collections.emptyList();
            }
        };
        log.debug("库存变更查询完成 [数据源:{}] 单据:{} 租户:{} 数量:{} 耗时:{}ms",
                dataSource.name(), billNo, tenantId, result.size(),
                System.currentTimeMillis() - startTime);
        return result;
    }

    /**
     * 获取合格和不合格数量
     *
     * @param detail 收货明细
     * @return 包含合格数量和不合格数量的数组
     */
    private BigDecimal[] getQualifiedAndUnqualifiedQuantities(ReceiveBillDetailDTO detail) {
        return new BigDecimal[] {
                Optional.ofNullable(detail.getQualifiedQuantity()).orElse(BigDecimal.ZERO),
                Optional.ofNullable(detail.getUnqualifiedQuantity()).orElse(BigDecimal.ZERO)
        };
    }

    /**
     * 处理单个批次的数量分配
     *
     * @param receiveBill                收货单
     * @param selectMap                  库存选择项Map
     * @param detail                     收货明细
     * @param traceChangeStock           追踪前缀到变更数量的映射
     * @param withHead2StoreBatchMapping 是否启用总部批次到门店批次映射
     * @param rootTracePref              根追踪前缀
     * @param quantities                 数量数组[合格数量, 不合格数量]
     * @return 是否已分配完毕
     */
    private boolean allocateItem(ReceiveBillDTO receiveBill,
            Map<InventorySelectEnum, List<InventorySelectItemDTO>> selectMap,
            ReceiveBillDetailDTO detail,
            Map<String, BigDecimal> traceChangeStock,
            boolean withHead2StoreBatchMapping,
            String rootTracePref,
            BigDecimal[] quantities) {

        BigDecimal changeStock = traceChangeStock.get(rootTracePref);

        // 1. 构建货位库存变更映射
        Map<String, BigDecimal> positionChangeStock = allocateQualifiedAndUnqualifiedQuantity(
                detail, changeStock);

        if (positionChangeStock.isEmpty()) {
            return false;
        }

        // 2. 获取对应的追溯码
        List<TraceCodeChangeDTO> traceCodes = filterTraceCodesByTracePref(
                getDetailTraceCodes(detail), rootTracePref);

        // 3. 处理批次映射和选择策略
        BatchMappingResult mappingResult = processBatchMapping(
                rootTracePref,
                withHead2StoreBatchMapping,
                receiveBill.getTenantId());

        // 4. 添加库存选择项
        inventorySelectItemConvert.addInventorySelectItem(
                mappingResult.selectStrategy(),
                selectMap,
                detail,
                receiveBill.getSupplierGuid(),
                positionChangeStock,
                mappingResult.mappedTracePref(),
                rootTracePref,
                traceCodes);

        // 5. 更新剩余数量并判断是否已分配完毕
        return updateRemainingQuantities(positionChangeStock, quantities);
    }

    /**
     * 处理批次映射和选择策略
     *
     * @param rootTracePref              根追踪前缀
     * @param withHead2StoreBatchMapping 是否启用总部批次到门店批次映射
     * @param tenantId                   租户ID
     * @return 批次映射结果
     */
    private BatchMappingResult processBatchMapping(String rootTracePref,
            boolean withHead2StoreBatchMapping,
            Long tenantId) {
        if (withHead2StoreBatchMapping) {
            String mappedTracePref = matchStoreTracePref(rootTracePref, tenantId);
            return new BatchMappingResult(
                    mappedTracePref,
                    StringUtils.isNotBlank(mappedTracePref) ? InventorySelectEnum.DIRECT : null);
        } else {
            return new BatchMappingResult(rootTracePref, InventorySelectEnum.DIRECT);
        }
    }

    /**
     * 匹配门店库存追踪前缀（总部批次到门店批次的映射，实际业务需实现）
     *
     * @param rootTracePref 总部库存追踪前缀
     * @return 门店库存追踪前缀
     */
    private String matchStoreTracePref(String rootTracePref, Long tenantId) {
        // TODO: 根据总部批次匹配门店批次的具体实现
        log.debug("批次匹配逻辑待实现 - 总部批次: {}", rootTracePref);
        return "todo";
    }

    /**
     * 清理缓存
     */
    private void clearCache() {
        changeDetailCache.clear();
    }

    // ========== 内部类 ==========

    /**
     * 批次映射结果
     *
     * @param mappedTracePref 映射后的追踪前缀
     * @param selectStrategy  选择策略
     */
    private record BatchMappingResult(
            String mappedTracePref,
            InventorySelectEnum selectStrategy) {
    }

    /**
     * 数据源类型枚举
     */
    protected enum DataSource {
        /** 本地数据库查询 */
        LOCAL,
        /** 云端接口查询 */
        CLOUD
    }
}