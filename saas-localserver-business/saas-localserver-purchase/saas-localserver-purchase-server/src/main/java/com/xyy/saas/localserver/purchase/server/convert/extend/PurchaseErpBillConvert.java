package com.xyy.saas.localserver.purchase.server.convert.extend;

import com.xyy.saas.localserver.purchase.api.extend.dto.PurchaseErpBillDTO;
import com.xyy.saas.localserver.purchase.api.extend.dto.PurchaseErpBillPageReqDTO;
import com.xyy.saas.localserver.purchase.server.admin.extend.vo.PurchaseErpBillPageReqVO;
import com.xyy.saas.localserver.purchase.server.admin.extend.vo.PurchaseErpBillRespVO;
import com.xyy.saas.localserver.purchase.server.admin.extend.vo.PurchaseErpBillSaveReqVO;
import com.xyy.saas.localserver.purchase.server.dal.dataobject.extend.PurchaseErpBillDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 采购-三方erp单据信息 Convert
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface PurchaseErpBillConvert {

    PurchaseErpBillConvert INSTANCE = Mappers.getMapper(PurchaseErpBillConvert.class);

    /**
     * DTO 转 DO
     *
     * @param erpBillDTO DTO
     * @return DO
     */
    PurchaseErpBillDO convert2DO(PurchaseErpBillDTO erpBillDTO);

    /**
     * VO 转 DTO
     *
     * @param pageReqVO VO
     * @return DO
     */
    PurchaseErpBillPageReqDTO convert2DTO(PurchaseErpBillPageReqVO pageReqVO);


    /**
     * DO 转 DTO
     *
     * @param erpBillDO DO
     * @return DTO
     */
    PurchaseErpBillDTO convert2DTO(PurchaseErpBillDO erpBillDO);


    /**
     * VO 转 DTO
     *
     * @param saveReqVO VO
     * @return DTO
     */
    PurchaseErpBillDTO convert2DTO(PurchaseErpBillSaveReqVO saveReqVO);

    /**
     * DO 转 VO
     *
     * @param erpBillDO DO
     * @return VO
     */
    PurchaseErpBillRespVO convert2VO(PurchaseErpBillDO erpBillDO);

    /**
     * DO 列表转 VO 列表
     *
     * @param list DO 列表
     * @return VO 列表
     */
    List<PurchaseErpBillRespVO> convert2VOList(List<PurchaseErpBillDTO> list);

    /**
     * DO 分页转 DTO 分页
     *
     * @param page DO 分页
     * @return VO 分页
     */
    PageResult<PurchaseErpBillDTO> convert2DTO(PageResult<PurchaseErpBillDO> page);

    /**
     * DTO 分页转 VO 分页
     *
     * @param page DO 分页
     * @return VO 分页
     */
    PageResult<PurchaseErpBillRespVO> convert2VO(PageResult<PurchaseErpBillDTO> page);
}
