package com.xyy.saas.localserver.purchase.server.service.receive.handler.stockin;

import com.xyy.saas.localserver.entity.enums.tenant.TenantTypeEnum;
import com.xyy.saas.localserver.inventory.api.change.dto.InventoryChangeDetailDTO;
import com.xyy.saas.localserver.inventory.api.stock.dto.InventorySelectItemDTO;
import com.xyy.saas.localserver.inventory.enums.InventorySelectEnum;
import com.xyy.saas.localserver.purchase.api.receive.dto.ReceiveBillDTO;
import com.xyy.saas.localserver.purchase.api.receive.dto.ReceiveBillDetailDTO;
import com.xyy.saas.localserver.purchase.enums.receive.ReceiveBillTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 总部出库库存入库处理器
 *
 * <p>
 * 该处理器专门处理基于总部出库的库存入库业务，包括：
 * </p>
 * <ul>
 * <li>调拨收货（ALLOCATION_RECEIVE）</li>
 * <li>请购收货（REQUISITION_RECEIVE）</li>
 * <li>配送收货（DISTRIBUTION_RECEIVE）</li>
 * </ul>
 *
 * <p>
 * 主要特点：
 * </p>
 * <ul>
 * <li>查询总部出库明细作为数据源</li>
 * <li>基于追溯码进行库存批次匹配</li>
 * <li>支持总部批次到门店批次的映射</li>
 * <li>使用DIRECT库存选择策略</li>
 * <li>仅支持门店租户类型</li>
 * </ul>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
public class HeadquartersOutboundStockInHandler extends AbstractStockInHandler {

        // ========== 常量定义 ==========

        /** 支持的收货单类型 */
        private static final Set<ReceiveBillTypeEnum> SUPPORTED_BILL_TYPES = Set.of(
                        ReceiveBillTypeEnum.ALLOCATION_RECEIVE, // 调拨收货
                        ReceiveBillTypeEnum.REQUISITION_RECEIVE, // 请购收货
                        ReceiveBillTypeEnum.DISTRIBUTION_RECEIVE // 配送收货
        );

        /** 支持的租户类型 */
        private static final Set<TenantTypeEnum> SUPPORTED_TENANT_TYPES = Set.of(
                        TenantTypeEnum.CHAIN_STORE);

        // ========== 公共方法 ==========

        /**
         * 获取支持的收货单类型
         * 支持调拨收货、请购收货和配送收货三种类型
         *
         * @return 支持的收货单类型集合
         */
        @Override
        public Set<ReceiveBillTypeEnum> getSupportedBillTypes() {
                return SUPPORTED_BILL_TYPES;
        }

        /**
         * 获取支持的租户类型
         * 仅支持门店租户类型
         *
         * @return 支持的租户类型集合
         */
        @Override
        public Set<TenantTypeEnum> getSupportedTenantTypes() {
                return SUPPORTED_TENANT_TYPES;
        }

        /**
         * 根据入库单处理库存入库数据
         * 处理流程：
         * 1. 查询总部出库的库存变更明细
         * 2. 按商品编码和批号分组
         * 3. 处理每个收货明细
         *
         * @param receiveBill 收货单信息
         * @param selectMap   库存选择项Map
         */
        @Override
        protected void processStockInItemsByReceiveBill(ReceiveBillDTO receiveBill,
                        Map<InventorySelectEnum, List<InventorySelectItemDTO>> selectMap) {
                if (receiveBill == null || CollectionUtils.isEmpty(receiveBill.getDetails())) {
                        log.warn("收货单或明细为空，跳过处理 - 单据号: {}", receiveBill != null ? receiveBill.getBillNo() : null);
                        return;
                }

                String billNo = receiveBill.getBillNo();
                log.info("开始处理总部出库库存入库 - 单据号: {}", billNo);

                // 1. 查询总部出库的库存变更明细
                List<InventoryChangeDetailDTO> headquartersOutboundDetails = queryInventoryChangeDetails(
                                DataSource.CLOUD,
                                receiveBill.getDeliveryBillNo(),
                                receiveBill.getHeadTenantId());

                if (CollectionUtils.isEmpty(headquartersOutboundDetails)) {
                        log.warn("未找到总部出库库存变更明细 - 单据号: {}, 租户ID: {}",
                                        receiveBill.getDeliveryBillNo(),
                                        receiveBill.getHeadTenantId());
                        return;
                }

                // 2. 按商品编码和批号分组
                Map<String, List<InventoryChangeDetailDTO>> groupedDetails = groupChangeDetailsByProductAndLot(
                                headquartersOutboundDetails);

                // 3. 根据源单据批次按先进先出原则分配入库批次
                for (ReceiveBillDetailDTO detail : receiveBill.getDetails()) {
                        allocateStockInItemsBySourceFIFO(receiveBill, detail, groupedDetails, selectMap, true);
                }

                log.info("总部出库库存入库处理完成 - 单据号: {}", billNo);
        }

}