package com.xyy.saas.localserver.purchase.server.service.purchase;

import cn.iocoder.yudao.framework.common.exception.ServiceException;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.localserver.purchase.api.purchase.dto.PurchaseBillDTO;
import com.xyy.saas.localserver.purchase.api.purchase.dto.PurchaseBillPageReqDTO;

/**
 * 采购订单 Service 接口
 * <p>
 * 主要功能：
 * 1. 采购订单基础操作（保存、删除、更新、查询）
 * 2. 采购订单状态管理
 * 3. 采购订单分页查询
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface PurchaseOrderService {

    // ========== 1. 基础操作 ==========

    /**
     * 保存采购订单
     *
     * @param saveDTO 待保存的采购订单信息
     * @throws ServiceException 如果采购订单信息为空
     */
    void savePurchaseOrder(PurchaseBillDTO saveDTO);

    /**
     * 撤销采购订单
     *
     * @param revokeDTO 待撤销的采购订单信息
     * @throws ServiceException 如果采购订单不存在
     */
    void revokeRequisition(PurchaseBillDTO revokeDTO);

    /**
     * 删除采购订单
     *
     * @param id 采购订单编号
     * @throws ServiceException 如果采购订单不存在
     */
    void deletePurchaseOrder(Long id);

    /**
     * 更新采购订单并刷新业务状态
     *
     * @param updateDTO 待更新的采购订单信息
     * @throws ServiceException 如果更新冲突
     */
    void updatePurchaseOrderWithStatusRefresh(PurchaseBillDTO updateDTO);

    // ========== 2. 查询操作 ==========

    /**
     * 获得采购订单
     *
     * @param id 采购订单编号
     * @return 采购订单信息
     * @throws ServiceException 如果采购订单不存在
     */
    PurchaseBillDTO getPurchaseOrder(Long id);

    /**
     * 获得采购订单和详情
     *
     * @param billNo   采购订单号
     * @param tenantId 租户ID
     * @return 采购订单信息（包含详情）
     * @throws ServiceException 如果采购订单不存在
     */
    PurchaseBillDTO getPurchaseOrderWithDetails(String billNo, Long tenantId);

    /**
     * 获得采购订单分页
     *
     * @param pageReqDTO 分页查询参数
     * @return 采购订单分页结果
     */
    PageResult<PurchaseBillDTO> getPurchaseOrderPage(PurchaseBillPageReqDTO pageReqDTO);
}