package com.xyy.saas.localserver.purchase.server.service.purchase;

import cn.iocoder.yudao.framework.common.exception.ServiceException;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.localserver.purchase.api.purchase.dto.PurchaseBillDTO;
import com.xyy.saas.localserver.purchase.api.purchase.dto.PurchaseBillPageReqDTO;

/**
 * 采购计划 Service 接口
 * <p>
 * 主要功能：
 * 1. 采购计划单基础操作（保存、删除、查询）
 * 2. 采购计划单状态管理
 * 3. 采购计划单分页查询
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface PurchasePlanService {

    // ========== 1. 基础操作 ==========

    /**
     * 保存采购计划单
     *
     * @param saveDTO 待保存的采购计划单信息
     * @throws ServiceException 如果采购计划单信息为空
     */
    void savePurchasePlan(PurchaseBillDTO saveDTO);

    /**
     * 删除采购计划单
     *
     * @param id 采购计划单编号
     * @throws ServiceException 如果采购计划单不存在
     */
    void deletePurchasePlan(Long id);

    // ========== 2. 查询操作 ==========

    /**
     * 获得采购计划单
     *
     * @param id 采购计划单编号
     * @return 采购计划单信息
     * @throws ServiceException 如果采购计划单不存在
     */
    PurchaseBillDTO getPurchasePlan(Long id);

    /**
     * 获得采购计划单和明细
     *
     * @param billNo   采购计划单号
     * @param tenantId 租户ID
     * @return 采购计划单信息（包含明细）
     * @throws ServiceException 如果采购计划单不存在
     */
    PurchaseBillDTO getPurchasePlanWithDetails(String billNo, Long tenantId);

    /**
     * 获得采购计划单分页
     *
     * @param pageReqDTO 分页查询参数
     * @return 采购计划单分页结果
     */
    PageResult<PurchaseBillDTO> getPurchasePlanPage(PurchaseBillPageReqDTO pageReqDTO);
}