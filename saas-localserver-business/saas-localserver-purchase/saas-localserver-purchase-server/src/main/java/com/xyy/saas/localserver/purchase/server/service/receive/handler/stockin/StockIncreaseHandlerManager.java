package com.xyy.saas.localserver.purchase.server.service.receive.handler.stockin;

import com.xyy.saas.localserver.entity.enums.tenant.TenantTypeEnum;
import com.xyy.saas.localserver.inventory.api.stock.dto.InventoryIncreaseDTO;
import com.xyy.saas.localserver.inventory.api.stock.dto.InventorySwapDTO;
import com.xyy.saas.localserver.purchase.api.purchase.dto.PurchaseBillDTO;
import com.xyy.saas.localserver.purchase.api.receive.dto.ReceiveBillDTO;
import com.xyy.saas.localserver.purchase.enums.receive.ReceiveBillTypeEnum;
import com.xyy.saas.localserver.purchase.server.convert.inventory.InventoryIncreaseConvert;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import java.util.EnumMap;
import java.util.List;
import java.util.Map;

/**
 * 库存入库处理器管理器
 * 
 * <p>
 * 负责管理和调度各种库存入库处理器，根据收货单类型选择合适的处理器进行处理。
 * </p>
 * 
 * <p>
 * 主要职责：
 * </p>
 * <ul>
 * <li>注册和管理所有库存入库处理器</li>
 * <li>根据收货单类型路由到对应的处理器</li>
 * <li>提供统一的处理入口</li>
 * <li>处理器的生命周期管理</li>
 * </ul>
 * 
 * <p>
 * 支持的收货单类型：
 * </p>
 * <ul>
 * <li>采购订单收货（PURCHASE_ORDER_RECEIVE）</li>
 * <li>退货收货（RETURN_RECEIVE）</li>
 * <li>拒收收货（REJECT_RECEIVE）</li>
 * <li>调拨收货（ALLOCATION_RECEIVE）</li>
 * <li>请购收货（REQUISITION_RECEIVE）</li>
 * <li>配送收货（DISTRIBUTION_RECEIVE）</li>
 * </ul>
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
public class StockIncreaseHandlerManager {

    /** 收货单类型与处理器的映射 */
    private final Map<ReceiveBillTypeEnum, Map<TenantTypeEnum, AbstractStockInHandler>> handlerMap = new EnumMap<>(
            ReceiveBillTypeEnum.class);

    @Resource
    private List<AbstractStockInHandler> handlers;

    /**
     * 初始化处理器映射，Spring容器启动后自动注册
     */
    @PostConstruct
    public void initHandlers() {
        log.info("初始化库存入库处理器映射");
        // 1. 注册所有处理器
        for (AbstractStockInHandler handler : handlers) {
            for (ReceiveBillTypeEnum type : handler.getSupportedBillTypes()) {
                Map<TenantTypeEnum, AbstractStockInHandler> tenantHandlerMap = handlerMap.computeIfAbsent(type,
                        k -> new EnumMap<>(TenantTypeEnum.class));

                for (TenantTypeEnum tenantType : handler.getSupportedTenantTypes()) {
                    if (tenantHandlerMap.containsKey(tenantType)) {
                        log.warn("收货单类型[{}]租户类型[{}]已有处理器[{}]，将被新处理器[{}]覆盖",
                                type,
                                tenantType,
                                tenantHandlerMap.get(tenantType).getClass().getSimpleName(),
                                handler.getClass().getSimpleName());
                    }
                    tenantHandlerMap.put(tenantType, handler);
                    log.debug("注册库存入库处理器: 类型={}, 租户类型={}, 处理器={}",
                            type,
                            tenantType,
                            handler.getClass().getSimpleName());
                }
            }
        }
        log.info("库存入库处理器映射初始化完成，共注册{}个类型", handlerMap.size());
        // 2. 打印处理器映射信息
        if (log.isDebugEnabled()) {
            handlerMap.forEach((type,
                    tenantMap) -> tenantMap.forEach((tenantType, handler) -> log.debug("收货单类型[{}]租户类型[{}] -> 处理器[{}]",
                            type,
                            tenantType,
                            handler.getClass().getSimpleName())));
        }
    }

    /**
     * 统一入库处理入口，根据收货单类型自动选择处理器
     * 
     * @param receiveBill 收货单
     * @return 处理后的库存增加DTO
     */
    public InventoryIncreaseDTO processStockInParams(ReceiveBillDTO receiveBill) {
        // 1. 获取对应的处理器
        AbstractStockInHandler handler = getHandler(receiveBill);

        // 2. 构建库存增加DTO
        final InventoryIncreaseDTO inventoryIncreaseDTO = InventoryIncreaseConvert.INSTANCE
                .buildInventoryIncreaseDTO(receiveBill);
        // 3. 执行处理
        handler.processStockInItems(receiveBill, inventoryIncreaseDTO.getSelectMap());
        log.debug("库存入库参数处理完成: 单据号={}, 类型={}, 租户类型={}",
                receiveBill.getBillNo(),
                ReceiveBillTypeEnum.fromCode(receiveBill.getBillType()),
                TenantTypeEnum.fromCode(receiveBill.getTenantType()));
        return inventoryIncreaseDTO;
    }

    /**
     * 统一一入一出处理入口，根据收货单类型自动选择处理器
     *
     * @param distributionBill 配送单
     * @param receiveBill      收货单
     * @return 处理后的库存增加DTO
     */
    public InventorySwapDTO processInventorySwapParams(PurchaseBillDTO distributionBill, ReceiveBillDTO receiveBill) {
        // 1. 获取对应的处理器
        AbstractStockInHandler handler = getHandler(receiveBill);

        // 2. 构建库存增加DTO
        final InventorySwapDTO inventorySwapDTO = InventoryIncreaseConvert.INSTANCE.buildInventorySwapDTO(distributionBill,
                receiveBill);
        // 3. 执行处理
        handler.processStockInItems(receiveBill, inventorySwapDTO.getSelectMap());
        log.debug("库存一入一出参数处理完成: 单据号={}, 类型={}, 租户类型={}",
                receiveBill.getBillNo(),
                ReceiveBillTypeEnum.fromCode(receiveBill.getBillType()),
                TenantTypeEnum.fromCode(receiveBill.getTenantType()));
        return inventorySwapDTO;
    }

    /**
     * 根据收货单获取对应的处理器
     *
     * @param receiveBill 收货单
     * @return 对应的处理器
     * @throws IllegalArgumentException 如果未找到对应的处理器
     */
    private AbstractStockInHandler getHandler(ReceiveBillDTO receiveBill) {
        // 1. 获取收货单类型和租户类型
        ReceiveBillTypeEnum billType = ReceiveBillTypeEnum.fromCode(receiveBill.getBillType());
        TenantTypeEnum tenantType = TenantTypeEnum.fromCode(receiveBill.getTenantType());

        // 2. 获取对应的处理器
        Map<TenantTypeEnum, AbstractStockInHandler> tenantHandlerMap = handlerMap.get(billType);
        if (tenantHandlerMap == null) {
            throw new IllegalArgumentException("未找到收货单类型[" + billType + "]对应的处理器");
        }

        AbstractStockInHandler handler = tenantHandlerMap.get(tenantType);
        if (handler == null) {
            throw new IllegalArgumentException("未找到收货单类型[" + billType + "]租户类型[" + tenantType + "]对应的处理器");
        }

        log.debug("处理库存入库参数: 单据号={}, 类型={}, 租户类型={}, 处理器={}",
                receiveBill.getBillNo(),
                billType,
                tenantType,
                handler.getClass().getSimpleName());

        return handler;
    }

}