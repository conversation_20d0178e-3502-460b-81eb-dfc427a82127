//package com.xyy.saas.localserver.purchase.server.service.receive;
//
//import com.xyy.saas.localserver.purchase.server.admin.receive.vo.ReceiveBillDetailPageReqVO;
//import com.xyy.saas.localserver.purchase.server.admin.receive.vo.ReceiveBillDetailSaveReqVO;
//import com.xyy.saas.localserver.purchase.server.dal.dataobject.receive.ReceiveBillDetailDO;
//import jakarta.validation.*;
//import cn.iocoder.yudao.framework.common.pojo.PageResult;
//
///**
// * 收货单明细 Service 接口
// *
// * <AUTHOR>
// */
//public interface ReceiveBillDetailService {
//
//    /**
//     * 创建收货单明细
//     *
//     * @param createReqVO 创建信息
//     * @return 编号
//     */
//    Long createPurchaseReceiveBillDetail(@Valid ReceiveBillDetailSaveReqVO createReqVO);
//
//    /**
//     * 更新收货单明细
//     *
//     * @param updateReqVO 更新信息
//     */
//    void updatePurchaseReceiveBillDetail(@Valid ReceiveBillDetailSaveReqVO updateReqVO);
//
//    /**
//     * 删除收货单明细
//     *
//     * @param id 编号
//     */
//    void deletePurchaseReceiveBillDetail(Long id);
//
//    /**
//     * 获得收货单明细
//     *
//     * @param id 编号
//     * @return 收货单明细
//     */
//    ReceiveBillDetailDO getPurchaseReceiveBillDetail(Long id);
//
//    /**
//     * 获得收货单明细分页
//     *
//     * @param pageReqVO 分页查询
//     * @return 收货单明细分页
//     */
//    PageResult<ReceiveBillDetailDO> getPurchaseReceiveBillDetailPage(ReceiveBillDetailPageReqVO pageReqVO);
//
//}