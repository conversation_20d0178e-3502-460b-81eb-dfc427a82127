//package com.xyy.saas.localserver.purchase.server.service.bpm.handler;
//
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.stereotype.Component;
//
//import static com.xyy.saas.inquiry.product.consts.ProductConstant.*;
//import static com.xyy.saas.inquiry.product.enums.ErrorCodeConstants.BPM_APPROVE_STATUS_INVALID;
//
///**
// */
//@Slf4j
//@Component
//public class PurchaseOrderApprovalHandler implements BpmApprovalHandler {
//
//
//    @Override
//    public void handleApproval(BpmBusinessRelationDto businessDto) {
//
//    }
//}