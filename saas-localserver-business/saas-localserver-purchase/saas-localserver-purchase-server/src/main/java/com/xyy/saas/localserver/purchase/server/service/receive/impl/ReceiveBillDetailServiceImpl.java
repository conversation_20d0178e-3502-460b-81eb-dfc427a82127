//package com.xyy.saas.localserver.purchase.server.service.receive.impl;
//
//import com.xyy.saas.localserver.purchase.server.admin.receive.vo.ReceiveBillDetailPageReqVO;
//import com.xyy.saas.localserver.purchase.server.admin.receive.vo.ReceiveBillDetailSaveReqVO;
//import com.xyy.saas.localserver.purchase.server.dal.dataobject.receive.ReceiveBillDetailDO;
//import com.xyy.saas.localserver.purchase.server.dal.mysql.receive.ReceiveBillDetailMapper;
//import com.xyy.saas.localserver.purchase.server.service.receive.ReceiveBillDetailService;
//import org.springframework.stereotype.Service;
//import jakarta.annotation.Resource;
//import org.springframework.validation.annotation.Validated;
//import cn.iocoder.yudao.framework.common.pojo.PageResult;
//import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
//import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
//import static com.xyy.saas.localserver.purchase.enums.ErrorCodeConstants.RECEIVE_BILL_DETAIL_NOT_EXISTS;
//
///**
// * 收货单明细 Service 实现类
// *
// * <AUTHOR>
// */
//@Service
//@Validated
//public class ReceiveBillDetailServiceImpl implements ReceiveBillDetailService {
//
//    @Resource
//    private ReceiveBillDetailMapper receiveBillDetailMapper;
//
//    @Override
//    public Long createPurchaseReceiveBillDetail(ReceiveBillDetailSaveReqVO createReqVO) {
//        // 插入
//        ReceiveBillDetailDO purchaseReceiveBillDetail = BeanUtils.toBean(createReqVO, ReceiveBillDetailDO.class);
//        receiveBillDetailMapper.insert(purchaseReceiveBillDetail);
//        // 返回
//        return purchaseReceiveBillDetail.getId();
//    }
//
//    @Override
//    public void updatePurchaseReceiveBillDetail(ReceiveBillDetailSaveReqVO updateReqVO) {
//        // 校验存在
//        validatePurchaseReceiveBillDetailExists(updateReqVO.getId());
//        // 更新
//        ReceiveBillDetailDO updateObj = BeanUtils.toBean(updateReqVO, ReceiveBillDetailDO.class);
//        receiveBillDetailMapper.updateById(updateObj);
//    }
//
//    @Override
//    public void deletePurchaseReceiveBillDetail(Long id) {
//        // 校验存在
//        validatePurchaseReceiveBillDetailExists(id);
//        // 删除
//        receiveBillDetailMapper.deleteById(id);
//    }
//
//    private void validatePurchaseReceiveBillDetailExists(Long id) {
//        if (receiveBillDetailMapper.selectById(id) == null) {
//            throw exception(RECEIVE_BILL_DETAIL_NOT_EXISTS);
//        }
//    }
//
//    @Override
//    public ReceiveBillDetailDO getPurchaseReceiveBillDetail(Long id) {
//        return receiveBillDetailMapper.selectById(id);
//    }
//
//    @Override
//    public PageResult<ReceiveBillDetailDO> getPurchaseReceiveBillDetailPage(ReceiveBillDetailPageReqVO pageReqVO) {
//        return receiveBillDetailMapper.selectPage(pageReqVO);
//    }
//
//}