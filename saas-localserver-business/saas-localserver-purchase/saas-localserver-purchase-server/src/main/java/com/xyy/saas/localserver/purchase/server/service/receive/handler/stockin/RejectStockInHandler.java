package com.xyy.saas.localserver.purchase.server.service.receive.handler.stockin;

import com.xyy.saas.localserver.entity.enums.tenant.TenantTypeEnum;
import com.xyy.saas.localserver.inventory.api.change.dto.InventoryChangeDetailDTO;
import com.xyy.saas.localserver.inventory.api.stock.dto.InventorySelectItemDTO;
import com.xyy.saas.localserver.inventory.api.tracecode.dto.TraceCodeChangeDTO;
import com.xyy.saas.localserver.inventory.enums.InventorySelectEnum;
import com.xyy.saas.localserver.purchase.api.extend.dto.ExtDTO;
import com.xyy.saas.localserver.purchase.api.receive.dto.ReceiveBillDTO;
import com.xyy.saas.localserver.purchase.api.receive.dto.ReceiveBillDetailDTO;
import com.xyy.saas.localserver.purchase.enums.receive.ReceiveBillTypeEnum;
import com.xyy.saas.localserver.purchase.server.dal.mysql.receive.ReceiveBillMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 拒收库存入库处理器
 * 
 * <p>
 * 该处理器专门处理拒收类型的库存入库业务。
 * 主要职责包括：
 * </p>
 * <ul>
 * <li>处理拒收单的库存入库逻辑</li>
 * <li>支持总部和门店两种租户类型</li>
 * <li>实现先进先出(FIFO)的库存分配策略</li>
 * <li>计算门店入库和配送出库的差值作为拒收数量</li>
 * </ul>
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
public class RejectStockInHandler extends AbstractStockInHandler {

        @Resource
        private ReceiveBillMapper receiveBillMapper;

        // ========== 公共方法 ==========

        /**
         * 获取支持的收货单类型
         * 仅支持拒收收货单类型
         *
         * @return 支持的收货单类型集合
         */
        @Override
        public Set<ReceiveBillTypeEnum> getSupportedBillTypes() {
                return Set.of(ReceiveBillTypeEnum.REJECT_RECEIVE);
        }

        /**
         * 获取支持的租户类型
         * 支持连锁店和门店两种租户类型
         *
         * @return 支持的租户类型集合
         */
        @Override
        public Set<TenantTypeEnum> getSupportedTenantTypes() {
                return Set.of(
                                TenantTypeEnum.CHAIN_STORE,
                                TenantTypeEnum.CHAIN_HEADQUARTERS);
        }

        /**
         * 根据入库单处理库存入库数据
         * 处理流程：
         * 1. 获取源单信息（门店收货单）
         * 2. 查询源收货单详情
         * 3. 获取配送单信息
         * 4. 查询库存变更明细（源单和配送单）
         * 5. 计算拒收差值
         * 6. 按先进先出原则分配拒收数量
         *
         * @param receiveBill 收货单信息
         * @param selectMap   库存选择项Map
         */
        @Override
        protected void processStockInItemsByReceiveBill(ReceiveBillDTO receiveBill,
                        Map<InventorySelectEnum, List<InventorySelectItemDTO>> selectMap) {
                String billNo = receiveBill.getBillNo();
                log.debug("开始处理拒收收货[{}]的库存入库", billNo);

                // 1. 获取源单信息
                String sourceBillNo = receiveBill.getSourceBillNo();
                Long outboundTenantId = receiveBill.getOutboundTenantId();

                // 2. 查询源收货单
                ReceiveBillDTO sourceReceiveBill = receiveBillMapper.getReceiveBillWithDetails(sourceBillNo,
                                outboundTenantId);
                if (sourceReceiveBill == null) {
                        log.warn("未找到源收货单 - 单据号: {}, 租户ID: {}", sourceBillNo, outboundTenantId);
                        return;
                }

                // 3. 获取配送单信息
                Long deliveryTenantId = sourceReceiveBill.getOutboundTenantId();
                String deliveryBillNo = sourceReceiveBill.getSourceBillNo();

                // 4. 查询库存变更明细
                // 4.1 查询源单明细（根据租户类型选择数据源）
                boolean isChainStore = TenantTypeEnum.CHAIN_STORE
                                .equals(TenantTypeEnum.fromCode(receiveBill.getTenantType()));
                List<InventoryChangeDetailDTO> sourceDetails = queryInventoryChangeDetails(
                                isChainStore ? DataSource.CLOUD : DataSource.LOCAL,
                                sourceBillNo,
                                outboundTenantId);

                // 4.2 查询配送单明细
                List<InventoryChangeDetailDTO> deliveryDetails = queryInventoryChangeDetails(
                                DataSource.LOCAL,
                                deliveryBillNo,
                                deliveryTenantId);
                if (CollectionUtils.isEmpty(deliveryDetails)) {
                        log.warn("未查询到配送出库库存变更明细 - 单据号: {}, 租户ID: {}", deliveryBillNo, deliveryTenantId);
                        return;
                }

                // 5. 计算拒收差值
                Map<String, List<InventoryChangeDetailDTO>> rejectDetailMap = calculateInventoryDifference(
                                sourceDetails,
                                deliveryDetails);
                if (rejectDetailMap.isEmpty()) {
                        log.debug("无拒收差值，跳过处理");
                        return;
                }

                // 6. 处理收货明细
                List<ReceiveBillDetailDTO> details = receiveBill.getDetails();
                if (CollectionUtils.isNotEmpty(details)) {
                        details.parallelStream()
                                        .forEach(detail -> allocateRejectedItemsToStockInBatchesByFIFO(
                                                        receiveBill,
                                                        selectMap,
                                                        rejectDetailMap,
                                                        detail));
                }

                log.debug("完成拒收收货[{}]的库存入库处理，处理明细数: {}",
                                billNo,
                                details != null ? details.size() : 0);
        }

        // ========== 私有方法 ==========

        /**
         * 根据源单据批次按先进先出原则分配拒收数量
         * 处理流程：
         * 1. 获取对应的拒收明细
         * 2. 追溯码匹配
         * 3. 判断入库数量
         * 4. 处理多批次场景
         *
         * @param receiveBill     收货单信息
         * @param selectMap       库存选择项Map
         * @param rejectDetailMap 拒收明细分组
         * @param detail          收货明细
         */
        private void allocateRejectedItemsToStockInBatchesByFIFO(ReceiveBillDTO receiveBill,
                        Map<InventorySelectEnum, List<InventorySelectItemDTO>> selectMap,
                        Map<String, List<InventoryChangeDetailDTO>> rejectDetailMap,
                        ReceiveBillDetailDTO detail) {
                log.debug("处理拒收收货明细 - 商品: {}, 批号: {}", detail.getProductPref(), detail.getLotNo());

                // 1. 获取对应的拒收明细
                String detailKey = buildDetailKey(detail.getProductPref(), detail.getLotNo());
                List<InventoryChangeDetailDTO> changeDetails = rejectDetailMap.get(detailKey);
                if (CollectionUtils.isEmpty(changeDetails)) {
                        log.warn("未找到对应的拒收库存变更明细 - 商品: {}, 批号: {}", detail.getProductPref(), detail.getLotNo());
                        return;
                }

                // 2. 追溯码匹配，总部收货追溯码数量和门店拒收追溯码数量一致
                List<TraceCodeChangeDTO> traceCodes = changeDetails.stream()
                                .flatMap(x -> x.getTraceCodes().stream())
                                .collect(Collectors.toList());
                if (detail.getExt() != null) {
                        detail.getExt().setTraceCodes(traceCodes);
                }

                // 3. 判断入库数是否小于0
                if (detail.getWarehouseQuantity().compareTo(BigDecimal.ZERO) <= 0) {
                        log.debug("收货明细数量为零，跳过处理 - 商品: {}, 批号: {}", detail.getProductPref(), detail.getLotNo());
                        return;
                }

                // 4. 处理多批次场景
                for (InventoryChangeDetailDTO changeDetail : changeDetails) {
                        // 4.1 构建货位库存变更映射
                        Map<String, BigDecimal> positionChangeStock = inventorySelectItemConvert
                                        .buildRejectPositionChangeStock(
                                                        detail,
                                                        changeDetail);

                        if (positionChangeStock.isEmpty()) {
                                continue;
                        }

                        // 4.2 添加库存选择项
                        inventorySelectItemConvert.addInventorySelectItem(
                                        InventorySelectEnum.DIRECT,
                                        selectMap,
                                        detail,
                                        receiveBill.getSupplierGuid(),
                                        positionChangeStock,
                                        changeDetail.getTracePref(),
                                        null,
                                        Optional.ofNullable(detail.getExt())
                                                        .map(ExtDTO::getTraceCodes)
                                                        .orElse(Collections.emptyList()));
                }
                log.debug("完成拒收收货明细处理 - 商品: {}, 批号: {}", detail.getProductPref(), detail.getLotNo());
        }

        /**
         * 计算库存差值（拒收明细）
         * 计算门店入库和配送出库的差值，这个差值就是拒收的数量
         *
         * @param sourceDetails   门店入库明细
         * @param deliveryDetails 配送出库明细
         * @return 拒收明细映射，key为"商品编码:批号"
         */
        private Map<String, List<InventoryChangeDetailDTO>> calculateInventoryDifference(
                        List<InventoryChangeDetailDTO> sourceDetails,
                        List<InventoryChangeDetailDTO> deliveryDetails) {

                long startTime = System.currentTimeMillis();

                // 1. 处理RootTracePref为空的情况
                Stream.concat(sourceDetails.stream(), deliveryDetails.stream())
                                .parallel()
                                .filter(detail -> detail.getRootTracePref() == null
                                                || detail.getRootTracePref().isEmpty())
                                .forEach(detail -> detail.setRootTracePref(detail.getTracePref()));

                // 2. 合并sourceDetails中同追踪维度的数据
                List<InventoryChangeDetailDTO> mergedSourceDetails = inventorySelectItemConvert
                                .mergeDetailsByTracePref(sourceDetails);

                // 3. 合并deliveryDetails中同追踪维度的数据
                List<InventoryChangeDetailDTO> mergedDeliveryDetails = inventorySelectItemConvert
                                .mergeDetailsByTracePref(deliveryDetails);

                // 4. 计算差值并按商品批号分组
                Map<String, List<InventoryChangeDetailDTO>> result = Stream
                                .concat(mergedSourceDetails.stream(), mergedDeliveryDetails.stream())
                                .parallel()
                                .collect(Collectors.groupingBy(
                                                dto -> buildDetailKey(dto.getProductPref(), dto.getLotNo()),
                                                ConcurrentHashMap::new,
                                                Collectors.collectingAndThen(
                                                                Collectors.toMap(
                                                                                InventoryChangeDetailDTO::getRootTracePref,
                                                                                Function.identity(),
                                                                                inventorySelectItemConvert::mergeDetails,
                                                                                ConcurrentHashMap::new),
                                                                map -> map.values().stream()
                                                                                .filter(x -> x.getInNumber().compareTo(
                                                                                                BigDecimal.ZERO) != 0
                                                                                                || !x.getTraceCodes()
                                                                                                                .isEmpty())
                                                                                .collect(Collectors.toList()))));

                long costTime = System.currentTimeMillis() - startTime;
                log.debug("计算库存差值完成 - 源明细数: {}, 配送明细数: {}, 结果数: {}, 耗时: {}ms",
                                sourceDetails.size(), deliveryDetails.size(), result.size(), costTime);

                return result;
        }
}