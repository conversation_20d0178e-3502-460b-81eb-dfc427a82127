package com.xyy.saas.localserver.purchase.server.convert.extend;

import com.xyy.saas.localserver.purchase.api.extend.dto.PurchaseInvoiceDTO;
import com.xyy.saas.localserver.purchase.api.extend.dto.PurchaseInvoicePageReqDTO;
import com.xyy.saas.localserver.purchase.server.admin.extend.vo.PurchaseInvoicePageReqVO;
import com.xyy.saas.localserver.purchase.server.admin.extend.vo.PurchaseInvoiceRespVO;
import com.xyy.saas.localserver.purchase.server.admin.extend.vo.PurchaseInvoiceSaveReqVO;
import com.xyy.saas.localserver.purchase.server.dal.dataobject.extend.PurchaseInvoiceDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 采购-发票信息 Convert
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface PurchaseInvoiceConvert {

    PurchaseInvoiceConvert INSTANCE = Mappers.getMapper(PurchaseInvoiceConvert.class);

    /**
     * DTO 转 DO
     *
     * @param invoiceDTO DTO
     * @return DO
     */
    PurchaseInvoiceDO convert2DO(PurchaseInvoiceDTO invoiceDTO);

    /**
     * DTO列表 转 DO列表
     *
     * @param invoiceDTOs DTO列表
     * @return DO列表
     */
    List<PurchaseInvoiceDO> convert2DOList(List<PurchaseInvoiceDTO> invoiceDTOs);


    /**
     * VO 转 DTO
     *
     * @param pageReqVO VO
     * @return DTO
     */
    PurchaseInvoicePageReqDTO convert2DTO(PurchaseInvoicePageReqVO pageReqVO);

    /**
     * DO 转 DTO
     *
     * @param invoiceDO DO
     * @return DTO
     */
    PurchaseInvoiceDTO convert2DTO(PurchaseInvoiceDO invoiceDO);

    /**
     * VO 转 DTO
     *
     * @param saveReqVO VO
     * @return DTO
     */
    PurchaseInvoiceDTO convert2DTO(PurchaseInvoiceSaveReqVO saveReqVO);

    /**
     * DTO 转 VO
     *
     * @param invoiceDTO DTO
     * @return VO
     */
    PurchaseInvoiceRespVO convert2VO(PurchaseInvoiceDTO invoiceDTO);

    /**
     * DO 列表转 VO 列表
     *
     * @param list DO 列表
     * @return VO 列表
     */
    List<PurchaseInvoiceRespVO> convert2VOList(List<PurchaseInvoiceDTO> list);

    /**
     * DO 分页转 DTO 分页
     *
     * @param page DO 分页
     * @return DTO 分页
     */
    PageResult<PurchaseInvoiceDTO> convert2DTO(PageResult<PurchaseInvoiceDO> page);

    /**
     * DTO 分页转 VO 分页
     *
     * @param page DTO 分页
     * @return VO 分页
     */
    PageResult<PurchaseInvoiceRespVO> convert2VO(PageResult<PurchaseInvoiceDTO> page);
}
