package com.xyy.saas.localserver.purchase.server.convert.returned;

import com.xyy.saas.localserver.purchase.api.extend.dto.ExtDTO;
import com.xyy.saas.localserver.purchase.api.returned.dto.ReturnBillDetailDTO;
import com.xyy.saas.localserver.purchase.server.admin.receive.vo.ReceiveBillDetailSaveReqVO;
import com.xyy.saas.localserver.purchase.server.admin.returned.vo.ReturnBillDetailSaveReqVO;
import com.xyy.saas.localserver.purchase.server.convert.extend.ExtConvert;
import com.xyy.saas.localserver.purchase.server.dal.dataobject.returned.ReturnBillDetailDO;
import com.xyy.saas.localserver.purchase.server.utils.ProductInfoUtil;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.Optional;

/**
 * 退货单明细转换器
 * <p>
 * 主要功能：
 * 1. 基础转换 - VO/DO/DTO之间的转换
 * 2. 单据转换 - 处理不同类型单据的转换
 * 3. 商品信息填充 - 处理商品相关信息的填充
 */
@Mapper(uses = { ExtConvert.class })
public interface ReturnBillDetailConvert {

        ReturnBillDetailConvert INSTANCE = Mappers.getMapper(ReturnBillDetailConvert.class);

        // ========== 1. 基础转换方法 ==========

        /**
         * VO 转 DO
         * 
         * @param returnBillDetail 退货单明细保存请求VO
         * @return 退货单明细DO
         */
        @Mapping(target = "id", ignore = true)
        @Mapping(target = "ext", source = "ext")
        ReturnBillDetailDO convert2DetailDO(ReturnBillDetailSaveReqVO returnBillDetail);

        /**
         * DTO 转 DO
         *
         * @param returnBillDetail 退货单明细DTO
         * @return 退货单明细DO
         */
        @Mapping(target = "ext", source = "ext")
        ReturnBillDetailDO convert2DetailDO(ReturnBillDetailDTO returnBillDetail);

        /**
         * DTO列表 转 DO列表
         *
         * @param returnBillDetails 退货单明细DTO列表
         * @return 退货单明细DO列表
         */
        @Mapping(target = "ext", source = "ext")
        List<ReturnBillDetailDO> convert2DetailDOList(List<ReturnBillDetailDTO> returnBillDetails);

        /**
         * DO 转 VO
         * 
         * @param returnBillDetail 退货单明细DO
         * @return 退货单明细保存请求VO
         */
        @Mapping(target = "id", ignore = true)
        @Mapping(target = "ext", source = "ext")
        ReturnBillDetailSaveReqVO convert2DetailSaveVO(ReturnBillDetailDO returnBillDetail);

        // ========== 2. 单据转换方法 ==========

        /**
         * 将退货单详情转换为收货单详情
         * 
         * @param returnDetail 退货单详情
         * @return 收货单详情
         */
        @Mapping(target = "id", ignore = true)
        @Mapping(target = "billNo", ignore = true)
        @Mapping(target = "ext", source = "ext")
        @Mapping(target = "arriveQuantity", source = "outboundQuantity")
        @Mapping(target = "receiveAmount", source = "outboundAmount")
        @Mapping(target = "receiveQuantity", source = "outboundQuantity")
        @Mapping(target = "taxRate", source = "inTaxRate")
        @Mapping(target = "discountedPrice", source = "price")
        ReceiveBillDetailSaveReqVO convert2ReceiveDetail(ReturnBillDetailDO returnDetail);

        // ========== 3. 商品信息填充方法 ==========

        /**
         * 填充商品信息
         * 
         * @param details 详情列表
         */
        default void fillProductInfo(List<ReturnBillDetailDTO> details) {
                ProductInfoUtil.fillProductInfo(
                                details,
                                ReturnBillDetailDTO::getProductPref,
                                (detail, productInfo) -> Optional.ofNullable(detail.getExt())
                                                .or(() -> Optional.of(new ExtDTO()).map(e -> {
                                                        detail.setExt(e);
                                                        return e;
                                                }))
                                                .ifPresent(ext -> ext.setProductInfo(productInfo)));
        }
}