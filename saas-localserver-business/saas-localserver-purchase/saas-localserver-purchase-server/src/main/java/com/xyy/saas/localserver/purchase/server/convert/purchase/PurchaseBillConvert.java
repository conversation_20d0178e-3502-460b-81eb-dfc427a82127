package com.xyy.saas.localserver.purchase.server.convert.purchase;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.web.core.util.WebFrameworkUtils;
import com.xyy.saas.datasync.client.constants.DataContextHolder;
import com.xyy.saas.localserver.entity.enums.tenant.TenantTypeEnum;
import com.xyy.saas.localserver.purchase.api.purchase.dto.PurchaseBillDTO;
import com.xyy.saas.localserver.purchase.api.purchase.dto.PurchaseBillDetailDTO;
import com.xyy.saas.localserver.purchase.api.purchase.dto.PurchaseBillPageReqDTO;
import com.xyy.saas.localserver.purchase.enums.purchase.PurchaseBillTypeEnum;
import com.xyy.saas.localserver.purchase.enums.purchase.PurchaseBillStatusEnum;
import com.xyy.saas.localserver.purchase.server.admin.purchase.vo.PurchaseBillPageReqVO;
import com.xyy.saas.localserver.purchase.server.admin.purchase.vo.PurchaseBillRespVO;
import com.xyy.saas.localserver.purchase.server.admin.purchase.vo.PurchaseBillSaveReqVO;
import com.xyy.saas.localserver.purchase.server.convert.extend.PurchaseTransportConvert;
import com.xyy.saas.localserver.purchase.server.dal.dataobject.purchase.PurchaseBillDO;
import com.xyy.saas.localserver.purchase.server.enums.BillNoTypeEnum;
import com.xyy.saas.localserver.purchase.server.utils.BillContentUtil;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;
import org.springframework.beans.BeanUtils;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.function.BiConsumer;
import java.util.function.Function;
import static com.xyy.saas.localserver.purchase.enums.purchase.PurchaseBillStatusEnum.*;
import static com.xyy.saas.localserver.purchase.enums.purchase.PurchaseBillTypeEnum.*;

/**
 * 采购单转换器
 * <p>
 * 主要功能：
 * 1. 基础转换 - VO/DO/DTO之间的转换
 * 2. 单据类型转换 - 处理不同类型的采购单据
 * 3. 内容填充 - 处理单据内容的生成和填充
 * 4. 单据拆分 - 处理采购单的拆分逻辑
 * 5. 辅助方法 - 提供各种辅助功能
 */
@Mapper(uses = { PurchaseTransportConvert.class, PurchaseBillDetailConvert.class })
public interface PurchaseBillConvert {

    PurchaseBillConvert INSTANCE = Mappers.getMapper(PurchaseBillConvert.class);

    // ========== 1. 基础转换方法 ==========

    /**
     * DTO 转 DO
     *
     * @param purchaseBillDTO 采购单DTO
     * @return 采购单DO
     */
    PurchaseBillDO convert2DO(PurchaseBillDTO purchaseBillDTO);

    /**
     * DTO列表 转 DO列表
     *
     * @param purchaseBillDTOs 采购单DTO列表
     * @return 采购单DO列表
     */
    List<PurchaseBillDO> convert2DOList(List<PurchaseBillDTO> purchaseBillDTOs);

    /**
     * DO 转 DTO
     *
     * @param purchaseBill 采购单DO
     * @return 采购单DTO
     */
    PurchaseBillDTO convert2DTO(PurchaseBillDO purchaseBill);

    /**
     * DO 转 DTO
     *
     * @param pageResult 采购单DO分页结果
     * @return 采购单DTO分页结果
     */
    PageResult<PurchaseBillDTO> convert2DTO(PageResult<PurchaseBillDO> pageResult);

    /**
     * VO 转 DTO
     * 
     * @param saveReqVO 采购单保存请求VO
     * @return 采购单DTO
     */
    @Mapping(target = "details", source = "details")
    PurchaseBillDTO convert2DTO(PurchaseBillSaveReqVO saveReqVO);

    /**
     * VO 转 DTO
     *
     * @param pageReqVO 采购单分页查询VO
     * @return 采购单分页查询DTO
     */
    PurchaseBillPageReqDTO convert2DTO(PurchaseBillPageReqVO pageReqVO);

    /**
     * DO 转 VO
     * 
     * @param purchaseBill 采购单DO
     * @return 采购单保存请求VO
     */
//    @Mapping(target = "details", source = "details")
    PurchaseBillSaveReqVO convert2VO(PurchaseBillDO purchaseBill);

    /**
     * DTO 转 VO
     *
     * @param purchaseBillDTO 采购单DTO
     * @return 采购单响应VO
     */
//    @Mapping(target = "details", source = "details")
    PurchaseBillRespVO convert2VO(PurchaseBillDTO purchaseBillDTO);

    /**
     * DTO 转 VO
     *
     * @param pageResult 采购单DTO分页结果
     * @return 采购单响应VO分页结果
     */
    PageResult<PurchaseBillRespVO> convert2VO(PageResult<PurchaseBillDTO> pageResult);

    /**
     * DTO列表 转 VO列表
     *
     * @param list 采购单DTO列表
     * @return 采购单响应VO列表
     */
    List<PurchaseBillRespVO> convert2VOList(List<PurchaseBillDTO> list);

    // ========== 2. 单据类型转换方法 ==========

    /**
     * VO 转 DTO(采购计划)
     * 
     * @param saveReqVO 采购计划单VO
     * @return 采购计划单DTO
     */
    default PurchaseBillDTO convert2PlanDTO(PurchaseBillSaveReqVO saveReqVO) {
        PurchaseBillDTO plan = convert2DTO(saveReqVO);
        plan.setBillType(PURCHASE_ORDER.getCode());
        plan.setStatus(PLAN_CREATED.getCode());
        // 设置租户信息和操作员
        fillTenantAndOperator(plan);
        return plan;
    }

    /**
     * VO 转 DTO(采购订单)
     * 
     * @param saveReqVO 采购订单VO
     * @return 采购订单DTO
     */
    default PurchaseBillDTO convert2OrderDTO(PurchaseBillSaveReqVO saveReqVO) {
        PurchaseBillDTO order = convert2DTO(saveReqVO);
        order.setBillType(PURCHASE_ORDER.getCode());
        // 设置租户信息和操作员
        fillTenantAndOperator(order);
        return order;
    }

    /**
     * VO 转 DTO(门店要货)
     * 
     * @param saveReqVO 要货单VO
     * @return 要货单DTO
     */
    default PurchaseBillDTO convert2RequisitionDTO(PurchaseBillSaveReqVO saveReqVO) {
        PurchaseBillDTO requisition = convert2DTO(saveReqVO);
        requisition.setBillType(STORE_REQUISITION.getCode());
        if (requisition.getPurchaseTime() == null) {
            requisition.setPurchaseTime(LocalDateTime.now());
        }
        // 设置租户信息和操作员
        fillTenantAndOperator(requisition);
        // 设置总部租户id
        requisition.setHeadTenantId(Long.valueOf(DataContextHolder.getTenantId()));
        return requisition;
    }

    /**
     * VO 转 DTO(门店调剂)
     * 
     * @param saveReqVO 调剂单VO
     * @return 调剂单DTO
     */
    default PurchaseBillDTO convert2AllocationDTO(PurchaseBillSaveReqVO saveReqVO) {
        PurchaseBillDTO allocation = convert2DTO(saveReqVO);
        allocation.setBillType(STORE_ALLOCATION.getCode());
        // 设置租户信息和操作员
        fillTenantAndOperator(allocation);
        // 设置总部租户id
        allocation.setHeadTenantId(Long.valueOf(DataContextHolder.getTenantId()));
        return allocation;
    }

    /**
     * VO 转 DTO(总部主配)
     * 
     * @param saveReqVO 铺货单VO
     * @return 铺货单DTO
     */
    default PurchaseBillDTO convert2DistributionDTO(PurchaseBillSaveReqVO saveReqVO) {
        PurchaseBillDTO distribution = convert2DTO(saveReqVO);
        distribution.setBillType(HEADQUARTERS_DISTRIBUTION.getCode());
        // 设置租户信息和操作员
        fillTenantAndOperator(distribution);
        return distribution;
    }

    /**
     * VO 转 DTO(撤销单据)
     * 
     * @param revokeReqVO 撤销请求VO
     * @return 采购单DTO
     */
    default PurchaseBillDTO convert2RevokeDTO(PurchaseBillSaveReqVO revokeReqVO) {
        PurchaseBillDTO purchaseBill = convert2DTO(revokeReqVO);
        // 设置撤销状态
        purchaseBill.setStatus(PurchaseBillStatusEnum.REVOKED.getCode());
        return purchaseBill;
    }

    // ========== 3. 内容填充方法 ==========

    /**
     * 填充采购内容
     * 包括：单号、金额计算、采购内容生成
     * 
     * @param purchaseBill 采购单
     */
    default void fillPurchaseContent(PurchaseBillDTO purchaseBill) {
        // 填充单号
        fillBillNo(purchaseBill);
        // 计算金额
        calculateAmount(purchaseBill);
        // 填充采购内容
        generatePurchaseContent(purchaseBill);
    }

    /**
     * 填充租户和操作员信息
     * 
     * @param bill 采购单
     */
    default void fillTenantAndOperator(PurchaseBillDTO bill) {
        // 设置租户id
        bill.setTenantId(Long.valueOf(DataContextHolder.getTenantId()));
        // todo 设置租户类型
        bill.setTenantType(TenantTypeEnum.CHAIN_HEADQUARTERS.getCode());
        // todo 设置操作员
        Optional.ofNullable(bill.getPlanner())
                .ifPresent(userId -> bill.setPlanner(WebFrameworkUtils.getLoginUserId().toString()));
        Optional.ofNullable(bill.getChecker())
                .ifPresent(userId -> bill.setChecker(WebFrameworkUtils.getLoginUserId().toString()));
        bill.setPurchaser(WebFrameworkUtils.getLoginUserId().toString());
    }

    /**
     * 计算金额
     * 
     * @param purchaseBill 采购单
     */
    default void calculateAmount(PurchaseBillDTO purchaseBill) {
        List<PurchaseBillDetailDTO> details = Optional.ofNullable(purchaseBill.getDetails())
                .orElseGet(Collections::emptyList);

        // 计算商品种类
        purchaseBill.setProductKind(details.size());

        // 计算采购数量和金额
        BigDecimal totalQuantity = BigDecimal.ZERO;
        BigDecimal totalAmount = BigDecimal.ZERO;

        for (PurchaseBillDetailDTO detail : details) {
            totalQuantity = totalQuantity.add(detail.getPurchaseQuantity());
            totalAmount = totalAmount.add(detail.getPurchaseAmount());
        }

        purchaseBill.setPurchaseQuantity(totalQuantity);
        purchaseBill.setPurchaseAmount(totalAmount);
    }

    /**
     * 智能生成采购描述
     * 
     * @param purchaseBill 采购单
     */
    default void generatePurchaseContent(PurchaseBillDTO purchaseBill) {
        List<PurchaseBillDetailDTO> details = Optional.ofNullable(purchaseBill.getDetails())
                .orElseGet(List::of);

        String content = BillContentUtil.generateContent(details,
                detail -> detail.getExt().getProductInfo());

        purchaseBill.setPurchaseContent(content);
    }

    // ========== 4. 单据拆分方法 ==========

    /**
     * 拆单
     * 根据供应商对采购单进行拆分
     * 
     * @param purchaseBill 采购单
     * @return 拆分后的采购单列表
     */
    default List<PurchaseBillDTO> splitBill(PurchaseBillDTO purchaseBill) {
        // 如果单据未提交，则不需要拆单
        if (!purchaseBill.getSubmitted()) {
            return List.of(purchaseBill);
        }

        // 按供应商分组
        Map<String, List<PurchaseBillDetailDTO>> supplierDetailsMap = purchaseBill.getDetails().stream()
                .collect(Collectors.groupingBy(PurchaseBillDetailDTO::getSupplierGuid));

        // 如果只有一个供应商，则不需要拆单
        if (supplierDetailsMap.keySet().size() <= 1) {
            return List.of(purchaseBill);
        }

        // 为每个供应商创建新的采购单
        return supplierDetailsMap.values().stream()
                .map(details -> createSplitBill(purchaseBill, details))
                .toList();
    }

    /**
     * 创建拆分后的采购单
     * 
     * @param originalBill 原始采购单
     * @param details      拆分后的详情列表
     * @return 新的采购单
     */
    private PurchaseBillDTO createSplitBill(PurchaseBillDTO originalBill, List<PurchaseBillDetailDTO> details) {
        PurchaseBillDTO newBill = new PurchaseBillDTO();
        BeanUtils.copyProperties(originalBill, newBill);

        // 重置单号
        newBill.setPurchaseBillNo(null);
        if (Objects.equals(originalBill.getStatus(), PLAN_CREATED.getCode())) {
            newBill.setPlanBillNo(null);
        }

        // 设置新的明细
        newBill.setDetails(details);

        // 设置供应商信息
        PurchaseBillDetailDTO first = details.getFirst();
        newBill.setSupplierGuid(first.getSupplierGuid());
        newBill.setSupplierName(first.getSupplierName());
        return newBill;
    }

    // ========== 5. 辅助方法 ==========

    /**
     * 填充单号
     * 
     * @param purchaseBill 采购单
     */
    private void fillBillNo(PurchaseBillDTO purchaseBill) {
        Optional.of(PurchaseBillTypeEnum.fromCode(purchaseBill.getBillType()))
                .ifPresentOrElse(billType -> {
                    switch (billType) {
                        case PURCHASE_ORDER -> generatePurchaseOrderNo(purchaseBill);
                        case STORE_REQUISITION, STORE_ALLOCATION, HEADQUARTERS_DISTRIBUTION ->
                            generateStoreAndHeadquartersBillNo(purchaseBill, billType);
                        default -> {
                        }
                    }
                    fillBillNoToDetails(purchaseBill);
                }, () -> fillBillNoToDetails(purchaseBill)); // 即使无匹配类型，也设置详情单号
    }

    /**
     * 生成采购订单类型单号
     * 
     * @param purchaseBill 采购单
     */
    private void generatePurchaseOrderNo(PurchaseBillDTO purchaseBill) {
        Optional.ofNullable(purchaseBill.getPlanBillNo())
                .or(() -> Optional.ofNullable(purchaseBill.getPlanTime())
                        .map(BillNoTypeEnum.PURCHASE_PLAN::getBillNo))
                .ifPresent(purchaseBill::setPlanBillNo);

        // 设置采购订单号
        Optional.ofNullable(purchaseBill.getStatus())
                .filter(status -> status > PLAN_CREATED.getCode())
                .flatMap(status -> Optional.ofNullable(purchaseBill.getPurchaseBillNo())
                        .or(() -> Optional.ofNullable(purchaseBill.getPurchaseTime())
                                .map(BillNoTypeEnum.PURCHASE_ORDER::getBillNo)))
                .ifPresent(purchaseBill::setPurchaseBillNo);
    }

    /**
     * 生成门店要货/调剂/总部铺货单号
     * 
     * @param purchaseBill 采购单
     * @param type         单据类型
     */
    private void generateStoreAndHeadquartersBillNo(PurchaseBillDTO purchaseBill, PurchaseBillTypeEnum type) {
        Optional.of(type)
                .map(t -> switch (t) {
                    case STORE_REQUISITION -> BillNoTypeEnum.STORE_REQUISITION;
                    case STORE_ALLOCATION -> BillNoTypeEnum.STORE_ALLOCATION;
                    case HEADQUARTERS_DISTRIBUTION -> BillNoTypeEnum.HEADQUARTERS_DISTRIBUTION;
                    default -> null;
                })
                .flatMap(billNoType -> Optional.ofNullable(purchaseBill.getPurchaseTime())
                        .map(billNoType::getBillNo))
                .ifPresent(purchaseBillNo -> {
                    purchaseBill.setPurchaseBillNo(purchaseBillNo);
                    purchaseBill.setPlanBillNo(purchaseBillNo); // planBillNo 复用 purchaseBillNo
                });
    }

    /**
     * 设置详情和运输单中的订单号
     * 
     * @param purchaseBill 采购单
     */
    private void fillBillNoToDetails(PurchaseBillDTO purchaseBill) {
        Optional.ofNullable(purchaseBill.getDetails())
                .ifPresent(details -> details.forEach(detail -> {
                    Optional.ofNullable(purchaseBill.getPlanBillNo()).ifPresent(detail::setPlanBillNo);
                    Optional.ofNullable(purchaseBill.getPurchaseBillNo()).ifPresent(detail::setPurchaseBillNo);
                }));

        Optional.ofNullable(purchaseBill.getTransport())
                .ifPresent(transport -> Optional.ofNullable(purchaseBill.getPurchaseBillNo())
                        .ifPresent(transport::setBillNo));
    }

    /**
     * 处理显示状态
     * 
     * @param purchaseBillVO 查询VO
     * @param statusProvider 状态提供者函数
     * @param statusApplier  状态应用函数
     * @param <T>            状态枚举类型
     */
    default <T> void processDisplayStatus(
            PurchaseBillPageReqVO purchaseBillVO,
            Function<Integer, T> statusProvider,
            BiConsumer<T, PurchaseBillPageReqVO> statusApplier) {
        if (purchaseBillVO.getDisplayStatus() != null) {
            T displayStatus = statusProvider.apply(purchaseBillVO.getDisplayStatus());
            statusApplier.accept(displayStatus, purchaseBillVO);
        }
    }

    /**
     * 采购计划单分页查询入参匹配
     *
     * @param purchasePlanVO 采购计划单VO
     */
    default void processPlanPageQueryParams(PurchaseBillPageReqVO purchasePlanVO) {
        purchasePlanVO.setBillType(PURCHASE_ORDER.getCode());
        // 应用显示状态
        processDisplayStatus(
                purchasePlanVO,
                PurchaseBillPageReqVO.PurchasePlanDisplayStatus::fromCode,
                PurchaseBillPageReqVO.PurchasePlanDisplayStatus::apply);
    }

    /**
     * 采购订单分页查询入参匹配
     *
     * @param purchaseOrderVO 采购订单VO
     */
    default void processOrderPageQueryParams(PurchaseBillPageReqVO purchaseOrderVO) {
        purchaseOrderVO.setBillType(PURCHASE_ORDER.getCode());
        // 应用显示状态
        processDisplayStatus(
                purchaseOrderVO,
                PurchaseBillPageReqVO.PurchaseOrderDisplayStatus::fromCode,
                PurchaseBillPageReqVO.PurchaseOrderDisplayStatus::apply);
    }

    /**
     * 门店要货/调剂单分页查询入参匹配
     *
     * @param requisitionAllocationVO 要货/调剂单VO
     */
    default void processRequisitionAllocationPageQueryParams(PurchaseBillPageReqVO requisitionAllocationVO) {
        // purchaseBillVO.setBillType(STORE_REQUISITION.getCode());
        // 应用显示状态
        processDisplayStatus(
                requisitionAllocationVO,
                PurchaseBillPageReqVO.StoreRequisitionAllocationDisplayStatus::fromCode,
                PurchaseBillPageReqVO.StoreRequisitionAllocationDisplayStatus::apply);
    }

    /**
     * 总部铺货单分页查询入参匹配
     *
     * @param distribution 总部铺货单VO
     */
    default void processDistributionPageQueryParams(PurchaseBillPageReqVO distribution) {
        distribution.setBillType(HEADQUARTERS_DISTRIBUTION.getCode());
        // 应用显示状态
        processDisplayStatus(
                distribution,
                PurchaseBillPageReqVO.HeadDistributionDisplayStatus::fromCode,
                PurchaseBillPageReqVO.HeadDistributionDisplayStatus::apply);
    }
}
