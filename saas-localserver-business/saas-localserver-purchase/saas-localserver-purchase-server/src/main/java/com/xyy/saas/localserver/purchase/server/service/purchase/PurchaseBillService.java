package com.xyy.saas.localserver.purchase.server.service.purchase;

import cn.iocoder.yudao.framework.common.exception.ServiceException;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.localserver.purchase.api.purchase.dto.PurchaseBillPageReqDTO;
import com.xyy.saas.localserver.purchase.api.purchase.dto.PurchaseBillDTO;

import java.util.List;

/**
 * 采购单 Service 接口
 * <p>
 * 主要功能：
 * 1. 采购单基础操作（保存、删除、更新、查询）
 * 2. 采购单状态管理
 * 3. 采购单数量计算
 * 4. 采购单分页查询
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface PurchaseBillService {

    // ========== 1. 基础操作 ==========

    /**
     * 保存采购单
     * 
     * @param saveDTOs 待保存的采购单信息列表
     * @throws ServiceException 如果采购单信息为空
     */
    void savePurchaseBills(List<PurchaseBillDTO> saveDTOs);

    /**
     * 删除采购单
     * 
     * @param id 采购单编号
     * @throws ServiceException 如果采购单不存在
     */
    void deletePurchaseBill(Long id);

    /**
     * 更新采购单
     * 
     * @param updateDTO 待更新的采购单信息
     * @throws ServiceException 如果更新冲突
     */
    void updatePurchaseBill(PurchaseBillDTO updateDTO);

    // ========== 2. 状态管理 ==========

    /**
     * 重新计算采购单状态和数量
     * 
     * @param recomputeDTO 需要重算状态和数量的采购单信息
     * @return 更新后的采购单
     */
    PurchaseBillDTO recomputePurchaseBillStatusWithQuantities(PurchaseBillDTO recomputeDTO);

    // ========== 3. 查询操作 ==========

    /**
     * 获得采购单
     * 
     * @param id 采购单编号
     * @return 采购单信息
     * @throws ServiceException 如果采购单不存在
     */
    PurchaseBillDTO getPurchaseBill(Long id);

    /**
     * 获得采购单分页
     * 
     * @param pageReqDTO 分页查询参数
     * @return 采购单分页结果
     */
    PageResult<PurchaseBillDTO> getPurchaseBillPage(PurchaseBillPageReqDTO pageReqDTO);

    /**
     * 获得采购单和详情
     * 
     * @param billNo   采购单号
     * @param tenantId 租户ID
     * @return 采购单信息（包含详情）
     * @throws ServiceException 如果采购单不存在
     */
    PurchaseBillDTO getPurchaseBillWithDetails(String billNo, Long tenantId);

    // /**
    // * 根据出库租户ID获取采购单
    // * @param billNo 单号
    // * @param outboundTenantId 出库租户ID
    // * @return 采购单
    // */
    // PurchaseBillDO getPurchaseBillByOutboundTenantId(String billNo, Long
    // outboundTenantId);
}