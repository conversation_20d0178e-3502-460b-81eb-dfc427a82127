package com.xyy.saas.localserver.purchase.server.utils;

import com.xyy.saas.inquiry.product.api.product.dto.ProductInfoDto;
import lombok.extern.slf4j.Slf4j;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
public class BillContentUtil {

    /**
     * 生成单据内容描述
     * 
     * @param details       详情列表
     * @param productGetter 获取商品信息的方法
     * @return 内容描述
     */
    public static <T> String generateContent(List<T> details, Function<T, ProductInfoDto>productGetter) {
        try {
            if (details == null || details.isEmpty()) {
                log.debug("详情列表为空，返回空字符串");
                return "";
            }

            if (productGetter == null) {
                log.warn("商品信息获取方法为空，返回空字符串");
                return "";
            }

            // 流式拼接+智能截断
            return details.stream()
                    .map(detail -> Optional.ofNullable(productGetter.apply(detail))
                            .map(ProductInfoDto::getCommonName)
                            .orElse(""))
                    .filter(name -> !name.isEmpty())
                    .collect(Collectors.collectingAndThen(
                            Collectors.toList(),
                            BillContentUtil::buildTruncatedString));
        } catch (Exception e) {
            log.error("生成单据内容描述失败", e);
            return "";
        }
    }

    /**
     * 智能字符串截断工具
     */
    public static String buildTruncatedString(List<String> strings) {
        if (strings == null || strings.isEmpty()) {
            return "";
        }

        // 计算总长度
        int totalLength = strings.stream()
                .mapToInt(String::length)
                .sum();

        // 如果总长度小于等于100，直接拼接
        if (totalLength <= 100) {
            return String.join("、", strings);
        }

        // 如果总长度大于100，截断
        StringBuilder result = new StringBuilder();
        int currentLength = 0;
        for (String str : strings) {
            if (currentLength + str.length() + 1 > 100) { // +1 是为了考虑分隔符"、"
                result.append("等");
                break;
            }
            if (currentLength > 0) {
                result.append("、");
                currentLength++;
            }
            result.append(str);
            currentLength += str.length();
        }

        return result.toString();
    }
}