package com.xyy.saas.localserver.purchase.server.service.purchase.impl;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.tenant.core.aop.TenantIgnore;
import com.xyy.saas.localserver.purchase.api.purchase.dto.PurchaseBillDTO;
import com.xyy.saas.localserver.purchase.api.purchase.dto.PurchaseBillDetailDTO;
import com.xyy.saas.localserver.purchase.api.purchase.dto.PurchaseBillPageReqDTO;
import com.xyy.saas.localserver.purchase.enums.purchase.PurchaseBillStatusEnum;
import com.xyy.saas.localserver.purchase.server.convert.extend.PurchaseTransportConvert;
import com.xyy.saas.localserver.purchase.server.convert.purchase.PurchaseBillConvert;
import com.xyy.saas.localserver.purchase.server.convert.purchase.PurchaseBillDetailConvert;
import com.xyy.saas.localserver.purchase.server.dal.dataobject.extend.PurchaseTransportDO;
import com.xyy.saas.localserver.purchase.server.dal.dataobject.purchase.PurchaseBillDO;
import com.xyy.saas.localserver.purchase.server.dal.dataobject.purchase.PurchaseBillDetailDO;
import com.xyy.saas.localserver.purchase.server.dal.mysql.extend.PurchaseTransportMapper;
import com.xyy.saas.localserver.purchase.server.dal.mysql.purchase.PurchaseBillDetailMapper;
import com.xyy.saas.localserver.purchase.server.dal.mysql.purchase.PurchaseBillMapper;
import com.xyy.saas.localserver.purchase.server.service.purchase.PurchaseBillService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.localserver.purchase.enums.ErrorCodeConstants.*;

/**
 * 采购单 Service 实现类
 * <p>
 * 主要功能：
 * 1. 采购单基础操作（保存、删除、更新、查询）
 * 2. 采购单状态管理
 * 3. 采购单数量计算
 * 4. 采购单分页查询
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
@Slf4j
public class PurchaseBillServiceImpl implements PurchaseBillService {

    // ========== 依赖注入 ==========
    @Resource
    protected PurchaseBillMapper purchaseBillMapper;

    @Resource
    protected PurchaseBillDetailMapper purchaseBillDetailMapper;

    @Resource
    protected PurchaseTransportMapper purchaseTransportMapper;

    // ========== 1. 基础操作 ==========

    /**
     * 保存到采购单数据库
     * 
     * @param saveDTOs 待保存的采购单信息列表
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void savePurchaseBills(List<PurchaseBillDTO> saveDTOs) {
        // 1. 获取第一个采购单信息
        PurchaseBillDTO purchaseBillDTO = saveDTOs.getFirst();

        // 2. 判断原单据是否存在，存在则先删除
        if (purchaseBillDTO.getId() != null) {
            // 2.1 查找原单据
            PurchaseBillDO originalBill = purchaseBillMapper.selectById(purchaseBillDTO.getId());
            if (originalBill == null) {
                throw exception(PURCHASE_BILL_NOT_EXISTS);
            }

            // 2.2 删除原单据
            purchaseBillMapper.deleteById(originalBill.getId());

            // 2.3 根据租户ID和计划单号删除所有相关明细
            purchaseBillDetailMapper.deleteByPlanBillNo(originalBill.getPlanBillNo());

            // 2.4 删除运输信息
            purchaseTransportMapper.deleteByBillNo(purchaseBillDTO.getPurchaseBillNo());
        }

        // 3. 转换数据
        List<PurchaseBillDO> purchaseBills = PurchaseBillConvert.INSTANCE.convert2DOList(saveDTOs);
        List<PurchaseBillDetailDO> purchaseBillDetails = PurchaseBillDetailConvert.INSTANCE
                .convert2DOList(saveDTOs.stream()
                        .flatMap(bill -> bill.getDetails().stream())
                        .toList());
        List<PurchaseTransportDO> transports = PurchaseTransportConvert.INSTANCE.convert2DOList(saveDTOs.stream()
                .map(PurchaseBillDTO::getTransport)
                .toList());

        // 4. 保存数据
        purchaseBillMapper.insertBatch(purchaseBills);
        purchaseBillDetailMapper.insertBatch(purchaseBillDetails);
        purchaseTransportMapper.insertBatch(transports);
    }

    /**
     * 删除采购单
     * 
     * @param id 采购单编号
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deletePurchaseBill(Long id) {
        // 1. 校验单据存在
        PurchaseBillDTO purchaseBill = getPurchaseBill(id);

        // 2. 删除明细
        purchaseBillDetailMapper.deleteByPlanBillNo(purchaseBill.getPlanBillNo());

        // 3. 删除主表
        purchaseBillMapper.deleteById(id);
    }

    /**
     * 更新采购单
     * 
     * @param updateDTO 待更新的采购单信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePurchaseBill(PurchaseBillDTO updateDTO) {
        // 1. 转换数据
        PurchaseBillDO purchaseBill = PurchaseBillConvert.INSTANCE.convert2DO(updateDTO);

        // 2. 更新主表
        int affectedRows = purchaseBillMapper.updatePurchaseBillByVersion(purchaseBill);

        // 3. 更新明细
        if (CollectionUtils.isNotEmpty(updateDTO.getDetails())) {
            List<PurchaseBillDetailDO> purchaseBillDetails = PurchaseBillDetailConvert.INSTANCE
                    .convert2DOList(updateDTO.getDetails());
            purchaseBillDetailMapper.updateById(purchaseBillDetails);
        }

        // 4. 校验更新结果
        if (affectedRows == 0) {
            throw exception(PURCHASE_BILL_UPDATE_STATUS_CONFLICT);
        }
    }

    // ========== 2. 状态管理 ==========

    /**
     * 重新计算采购单状态和数量
     * 
     * @param recomputeDTO 需要重算状态和数量的采购单信息
     * @return 更新后的采购单
     */
    @Override
    public PurchaseBillDTO recomputePurchaseBillStatusWithQuantities(PurchaseBillDTO recomputeDTO) {
        // 1. 获取源数据
        PurchaseBillDTO source = getPurchaseBillWithDetails(recomputeDTO.getPurchaseBillNo(),
                recomputeDTO.getTenantId());

        // 2. 合并配送数量
        mergeDeliveryQuantities(source, recomputeDTO);

        // 3. 更新状态
        source.setStatus(determinePurchaseBillStatus(source).getCode());

        // 4. 重新计算聚合数量
        recalculateAggregateQuantities(source);

        return source;
    }

    // ========== 3. 查询操作 ==========

    /**
     * 获得采购单
     * 
     * @param id 采购单编号
     * @return 采购单信息
     */
    @Override
    public PurchaseBillDTO getPurchaseBill(Long id) {
        PurchaseBillDO purchaseBill = purchaseBillMapper.selectById(id);
        if (purchaseBill == null) {
            throw exception(PURCHASE_BILL_NOT_EXISTS);
        }
        return PurchaseBillConvert.INSTANCE.convert2DTO(purchaseBill);
    }

    /**
     * 获得采购单分页
     * 
     * @param pageReqDTO 分页查询参数
     * @return 采购单分页结果
     */
    @Override
    @TenantIgnore
    public PageResult<PurchaseBillDTO> getPurchaseBillPage(PurchaseBillPageReqDTO pageReqDTO) {
        PageResult<PurchaseBillDO> pageResult = purchaseBillMapper.selectPage(pageReqDTO);
        // TODO: 查询审批流获取当前待办人
        return PurchaseBillConvert.INSTANCE.convert2DTO(pageResult);
    }

    /**
     * 获得采购单和详情
     * 
     * @param billNo   采购单号
     * @param tenantId 租户ID
     * @return 采购单信息（包含详情）
     */
    @Override
    public PurchaseBillDTO getPurchaseBillWithDetails(String billNo, Long tenantId) {
        PurchaseBillDTO purchaseBill = purchaseBillMapper.getPurchaseBillWithDetails(billNo, tenantId);
        if (purchaseBill == null) {
            throw exception(PURCHASE_BILL_NOT_EXISTS);
        }
        return purchaseBill;
    }

    // ========== 4. 私有方法 ==========

    /**
     * 合并配送数量（主单+明细）
     * 
     * @param source 源数据
     * @param target 目标数据
     */
    private void mergeDeliveryQuantities(PurchaseBillDTO source, PurchaseBillDTO target) {
        // 1. 更新主单数量
        source.setDeliveredQuantity(target.getDeliveredQuantity());
        source.setReturnableQuantity(target.getReturnableQuantity());

        // 2. 更新明细数量
        Map<String, PurchaseBillDetailDTO> updateDetails = createDetailLookupMap(target);
        source.getDetails().forEach(detail -> Optional.ofNullable(updateDetails.get(detail.getProductPref()))
                .ifPresent(updateDetail -> {
                    detail.setDeliveredQuantity(updateDetail.getDeliveredQuantity());
                    detail.setReturnableQuantity(updateDetail.getReturnableQuantity());
                    detail.setWarehouseQuantity(updateDetail.getWarehouseQuantity());
                }));
    }

    /**
     * 创建明细快速查找映射
     * 
     * @param bill 采购单信息
     * @return 明细映射
     */
    private Map<String, PurchaseBillDetailDTO> createDetailLookupMap(PurchaseBillDTO bill) {
        return CollectionUtils.isEmpty(bill.getDetails()) ? Collections.emptyMap()
                : bill.getDetails().stream()
                        .collect(Collectors.toMap(
                                PurchaseBillDetailDTO::getProductPref,
                                Function.identity(),
                                (existing, replacement) -> {
                                    log.warn("重复的productPref: {}", existing.getProductPref());
                                    return existing;
                                }));
    }

    /**
     * 重新计算聚合数量
     * 
     * @param bill 采购单信息
     */
    private void recalculateAggregateQuantities(PurchaseBillDTO bill) {
        bill.setDeliveredQuantity(sumQuantities(bill.getDetails(), PurchaseBillDetailDTO::getDeliveredQuantity));
        bill.setReturnableQuantity(sumQuantities(bill.getDetails(), PurchaseBillDetailDTO::getReturnableQuantity));
    }

    /**
     * 计算数量总和
     * 
     * @param details 明细列表
     * @param mapper  数量映射函数
     * @return 数量总和
     */
    private BigDecimal sumQuantities(List<PurchaseBillDetailDTO> details,
            Function<PurchaseBillDetailDTO, BigDecimal> mapper) {
        return details.stream()
                .map(mapper)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 判断采购单状态
     * 
     * @param bill 采购单信息
     * @return 采购单状态
     */
    private PurchaseBillStatusEnum determinePurchaseBillStatus(PurchaseBillDTO bill) {
        // 1. 判断是否全部配送完成
        boolean allDelivered = checkAllDelivered(bill.getDetails());
        if (!allDelivered) {
            return PurchaseBillStatusEnum.IN_DELIVERY;
        }

        // 2. 判断是否全部收货
        boolean allReceived = checkAllReceived(bill.getDetails());
        return allReceived ? PurchaseBillStatusEnum.COMPLETED : PurchaseBillStatusEnum.DELIVERED;
    }

    /**
     * 判断是否全部配送完成
     * 
     * @param details 明细列表
     * @return 是否全部配送完成
     */
    private boolean checkAllDelivered(List<PurchaseBillDetailDTO> details) {
        return details.stream()
                .allMatch(detail -> detail.getDeliveredQuantity() != null &&
                        detail.getDeliveredQuantity().compareTo(detail.getPurchaseQuantity()) >= 0);
    }

    /**
     * 判断是否全部收货完成
     * 
     * @param details 明细列表
     * @return 是否全部收货完成
     */
    private boolean checkAllReceived(List<PurchaseBillDetailDTO> details) {
        return details.stream()
                .allMatch(detail -> detail.getWarehouseQuantity() != null &&
                        detail.getWarehouseQuantity().compareTo(detail.getPurchaseQuantity()) >= 0);
    }
}