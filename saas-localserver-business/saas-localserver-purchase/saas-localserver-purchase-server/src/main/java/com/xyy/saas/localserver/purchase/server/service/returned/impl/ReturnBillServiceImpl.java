package com.xyy.saas.localserver.purchase.server.service.returned.impl;

import com.xyy.saas.localserver.inventory.api.campon.InventoryCampOnApi;
import com.xyy.saas.localserver.inventory.api.campon.dto.InventoryCampOnResultDTO;
import com.xyy.saas.localserver.inventory.api.campon.dto.InventorySimpleReleaseDTO;
import com.xyy.saas.localserver.purchase.api.returned.dto.ReturnBillDTO;
import com.xyy.saas.localserver.purchase.api.returned.dto.ReturnBillPageReqDTO;
import com.xyy.saas.localserver.purchase.enums.returned.ReturnBillStatusEnum;
import com.xyy.saas.localserver.purchase.server.convert.inventory.InventoryCampOnConvert;
import com.xyy.saas.localserver.purchase.server.convert.returned.ReturnBillConvert;
import com.xyy.saas.localserver.purchase.server.convert.returned.ReturnBillDetailConvert;
import com.xyy.saas.localserver.purchase.server.dal.dataobject.returned.ReturnBillDO;
import com.xyy.saas.localserver.purchase.server.dal.dataobject.returned.ReturnBillDetailDO;
import com.xyy.saas.localserver.purchase.server.dal.mysql.returned.ReturnBillDetailMapper;
import com.xyy.saas.localserver.purchase.server.dal.mysql.returned.ReturnBillMapper;
import com.xyy.saas.localserver.purchase.server.service.returned.ReturnBillService;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import org.springframework.transaction.annotation.Transactional;
import java.util.*;
import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.localserver.purchase.enums.ErrorCodeConstants.*;
import static com.xyy.saas.localserver.purchase.enums.returned.ReturnBillStatusEnum.*;

/**
 * 退货单 Service 实现类
 *
 * <p>
 * 该实现类负责处理退货单相关的所有业务逻辑，主要包括：
 * </p>
 * <ul>
 * <li>基础操作：保存、删除、查询等</li>
 * <li>状态管理：撤销、驳回、审核、出库等</li>
 * <li>库存处理：预占、释放、出库等</li>
 * </ul>
 *
 * <p>
 * 主要特点：
 * </p>
 * <ul>
 * <li>支持多种租户类型：单体、总部、门店</li>
 * <li>支持完整的单据生命周期管理</li>
 * <li>支持库存预占和释放机制</li>
 * <li>支持乐观锁并发控制</li>
 * </ul>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
public class ReturnBillServiceImpl implements ReturnBillService {

    // ========== 依赖注入 ==========

    /** 退货单 Mapper */
    @Resource
    protected ReturnBillMapper returnBillMapper;

    /** 退货单明细 Mapper */
    @Resource
    private ReturnBillDetailMapper returnBillDetailMapper;

    /** 库存预占 API */
    @Resource
    private InventoryCampOnApi inventoryCampOnApi;

    /**
     * 保存退货单信息
     * 处理流程：
     * 1. 获取第一个退货单信息
     * 2. 判断原单据是否存在，存在则先删除
     * 3. 保存新的退货单信息
     * 4. 保存新的退货单明细
     *
     * @param returnBills 退货单信息列表
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveReturnBills(List<ReturnBillDTO> returnBills) {
        // 1. 获取第一个退货单信息
        ReturnBillDTO returnBill = returnBills.getFirst();

        // 2. 判断原单据是否存在，存在则先删除
        if (returnBill.getId() != null) {
            // 2.1 查找原单据
            ReturnBillDO originalBill = returnBillMapper.selectById(returnBill.getId());
            if (originalBill == null) {
                throw exception(RETURN_BILL_NOT_EXISTS);
            }

            // 2.2 删除原单据
            returnBillMapper.deleteById(originalBill.getId());

            // 2.3 根据单号删除所有相关明细
            returnBillDetailMapper.deleteByBillNo(originalBill.getBillNo());
        }

        // 3. 保存新的退货单
        returnBillMapper.insertBatch(ReturnBillConvert.INSTANCE.convert2DOList(returnBills));

        // 4. 保存新的退货单明细
        List<ReturnBillDetailDO> returnDetails = returnBills.stream()
                .map(ReturnBillDTO::getDetails)
                .flatMap(List::stream)
                .map(ReturnBillDetailConvert.INSTANCE::convert2DetailDO)
                .toList();
        returnBillDetailMapper.insertBatch(returnDetails);
    }

    /**
     * 撤销或驳回退货单
     * 处理流程：
     * 1. 校验原单据状态是否允许撤销
     * 2. 更新单据状态
     * 3. 释放库存预占（如果存在）
     *
     * @param returnBill 退货单信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void revokeOrRejectPurchaseReturn(ReturnBillDTO returnBill) {
        // 1. 校验原单据状态
        ReturnBillDTO originalReturnBill = getReturnBill(returnBill.getId());
        if (!Objects.equals(originalReturnBill.getStatus(), ReturnBillStatusEnum.PENDING_APPROVAL.getCode())) {
            throw exception(RETURN_BILL_STATUS_NOT_SUPPORT_REVOKE);
        }

        // 2. 设置版本号，用于乐观锁控制
        returnBill.setVersion(originalReturnBill.getVersion());

        // 3. 更新状态
        returnBillMapper.updateReturnBillByVersion(ReturnBillConvert.INSTANCE.convert2DO(returnBill));

        // 4. 查询预占单是否存在
        InventoryCampOnResultDTO campOnBill = inventoryCampOnApi
                .queryCampOnBill(InventoryCampOnConvert.INSTANCE.convert2QueryCampOnDto(returnBill));
        if (Objects.nonNull(campOnBill)) {
            // 4.1 释放预占
            inventoryCampOnApi.releaseCampOn(InventoryCampOnConvert.INSTANCE.convert2ReleaseCampOnDto(returnBill));
        }
    }

    /**
     * 审核并出库
     * 处理流程：
     * 1. 校验原单据状态是否允许出库
     * 2. 更新退货单状态为已出库
     * 3. 释放库存预占并执行出库操作
     *
     * @param returnBill 退货单信息
     * @param release    库存释放信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void checkAndOutbound(ReturnBillDTO returnBill, InventorySimpleReleaseDTO release) {
        // 1. 校验原单据状态
        ReturnBillDTO originalReturnBill = getReturnBill(returnBill.getId());
        if (!List.of(PENDING_OUTBOUND, PENDING_REVIEW)
                .contains(ReturnBillStatusEnum.fromCode(originalReturnBill.getStatus()))) {
            throw exception(RETURN_BILL_STATUS_NOT_SUPPORT_REVOKE);
        }

        // 2. 修改退货单状态为已出库
        returnBill.setStatus(OUTBOUND.getCode());
        returnBillMapper.updateReturnBillByVersion(ReturnBillConvert.INSTANCE.convert2DO(returnBill));

        // 3. 调用库存释放预占&追溯码并出库
        inventoryCampOnApi.releaseStock(release);
    }

    /**
     * 更新退货单状态
     * 处理流程：
     * 1. 校验单据版本
     * 2. 更新单据状态
     *
     * @param returnBill 退货单信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStatus(ReturnBillDTO returnBill) {
        // 1. 使用乐观锁更新单据状态
        returnBillMapper.updateReturnBillByVersion(ReturnBillConvert.INSTANCE.convert2DO(returnBill));
    }

    /**
     * 删除退货单
     * 处理流程：
     * 1. 校验单据是否存在
     * 2. 删除单据信息
     *
     * @param id 退货单编号
     */
    @Override
    public void deleteReturnBill(Long id) {
        // 1. 校验存在
        getReturnBill(id);

        // 2. 删除数据
        returnBillMapper.deleteById(id);
    }

    /**
     * 获取退货单信息
     * 处理流程：
     * 1. 根据ID查询单据
     * 2. 校验单据是否存在
     * 3. 转换并返回结果
     *
     * @param id 退货单编号
     * @return 退货单信息
     */
    @Override
    public ReturnBillDTO getReturnBill(Long id) {
        // 1. 查询单据
        ReturnBillDO returnBill = returnBillMapper.selectById(id);
        if (returnBill == null) {
            throw exception(RETURN_BILL_NOT_EXISTS);
        }
        // 2. 转换并返回
        return ReturnBillConvert.INSTANCE.convert2DTO(returnBill);
    }

    /**
     * 根据单号和租户ID获取退货单信息
     * 处理流程：
     * 1. 根据单号和租户ID查询单据
     * 2. 校验单据是否存在
     * 3. 转换并返回结果
     *
     * @param billNo   单据编号
     * @param tenantId 租户编号
     * @return 退货单信息
     */
    @Override
    public ReturnBillDTO getReturnBillByBillNoAndTenantId(String billNo, Long tenantId) {
        // 1. 查询单据
        ReturnBillDO returnBill = returnBillMapper.selectReturnBillByBillNoAndTenantId(billNo, tenantId);
        if (returnBill == null) {
            throw exception(RETURN_BILL_NOT_EXISTS);
        }
        // 2. 转换并返回
        return ReturnBillConvert.INSTANCE.convert2DTO(returnBill);
    }

    /**
     * 根据单号和租户ID获取退货单信息（包含明细）
     * 处理流程：
     * 1. 根据单号和租户ID查询单据
     * 2. 校验单据是否存在
     * 3. 转换并返回结果
     *
     * @param billNo   单据编号
     * @param tenantId 租户编号
     * @return 退货单信息
     */
    @Override
    public ReturnBillDTO getReturnBillWithDetails(String billNo, Long tenantId) {
        // 1. 查询单据
        ReturnBillDTO returnBill = returnBillMapper.getReturnBillWithDetails(billNo, tenantId);
        if (returnBill == null) {
            throw exception(RETURN_BILL_NOT_EXISTS);
        }
        // 2. 转换并返回
        return returnBill;
    }

    /**
     * 获取退货单分页信息
     * 处理流程：
     * 1. 根据查询条件获取分页数据
     * 2. 转换并返回结果
     *
     * @param pageReqDTO 分页查询条件
     * @return 分页结果
     */
    @Override
    public PageResult<ReturnBillDTO> getReturnBillPage(ReturnBillPageReqDTO pageReqDTO) {
        // 1. 查询分页数据
        PageResult<ReturnBillDO> pageResult = returnBillMapper.selectPage(pageReqDTO);
        // 2. 转换并返回
        return ReturnBillConvert.INSTANCE.convert2DTO(pageResult);
    }

}