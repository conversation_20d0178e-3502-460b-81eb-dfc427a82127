package com.xyy.saas.localserver.purchase.server.service.returned;

import com.xyy.saas.localserver.inventory.api.campon.dto.InventorySimpleReleaseDTO;
import com.xyy.saas.localserver.purchase.api.returned.dto.ReturnBillDTO;
import com.xyy.saas.localserver.purchase.api.returned.dto.ReturnBillPageReqDTO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import java.util.List;

/**
 * 退货单 Service 接口
 *
 * <p>
 * 该接口定义了退货单相关的核心业务操作，包括：
 * </p>
 * <ul>
 * <li>基础操作：保存、删除、查询等</li>
 * <li>状态管理：撤销、驳回、审核、出库等</li>
 * <li>库存处理：预占、释放、出库等</li>
 * </ul>
 *
 * <p>
 * 主要特点：
 * </p>
 * <ul>
 * <li>支持多种租户类型：单体、总部、门店</li>
 * <li>支持完整的单据生命周期管理</li>
 * <li>支持库存预占和释放机制</li>
 * </ul>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface ReturnBillService {

    /**
     * 保存退货单信息
     * 处理流程：
     * 1. 校验原单据是否存在
     * 2. 删除原单据及明细（如果存在）
     * 3. 保存新的退货单信息
     * 4. 保存新的退货单明细
     *
     * @param returnBills 退货单信息列表
     */
    void saveReturnBills(List<ReturnBillDTO> returnBills);

    /**
     * 撤销或驳回退货单
     * 处理流程：
     * 1. 校验原单据状态是否允许撤销
     * 2. 更新单据状态
     * 3. 释放库存预占（如果存在）
     *
     * @param returnBill 退货单信息
     */
    void revokeOrRejectPurchaseReturn(ReturnBillDTO returnBill);

    /**
     * 审核并出库
     * 处理流程：
     * 1. 校验原单据状态是否允许出库
     * 2. 更新退货单状态为已出库
     * 3. 释放库存预占并执行出库操作
     *
     * @param returnBill 退货单信息
     * @param release    库存释放信息
     */
    void checkAndOutbound(ReturnBillDTO returnBill, InventorySimpleReleaseDTO release);

    /**
     * 更新退货单状态
     * 处理流程：
     * 1. 校验单据版本
     * 2. 更新单据状态
     *
     * @param returnBill 退货单信息
     */
    void updateStatus(ReturnBillDTO returnBill);

    /**
     * 删除退货单
     * 处理流程：
     * 1. 校验单据是否存在
     * 2. 删除单据信息
     *
     * @param id 退货单编号
     */
    void deleteReturnBill(Long id);

    /**
     * 获取退货单信息
     * 处理流程：
     * 1. 根据ID查询单据
     * 2. 校验单据是否存在
     * 3. 转换并返回结果
     *
     * @param id 退货单编号
     * @return 退货单信息
     */
    ReturnBillDTO getReturnBill(Long id);

    /**
     * 根据单号和租户ID获取退货单信息
     * 处理流程：
     * 1. 根据单号和租户ID查询单据
     * 2. 校验单据是否存在
     * 3. 转换并返回结果
     *
     * @param billNo   单据编号
     * @param tenantId 租户编号
     * @return 退货单信息
     */
    ReturnBillDTO getReturnBillByBillNoAndTenantId(String billNo, Long tenantId);

    /**
     * 获取退货单分页信息
     * 处理流程：
     * 1. 根据查询条件获取分页数据
     * 2. 转换并返回结果
     *
     * @param pageReqDTO 分页查询条件
     * @return 分页结果
     */
    PageResult<ReturnBillDTO> getReturnBillPage(ReturnBillPageReqDTO pageReqDTO);

    /**
     * 根据单号和租户ID获取退货单信息（包含明细）
     * 处理流程：
     * 1. 根据单号和租户ID查询单据
     * 2. 校验单据是否存在
     * 3. 转换并返回结果
     *
     * @param billNo   单据编号
     * @param tenantId 租户编号
     * @return 退货单信息
     */
    ReturnBillDTO getReturnBillWithDetails(String billNo, Long tenantId);
}