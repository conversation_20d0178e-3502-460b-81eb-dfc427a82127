package com.xyy.saas.localserver.purchase.server.service.receive.handler.stockin;

import com.xyy.saas.localserver.entity.enums.tenant.TenantTypeEnum;
import com.xyy.saas.localserver.inventory.api.stock.dto.InventorySelectItemDTO;
import com.xyy.saas.localserver.inventory.api.tracecode.dto.TraceCodeChangeDTO;
import com.xyy.saas.localserver.inventory.enums.InventorySelectEnum;
import com.xyy.saas.localserver.purchase.api.receive.dto.ReceiveBillDTO;
import com.xyy.saas.localserver.purchase.api.receive.dto.ReceiveBillDetailDTO;
import com.xyy.saas.localserver.purchase.enums.receive.ReceiveBillTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;

/**
 * 采购订单收货库存入库处理器
 *
 * <p>
 * 处理采购订单收货时的库存入库逻辑。采购订单收货是最基础的收货类型，
 * 直接根据收货明细中的合格和不合格数量进行库存入库。
 * </p>
 *
 * <p>
 * 处理特点：
 * </p>
 * <ul>
 * <li>不需要查询外部库存变更明细</li>
 * <li>直接使用收货明细中的数量信息</li>
 * <li>按照合格/不合格分别入库到对应货位</li>
 * <li>使用收货明细中的追溯码信息</li>
 * </ul>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
public class PurchaseOrderStockInHandler extends AbstractStockInHandler {

        /**
         * 获取支持的收货单类型
         *
         * @return 支持的收货单类型集合
         */
        @Override
        public Set<ReceiveBillTypeEnum> getSupportedBillTypes() {
                return Set.of(ReceiveBillTypeEnum.PURCHASE_ORDER_RECEIVE);
        }

        /**
         * 获取支持的租户类型
         *
         * @return 单体/总部
         */
        @Override
        public Set<TenantTypeEnum> getSupportedTenantTypes() {
                return Set.of(
                        TenantTypeEnum.SINGLE_STORE,
                        TenantTypeEnum.CHAIN_HEADQUARTERS);
        }

        /**
         * 根据入库单处理库存入库数据
         * <p>
         * 直接根据收货明细中的数量进行库存入库。
         * </p>
         *
         * @param receiveBill          收货单
         * @param selectMap            库存选取Map
         */
        @Override
        protected void processStockInItemsByReceiveBill(ReceiveBillDTO receiveBill, Map<InventorySelectEnum, List<InventorySelectItemDTO>> selectMap) {
                String billNo = receiveBill.getBillNo();
                log.debug("开始处理采购订单收货[{}]的库存入库", billNo);

                List<ReceiveBillDetailDTO> details = receiveBill.getDetails();
                if (CollectionUtils.isEmpty(details)) {
                        log.debug("采购订单收货[{}]无明细，跳过处理", billNo);
                        return;
                }

                // 1. 批量处理收货明细
                details.parallelStream()
                        .forEach(detail -> buildStockInItems(receiveBill, selectMap, detail));

                log.debug("完成采购订单收货[{}]的库存入库处理，处理明细数: {}", billNo, details.size());
        }

        /**
         * 构建入库批次条目
         * <p>
         * 直接根据收货明细中的合格和不合格数量进行库存入库。
         * </p>
         *
         * @param receiveBill          收货单
         * @param selectMap            库存选取Map
         * @param detail               收货明细
         */
        private void buildStockInItems(ReceiveBillDTO receiveBill,
                                        Map<InventorySelectEnum, List<InventorySelectItemDTO>> selectMap,
                                        ReceiveBillDetailDTO detail) {

                String productPref = detail.getProductPref();
                String lotNo = detail.getLotNo();
                log.debug("处理采购订单收货明细 - 商品: {}, 批号: {}", productPref, lotNo);

                // 1. 判断入库数是否小于0
                if (detail.getWarehouseQuantity().compareTo(BigDecimal.ZERO) <= 0) {
                        log.debug("收货明细数量为零，跳过处理 - 商品: {}, 批号: {}", productPref, lotNo);
                        return;
                }

                // 2. 构建货位库存变更映射
                Map<String, BigDecimal> positionChangeStock = allocateQualifiedAndUnqualifiedQuantity(
                        detail,
                        detail.getWarehouseQuantity());

                if (positionChangeStock.isEmpty()) {
                        log.warn("收货明细无有效库存变更，跳过处理 - 商品: {}, 批号: {}, 合格数量: {}, 不合格数量: {}",
                                productPref,
                                lotNo,
                                detail.getWarehouseQuantity(),
                                BigDecimal.ZERO);
                        return;
                }

                // 3. 获取追溯码信息
                List<TraceCodeChangeDTO> traceCodes = getDetailTraceCodes(detail);

                // 4. 添加库存选择项
                inventorySelectItemConvert.addInventorySelectItem(
                        null,
                        selectMap,
                        detail,
                        receiveBill.getSupplierGuid(),
                        positionChangeStock,
                        null,
                        null,
                        traceCodes);

                log.debug("完成采购订单收货明细处理 - 商品: {}, 批号: {}, 货位变更: {}, 追溯码数: {}",
                        productPref,
                        lotNo,
                        positionChangeStock.size(),
                        traceCodes.size());
        }
}