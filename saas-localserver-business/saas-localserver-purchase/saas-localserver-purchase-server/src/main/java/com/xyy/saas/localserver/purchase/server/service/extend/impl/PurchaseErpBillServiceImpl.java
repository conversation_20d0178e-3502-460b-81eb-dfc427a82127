package com.xyy.saas.localserver.purchase.server.service.extend.impl;

import com.xyy.saas.localserver.purchase.api.extend.dto.PurchaseErpBillDTO;
import com.xyy.saas.localserver.purchase.api.extend.dto.PurchaseErpBillPageReqDTO;
import com.xyy.saas.localserver.purchase.server.convert.extend.PurchaseErpBillConvert;
import com.xyy.saas.localserver.purchase.server.dal.dataobject.extend.PurchaseErpBillDO;
import com.xyy.saas.localserver.purchase.server.dal.mysql.extend.PurchaseErpBillMapper;
import com.xyy.saas.localserver.purchase.server.service.extend.PurchaseErpBillService;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.localserver.purchase.enums.ErrorCodeConstants.*;
import org.springframework.transaction.annotation.Transactional;

/**
 * 采购-三方erp单据信息 Service 实现类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
@Validated
public class PurchaseErpBillServiceImpl implements PurchaseErpBillService {

    @Resource
    private PurchaseErpBillMapper purchaseErpBillMapper;

    /**
     * 创建采购-三方erp单据信息
     *
     * @param createDTO 创建信息
     * @return 创建结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createPurchaseErpBill(PurchaseErpBillDTO createDTO) {
        // 1. 将 DTO 转换为 DO
        PurchaseErpBillDO erpBill = PurchaseErpBillConvert.INSTANCE.convert2DO(createDTO);
        // 2. 插入数据
        purchaseErpBillMapper.insert(erpBill);
        // 3. 返回主键
        return erpBill.getId();
    }

    /**
     * 更新采购-三方erp单据信息
     *
     * @param updateDTO 更新信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePurchaseErpBill(PurchaseErpBillDTO updateDTO) {
        // 1. 校验存在
        validatePurchaseErpBillExists(updateDTO.getId());
        // 2. 将 DTO 转换为 DO
        PurchaseErpBillDO erpBill = PurchaseErpBillConvert.INSTANCE.convert2DO(updateDTO);
        // 3. 更新数据
        purchaseErpBillMapper.updateById(erpBill);
    }

    /**
     * 删除采购-三方erp单据信息
     *
     * @param id 编号
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deletePurchaseErpBill(Long id) {
        // 1. 校验存在
        validatePurchaseErpBillExists(id);
        // 2. 删除数据
        purchaseErpBillMapper.deleteById(id);
    }

    /**
     * 获得采购-三方erp单据信息
     *
     * @param id 编号
     * @return 采购-三方erp单据信息
     */
    @Override
    public PurchaseErpBillDO getPurchaseErpBill(Long id) {
        // 1. 查询数据
        return purchaseErpBillMapper.selectById(id);
    }

    /**
     * 获得采购-三方erp单据信息分页
     *
     * @param pageReqDTO 分页查询参数
     * @return 采购-三方erp单据信息分页
     */
    @Override
    public PageResult<PurchaseErpBillDTO> getPurchaseErpBillPage(PurchaseErpBillPageReqDTO pageReqDTO) {
        // 1. 分页查询
        PageResult<PurchaseErpBillDO> pageResult = purchaseErpBillMapper.selectPage(pageReqDTO);
        // 2. 转换结果
        return PurchaseErpBillConvert.INSTANCE.convert2DTO(pageResult);
    }

    /**
     * 校验采购-三方erp单据信息是否存在
     *
     * @param id 编号
     */
    private void validatePurchaseErpBillExists(Long id) {
        if (purchaseErpBillMapper.selectById(id) == null) {
            throw exception(PURCHASE_ERP_BILL_NOT_EXISTS);
        }
    }
}