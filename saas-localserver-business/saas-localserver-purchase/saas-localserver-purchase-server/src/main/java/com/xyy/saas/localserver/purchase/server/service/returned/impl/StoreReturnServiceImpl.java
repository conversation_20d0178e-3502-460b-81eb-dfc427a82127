package com.xyy.saas.localserver.purchase.server.service.returned.impl;

import cn.hutool.core.lang.Assert;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import com.xyy.saas.localserver.inventory.api.campon.InventoryCampOnApi;
import com.xyy.saas.localserver.purchase.api.returned.dto.ReturnBillDTO;
import com.xyy.saas.localserver.purchase.api.returned.dto.ReturnBillPageReqDTO;
import com.xyy.saas.localserver.purchase.enums.returned.ReturnBillStatusEnum;
import com.xyy.saas.localserver.purchase.server.admin.receive.vo.ReceiveBillSaveReqVO;
import com.xyy.saas.localserver.purchase.server.convert.inventory.InventoryCampOnConvert;
import com.xyy.saas.localserver.purchase.server.convert.returned.ReturnBillConvert;
import com.xyy.saas.localserver.purchase.server.convert.returned.ReturnBillDetailConvert;
import com.xyy.saas.localserver.purchase.server.protocol.CloudServiceClient;
import com.xyy.saas.localserver.purchase.server.protocol.receive.ReceiveClient;
import com.xyy.saas.localserver.purchase.server.service.returned.StoreReturnService;
import com.xyy.saas.localserver.purchase.server.service.returned.ReturnBillService;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import jakarta.annotation.Resource;
import java.util.List;
import java.util.Objects;
import static com.xyy.saas.localserver.purchase.enums.returned.ReturnBillStatusEnum.PENDING_APPROVAL;

/**
 * 门店退货单 Service 实现类
 *
 * <p>
 * 该实现类主要处理门店退货相关的业务逻辑，包括：
 * </p>
 * <ul>
 * <li>退货单的保存和撤销</li>
 * <li>退货单的复核和出库</li>
 * <li>退货单的删除和查询</li>
 * <li>与云端服务的交互</li>
 * </ul>
 *
 * <p>
 * 主要特点：
 * </p>
 * <ul>
 * <li>支持门店退货的业务场景</li>
 * <li>支持库存预占和释放机制</li>
 * <li>支持与云端服务的集成</li>
 * <li>支持完整的单据生命周期管理</li>
 * </ul>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
@Validated
public class StoreReturnServiceImpl implements StoreReturnService {

    @Resource
    private ReturnBillService returnBillService;

    @Resource
    private CloudServiceClient cloudServiceClient;

    @Resource
    private InventoryCampOnApi inventoryCampOnApi;

    // ========== 核心业务方法 ==========

    /**
     * 保存门店退货单
     * 处理流程：
     * 1. 填充商品信息
     * 2. 填充退货单内容
     * 3. 保存门店退货单
     * 4. 处理提交状态：
     * 4.1 预占库存和追溯码
     * 4.2 发起审批流
     *
     * @param returnBill 退货单信息
     * @return 退货单编号
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveStoreReturnBill(ReturnBillDTO returnBill) {
        // 1. 填充商品信息
        ReturnBillDetailConvert.INSTANCE.fillProductInfo(returnBill.getDetails());

        // 2. 填充退货单内容
        ReturnBillConvert.INSTANCE.fillReturnContent(returnBill);

        // 3. 保存门店退货单
        returnBillService.saveReturnBills(List.of(returnBill));

        // 4. 如果提交&待审批状态，记录本地消息&发起审批流
        if (returnBill.getSubmitted()
                && Objects.equals(ReturnBillStatusEnum.fromCode(returnBill.getStatus()), PENDING_APPROVAL)) {
            // 4.1 预占库存&追溯码
            inventoryCampOnApi.campOn(InventoryCampOnConvert.INSTANCE.convert2InventoryCampOn(returnBill));

            // TODO: 发起审批流
        }

        return returnBill.getId();
    }

    /**
     * 撤销门店退货单
     * 处理流程：
     * 1. 调用基础服务撤销或拒绝退货单
     *
     * @param returnBill 退货单信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void revokeStoreReturn(ReturnBillDTO returnBill) {
        // 调用基础服务撤销或拒绝退货单
        returnBillService.revokeOrRejectPurchaseReturn(returnBill);
    }

    /**
     * 复核并出库
     * 处理流程：
     * 1. 修改退货单状态为已出库，释放预占扣减库存
     * 2. 生成总部收货对象
     * 3. 记录本地消息并调用云端总部收货接口
     *
     * @param returnBill 退货单信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void checkAndOutbound(ReturnBillDTO returnBill) {
        // 1. 修改退货单状态为已出库，释放预占扣减库存
        returnBillService.checkAndOutbound(returnBill,
                InventoryCampOnConvert.INSTANCE.convert2ReleaseCampOnDto(returnBill));

        // 2. 生成总部收货对象
        ReceiveBillSaveReqVO headReceiveBill = ReturnBillConvert.INSTANCE.generateHeadReceiveBill(returnBill);

        // 3. 记录本地消息并调用云端总部收货接口（生成总部收货单）
        CommonResult<Long> result = cloudServiceClient
                .call(() -> cloudServiceClient.getClient(ReceiveClient.class).createHeadReceive(headReceiveBill));
        Assert.state(result.isSuccess(), result.getMsg());
    }

    // ========== 基础操作方法 ==========

    /**
     * 删除门店退货单
     * 处理流程：
     * 1. 调用基础服务删除退货单
     *
     * @param id 退货单编号
     */
    @Override
    public void deleteStoreReturnBill(Long id) {
        // 调用基础服务删除退货单
        returnBillService.deleteReturnBill(id);
    }

    /**
     * 获取门店退货单信息
     * 处理流程：
     * 1. 调用基础服务获取退货单信息
     *
     * @param id 退货单编号
     * @return 退货单信息
     */
    @Override
    public ReturnBillDTO getStoreReturnBill(Long id) {
        // 调用基础服务获取退货单信息
        return returnBillService.getReturnBill(id);
    }

    /**
     * 获取门店退货单分页信息
     * 处理流程：
     * 1. 调用基础服务获取分页数据
     *
     * @param pageReqDTO 分页查询条件
     * @return 分页结果
     */
    @Override
    public PageResult<ReturnBillDTO> getStoreReturnBillPage(ReturnBillPageReqDTO pageReqDTO) {
        // 调用基础服务获取分页数据
        return returnBillService.getReturnBillPage(pageReqDTO);
    }

    /**
     * 根据单号和租户ID获取门店退货单信息（包含明细）
     * 处理流程：
     * 1. 调用基础服务获取退货单信息
     *
     * @param billNo   单据编号
     * @param tenantId 租户编号
     * @return 退货单信息
     */
    @Override
    public ReturnBillDTO getStoreReturnBillWithDetails(String billNo, Long tenantId) {
        // 调用基础服务获取退货单信息
        return returnBillService.getReturnBillWithDetails(billNo, tenantId);
    }
}