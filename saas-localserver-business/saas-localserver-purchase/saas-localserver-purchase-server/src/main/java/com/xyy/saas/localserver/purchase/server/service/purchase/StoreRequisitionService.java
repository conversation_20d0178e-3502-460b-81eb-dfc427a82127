package com.xyy.saas.localserver.purchase.server.service.purchase;

import cn.iocoder.yudao.framework.common.exception.ServiceException;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.localserver.purchase.api.purchase.dto.PurchaseBillDTO;
import com.xyy.saas.localserver.purchase.api.purchase.dto.PurchaseBillPageReqDTO;

/**
 * 门店要货 Service 接口
 * <p>
 * 主要功能：
 * 1. 门店要货单基础操作（保存、删除、更新、查询）
 * 2. 门店要货单状态管理
 * 3. 门店要货单分页查询
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface StoreRequisitionService {

    // ========== 1. 基础操作 ==========

    /**
     * 保存门店要货单
     *
     * @param saveDTO 待保存的门店要货单信息
     * @throws ServiceException 如果门店要货单信息为空
     */
    void saveRequisitionBill(PurchaseBillDTO saveDTO);

    /**
     * 撤销要货
     *
     * @param revokeDTO 待撤销的门店要货单信息
     * @throws ServiceException 如果门店要货单不存在
     */
    void revokeRequisition(PurchaseBillDTO revokeDTO);

    /**
     * 删除门店要货单
     *
     * @param id 门店要货单编号
     * @throws ServiceException 如果门店要货单不存在
     */
    void deleteRequisitionBill(Long id);

    /**
     * 更新门店要货单并刷新业务状态
     *
     * @param updateDTO 待更新的门店要货单信息
     * @throws ServiceException 如果更新冲突
     */
    void updateRequisitionBillWithStatusRefresh(PurchaseBillDTO updateDTO);

    // ========== 2. 查询操作 ==========

    /**
     * 获得门店要货单
     *
     * @param id 门店要货单编号
     * @return 门店要货单信息
     * @throws ServiceException 如果门店要货单不存在
     */
    PurchaseBillDTO getRequisitionBill(Long id);

    /**
     * 获得门店要货单及详情
     *
     * @param billNo   门店要货单号
     * @param tenantId 租户ID
     * @return 门店要货单信息（包含详情）
     * @throws ServiceException 如果门店要货单不存在
     */
    PurchaseBillDTO getRequisitionBillWithDetails(String billNo, Long tenantId);

    /**
     * 获得门店要货单分页
     *
     * @param pageReqDTO 分页查询参数
     * @return 门店要货单分页结果
     */
    PageResult<PurchaseBillDTO> getRequisitionBillPage(PurchaseBillPageReqDTO pageReqDTO);
}