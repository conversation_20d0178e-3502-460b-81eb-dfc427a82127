package com.xyy.saas.localserver.purchase.server.service.receive;

import com.xyy.saas.localserver.purchase.api.receive.dto.ReceiveBillDTO;
import com.xyy.saas.localserver.purchase.api.receive.dto.ReceiveBillPageReqDTO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;

/**
 * 收货单 Service 接口
 *
 * <p>
 * 该接口定义了收货单相关的核心业务操作，包括：
 * </p>
 * <ul>
 * <li>收货、验收、入库等基础操作</li>
 * <li>一步入库、拒收入库等特殊操作</li>
 * <li>单据的增删改查等基础功能</li>
 * </ul>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface ReceiveBillService {

    /**
     * 收货操作
     * 处理流程：
     * 1. 填充商品信息
     * 2. 填充收货内容
     * 3. 保存单据
     * 4. 处理拒收情况
     * 5. 更新采购单状态
     * 6. 更新追溯码
     *
     * @param bill 收货单信息
     * @return 收货单编号
     */
    Long receive(ReceiveBillDTO bill);

    /**
     * 验收操作
     * 处理流程：
     * 1. 校验收货单存在
     * 2. 保存验收信息
     *
     * @param bill 验收单信息
     * @return 验收单编号
     */
    Long accept(ReceiveBillDTO bill);

    /**
     * 入库操作
     * 处理流程：
     * 1. 校验验收单存在
     * 2. 保存入库信息
     * 3. 更新采购单状态
     * 4. 更新库存
     * 5. 处理拒收情况
     *
     * @param bill 入库单信息
     * @return 入库单编号
     */
    Long warehousing(ReceiveBillDTO bill);

    /**
     * 回退操作
     * 处理流程：
     * 1. 校验单据存在
     * 2. 校验单据状态
     * 3. 执行回退
     * 4. 处理追溯码
     *
     * @param id 收货单编号
     */
    void rollback(Long id);

    /**
     * 一步入库操作
     * 处理流程：
     * 1. 填充商品信息
     * 2. 拆分单据（中药/西药）
     * 3. 处理拆分单据
     * 4. 批量保存
     * 5. 更新库存
     *
     * @param bill 入库单信息
     * @return 拆分后的单据数量
     */
    Integer oneStepWarehousing(ReceiveBillDTO bill);

    /**
     * 拒收入库操作
     * 处理流程：
     * 1. 填充商品信息
     * 2. 填充收货内容
     * 3. 查询默认人员
     * 4. 查询默认货位
     * 5. 更新库存
     * 6. 保存单据
     *
     * @param bill 拒收入库单信息
     */
    void rejectWarehousing(ReceiveBillDTO bill);

    /**
     * 保存收货单
     * 处理流程：
     * 1. 主表操作（新增/更新）
     * 2. 明细处理
     * 3. 关联信息处理
     *
     * @param receiveBill 收货单信息
     */
    void saveReceiveBill(ReceiveBillDTO receiveBill);

    /**
     * 删除收货单
     * 处理流程：
     * 1. 校验单据存在
     * 2. 删除明细
     * 3. 删除运输信息
     * 4. 删除发票信息
     * 5. 删除主表
     *
     * @param id 收货单编号
     */
    void deleteReceiveBill(Long id);

    /**
     * 获取收货单
     * 处理流程：
     * 1. 查询单据
     * 2. 校验存在
     * 3. 转换返回
     *
     * @param id 收货单编号
     * @return 收货单信息
     */
    ReceiveBillDTO getReceiveBill(Long id);

    /**
     * 获取收货单及详情
     * 处理流程：
     * 1. 查询单据及详情
     * 2. 校验存在
     * 3. 填充商品信息
     *
     * @param billNo   单据编号
     * @param tenantId 租户编号
     * @return 收货单及详情信息
     */
    ReceiveBillDTO getReceiveBillAndDetails(String billNo, Long tenantId);

    /**
     * 获取收货单分页
     * 处理流程：
     * 1. 查询分页
     * 2. 转换结果
     * 3. 补充信息
     *
     * @param pageReqDTO 分页查询条件
     * @return 分页结果
     */
    PageResult<ReceiveBillDTO> getReceiveBillPage(ReceiveBillPageReqDTO pageReqDTO);
}