package com.xyy.saas.localserver.purchase.server.service.purchase.impl;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.localserver.purchase.api.purchase.dto.PurchaseBillDTO;
import com.xyy.saas.localserver.purchase.api.purchase.dto.PurchaseBillPageReqDTO;
import com.xyy.saas.localserver.purchase.enums.purchase.PurchaseBillStatusEnum;
import com.xyy.saas.localserver.purchase.enums.returned.ReturnBillStatusEnum;
import com.xyy.saas.localserver.purchase.server.convert.purchase.PurchaseBillConvert;
import com.xyy.saas.localserver.purchase.server.convert.purchase.PurchaseBillDetailConvert;
import com.xyy.saas.localserver.purchase.server.service.purchase.PurchaseBillService;
import com.xyy.saas.localserver.purchase.server.service.purchase.StoreRequisitionService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;
import java.util.Objects;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.localserver.purchase.enums.ErrorCodeConstants.REQUISITION_BILL_STATUS_NOT_SUPPORT_REVOKE;
import static com.xyy.saas.localserver.purchase.enums.purchase.PurchaseBillStatusEnum.PENDING_APPROVAL;

/**
 * 门店要货单 Service 实现类
 * <p>
 * 主要功能：
 * 1. 门店要货单基础操作（保存、删除、更新、查询）
 * 2. 门店要货单状态管理
 * 3. 门店要货单分页查询
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
@Slf4j
public class StoreRequisitionServiceImpl implements StoreRequisitionService {

    // ========== 依赖注入 ==========
    @Resource
    private PurchaseBillService purchaseBillService;

    // ========== 1. 基础操作 ==========

    /**
     * 保存门店要货单
     *
     * @param saveDTO 待保存的门店要货单信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveRequisitionBill(PurchaseBillDTO saveDTO) {
        // 1. 填充商品信息
        PurchaseBillDetailConvert.INSTANCE.fillProductInfo(saveDTO.getDetails());

        // 2. 填充要货内容
        PurchaseBillConvert.INSTANCE.fillPurchaseContent(saveDTO);

        // 3. 保存单据信息
        purchaseBillService.savePurchaseBills(List.of(saveDTO));

        // 4. 如果提交，处理后续逻辑
        if (saveDTO.getSubmitted() && PurchaseBillStatusEnum.fromCode(saveDTO.getStatus()).equals(PENDING_APPROVAL)) {
            // TODO: 1.写本地消息表
            // TODO: 2.HTTP调用云端库存接口，预占库存
            // TODO: 3.根据预占库存结果，HTTP调用云端采购接口，生成缺货单
            // TODO: 4.HTTP调用云端接口，创建审批流
        }
    }

    /**
     * 撤销门店要货单
     *
     * @param revokeDTO 待撤销的门店要货单信息
     */
    @Override
    public void revokeRequisition(PurchaseBillDTO revokeDTO) {
        // 1. 校验原单据状态
        PurchaseBillDTO originalRequisitionBill = purchaseBillService.getPurchaseBill(revokeDTO.getId());
        if (!Objects.equals(originalRequisitionBill.getStatus(), ReturnBillStatusEnum.PENDING_APPROVAL.getCode())) {
            throw exception(REQUISITION_BILL_STATUS_NOT_SUPPORT_REVOKE);
        }

        // 2. 更新版本号
        revokeDTO.setVersion(originalRequisitionBill.getVersion());

        // 3. 更新单据
        purchaseBillService.updatePurchaseBill(revokeDTO);
    }

    /**
     * 删除门店要货单
     *
     * @param id 门店要货单编号
     */
    @Override
    public void deleteRequisitionBill(Long id) {
        purchaseBillService.deletePurchaseBill(id);
    }

    /**
     * 更新门店要货单并刷新业务状态
     *
     * @param updateDTO 待更新的门店要货单信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateRequisitionBillWithStatusRefresh(PurchaseBillDTO updateDTO) {
        // 1. 执行状态和数量的重新计算
        PurchaseBillDTO updatedBill = purchaseBillService.recomputePurchaseBillStatusWithQuantities(updateDTO);

        // 2. 执行带版本校验的更新
        purchaseBillService.updatePurchaseBill(updatedBill);
    }

    // ========== 2. 查询操作 ==========

    /**
     * 获得门店要货单
     *
     * @param id 门店要货单编号
     * @return 门店要货单信息
     */
    @Override
    public PurchaseBillDTO getRequisitionBill(Long id) {
        return purchaseBillService.getPurchaseBill(id);
    }

    /**
     * 获得门店要货单和详情
     *
     * @param billNo   门店要货单号
     * @param tenantId 租户ID
     * @return 门店要货单信息（包含详情）
     */
    @Override
    public PurchaseBillDTO getRequisitionBillWithDetails(String billNo, Long tenantId) {
        return purchaseBillService.getPurchaseBillWithDetails(billNo, tenantId);
    }

    /**
     * 获得门店要货单分页
     *
     * @param pageReqDTO 分页查询参数
     * @return 门店要货单分页结果
     */
    @Override
    public PageResult<PurchaseBillDTO> getRequisitionBillPage(PurchaseBillPageReqDTO pageReqDTO) {
        return purchaseBillService.getPurchaseBillPage(pageReqDTO);
    }
}