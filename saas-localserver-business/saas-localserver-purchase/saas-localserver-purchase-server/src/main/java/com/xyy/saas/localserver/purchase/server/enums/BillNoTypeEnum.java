package com.xyy.saas.localserver.purchase.server.enums;

import com.xyy.saas.localserver.entity.config.db.BillNoGenerator;
import com.xyy.saas.localserver.purchase.server.dal.dataobject.purchase.PurchaseBillDO;
import com.xyy.saas.localserver.purchase.server.dal.dataobject.receive.ReceiveBillDO;
import com.xyy.saas.localserver.purchase.server.dal.dataobject.returned.ReturnBillDO;
import com.xyy.saas.localserver.purchase.server.dal.dataobject.stockout.StockoutBillDO;
import com.xyy.saas.localserver.purchase.server.dal.dataobject.supplier.SupplierDO;
import lombok.Getter;
import java.time.LocalDateTime;

/**
 * 单据编号类型枚举
 */
@Getter
public enum BillNoTypeEnum {

    PURCHASE_PLAN("JHD", "采购计划单", PurchaseBillDO.class, "plan_bill_no"),
    PURCHASE_ORDER("CGD", "采购单", PurchaseBillDO.class, "purchase_bill_no"),
    STORE_REQUISITION("YHD", "要货申请单", PurchaseBillDO.class, "purchase_bill_no"),
    STORE_ALLOCATION("YHTJD", "要货调剂单", PurchaseBillDO.class, "purchase_bill_no"),
    HEADQUARTERS_DISTRIBUTION("PHD", "总部铺货单", PurchaseBillDO.class, "purchase_bill_no"),
    RECEIVE("SHD", "收货单", ReceiveBillDO.class, "bill_no"),
    RETURN("THD", "退货单", ReturnBillDO.class, "bill_no"),
    STOCKOUT("QHD", "缺货单", StockoutBillDO.class, "bill_no"),
    OUTBOUND_ORDER("CKD", "总部出库单", ReceiveBillDO.class, "delivery_bill_no"),
    SUPPLIER_GUID("GYS", "供应商信息", SupplierDO.class, "guid");

    private final String prefix;
    private final String description;
    private final Class<?> entityClass;
    private final String columnName;

    BillNoTypeEnum(String prefix, String description, Class<?> entityClass, String columnName) {
        this.prefix = prefix;
        this.description = description;
        this.entityClass = entityClass;
        this.columnName = columnName;
    }

    /**
     * 获取单据编号
     */
    public String getBillNo(LocalDateTime localDateTime) {
        return BillNoGenerator.generateBillNoByDate(prefix, entityClass, columnName, localDateTime);
    }

}