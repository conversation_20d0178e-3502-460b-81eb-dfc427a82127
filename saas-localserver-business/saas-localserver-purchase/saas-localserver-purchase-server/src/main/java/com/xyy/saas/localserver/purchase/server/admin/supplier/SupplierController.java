package com.xyy.saas.localserver.purchase.server.admin.supplier;

import com.xyy.saas.localserver.purchase.api.supplier.dto.SupplierDTO;
import com.xyy.saas.localserver.purchase.api.supplier.dto.SupplierPageReqDTO;
import com.xyy.saas.localserver.purchase.server.admin.supplier.vo.SupplierPageReqVO;
import com.xyy.saas.localserver.purchase.server.admin.supplier.vo.SupplierRespVO;
import com.xyy.saas.localserver.purchase.server.admin.supplier.vo.SupplierSaveReqVO;
import com.xyy.saas.localserver.purchase.server.convert.supplier.SupplierConvert;
import com.xyy.saas.localserver.purchase.server.service.supplier.SupplierService;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

/**
 * 管理后台 - 供应商信息 Controller
 *
 * <p>
 * 该Controller提供了供应商信息相关的REST API，包括：
 * </p>
 * <ul>
 * <li>供应商信息的增删改查等基础操作</li>
 * <li>供应商信息的分页查询</li>
 * <li>供应商信息的Excel导出</li>
 * </ul>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Tag(name = "管理后台 - 供应商信息")
@RestController
@RequestMapping("/saas/purchase/supplier")
@Validated
public class SupplierController {

    @Resource
    private SupplierService supplierService;

    // ========== 基础操作方法 ==========

    /**
     * 创建供应商信息
     *
     * <p>
     * 该接口用于创建新的供应商信息，主要处理以下步骤：
     * </p>
     * <ol>
     * <li>将请求对象转换为DTO对象</li>
     * <li>调用Service层创建供应商信息</li>
     * </ol>
     *
     * @param createReqVO 创建供应商的请求对象，包含供应商的基本信息
     * @return 供应商编号
     */
    @PostMapping("/create")
    @Operation(summary = "创建供应商信息")
    @PreAuthorize("@ss.hasPermission('saas:purchase:supplier:create')")
    public CommonResult<String> createSupplier(@Valid @RequestBody SupplierSaveReqVO createReqVO) {
        // 1. 对象转换
        SupplierDTO supplier = SupplierConvert.INSTANCE.convert2DTO(createReqVO);
        // 2. 创建供应商
        return success(supplierService.createSupplier(supplier));
    }

    /**
     * 更新供应商信息
     *
     * <p>
     * 该接口用于更新已存在的供应商信息，主要处理以下步骤：
     * </p>
     * <ol>
     * <li>将请求对象转换为DTO对象</li>
     * <li>调用Service层更新供应商信息</li>
     * </ol>
     *
     * @param updateReqVO 更新供应商的请求对象，包含需要更新的供应商信息
     * @return 是否更新成功
     */
    @PutMapping("/update")
    @Operation(summary = "更新供应商信息")
    @PreAuthorize("@ss.hasPermission('saas:purchase:supplier:update')")
    public CommonResult<Boolean> updateSupplier(@Valid @RequestBody SupplierSaveReqVO updateReqVO) {
        // 1. 对象转换
        SupplierDTO supplier = SupplierConvert.INSTANCE.convert2DTO(updateReqVO);
        // 2. 更新供应商
        supplierService.updateSupplier(supplier);
        return success(true);
    }

    /**
     * 删除供应商信息
     *
     * <p>
     * 该接口用于删除指定的供应商信息，主要处理以下步骤：
     * </p>
     * <ol>
     * <li>调用Service层删除供应商信息</li>
     * </ol>
     *
     * @param id 供应商编号
     * @return 是否删除成功
     */
    @DeleteMapping("/delete")
    @Operation(summary = "删除供应商信息")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('saas:purchase:supplier:delete')")
    public CommonResult<Boolean> deleteSupplier(@RequestParam("id") Long id) {
        // 1. 删除供应商
        supplierService.deleteSupplier(id);
        return success(true);
    }

    /**
     * 获取供应商信息
     *
     * <p>
     * 该接口用于获取指定供应商的详细信息，主要处理以下步骤：
     * </p>
     * <ol>
     * <li>调用Service层查询供应商信息</li>
     * <li>将DTO对象转换为VO对象返回</li>
     * </ol>
     *
     * @param id 供应商编号
     * @return 供应商详细信息
     */
    @GetMapping("/get")
    @Operation(summary = "获得供应商信息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('saas:purchase:supplier:query')")
    public CommonResult<SupplierRespVO> getSupplier(@RequestParam("id") Long id) {
        // 1. 查询供应商
        SupplierDTO supplier = supplierService.getSupplier(id);
        // 2. 转换返回
        return success(SupplierConvert.INSTANCE.convert2VO(supplier));
    }

    // ========== 查询方法 ==========

    /**
     * 获取供应商信息分页
     *
     * <p>
     * 该接口用于分页查询供应商信息，主要处理以下步骤：
     * </p>
     * <ol>
     * <li>将请求对象转换为DTO对象</li>
     * <li>调用Service层进行分页查询</li>
     * <li>将查询结果转换为VO对象返回</li>
     * </ol>
     *
     * @param pageReqVO 分页查询条件
     * @return 分页查询结果
     */
    @GetMapping("/page")
    @Operation(summary = "获得供应商信息分页")
    @PreAuthorize("@ss.hasPermission('saas:purchase:supplier:query')")
    public CommonResult<PageResult<SupplierRespVO>> getSupplierPage(@Valid SupplierPageReqVO pageReqVO) {
        // 1. 对象转换
        SupplierPageReqDTO pageReqDTO = SupplierConvert.INSTANCE.convert2DTO(pageReqVO);
        // 2. 查询分页
        PageResult<SupplierDTO> pageResult = supplierService.getSupplierPage(pageReqDTO);
        // 3. 转换返回
        return success(SupplierConvert.INSTANCE.convert2VO(pageResult));
    }

    // ========== 导出方法 ==========

    /**
     * 导出供应商信息Excel
     *
     * <p>
     * 该接口用于导出供应商信息到Excel文件，主要处理以下步骤：
     * </p>
     * <ol>
     * <li>设置不分页查询</li>
     * <li>查询所有符合条件的供应商信息</li>
     * <li>将数据导出为Excel文件</li>
     * </ol>
     *
     * @param pageReqVO 查询条件
     * @param response  HTTP响应对象
     * @throws IOException 导出过程中的IO异常
     */
    @GetMapping("/export-excel")
    @Operation(summary = "导出供应商信息 Excel")
    @PreAuthorize("@ss.hasPermission('saas:purchase:supplier:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportPurchaseSupplierExcel(@Valid SupplierPageReqVO pageReqVO,
            HttpServletResponse response) throws IOException {
        // 1. 设置不分页
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        // 2. 查询数据
        SupplierPageReqDTO pageReqDTO = SupplierConvert.INSTANCE.convert2DTO(pageReqVO);
        List<SupplierDTO> list = supplierService.getSupplierPage(pageReqDTO).getList();
        // 3. 导出 Excel
        ExcelUtils.write(response, "供应商信息.xls", "数据", SupplierRespVO.class,
                SupplierConvert.INSTANCE.convert2VOList(list));
    }

}