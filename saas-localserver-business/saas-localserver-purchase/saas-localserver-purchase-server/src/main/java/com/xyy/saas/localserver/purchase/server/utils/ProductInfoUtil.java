package com.xyy.saas.localserver.purchase.server.utils;

import com.xyy.saas.inquiry.product.api.product.dto.ProductInfoDto;
import com.xyy.saas.localserver.entity.mock.MockProductUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 商品信息工具类
 */
@Slf4j
@Component
//public class ProductInfoUtil implements ApplicationContextAware {
public class ProductInfoUtil {

//    private static ProductInfoService productInfoService;
//
//    @Override
//    public void setApplicationContext(ApplicationContext applicationContext) {
//        productInfoService = applicationContext.getBean(ProductInfoService.class);
//    }

    /**
     * 填充商品信息到详情列表
     * 
     * @param details           详情列表
     * @param productPrefGetter 获取商品编码的方法
     * @param productInfoSetter 设置商品信息的方法
     * @param <T>               详情类型
     */
    public static <T> void fillProductInfo(List<T> details, Function<T, String> productPrefGetter,
            BiConsumer<T, ProductInfoDto> productInfoSetter) {
        try {
            if (CollectionUtils.isEmpty(details)) {
                log.debug("详情列表为空，跳过商品信息填充");
                return;
            }

            // 批量获取商品信息
            List<String> productPrefs = details.stream()
                    .map(productPrefGetter)
                    .filter(StringUtils::isNotBlank)
                    .toList();

            if (CollectionUtils.isEmpty(productPrefs)) {
                log.warn("未找到有效的商品编码，跳过商品信息填充");
                return;
            }

//            List<ProductInfoDto> productInfos = productInfoService.listProductInfoByPref(productPrefs);
            List<ProductInfoDto> productInfos = MockProductUtil.listProductInfoByPref(productPrefs);
            Map<String, ProductInfoDto> productMap = productInfos.stream()
                    .collect(Collectors.toMap(ProductInfoDto::getPref, o -> o, (o1, o2) -> o1));

            // 设置商品信息到详情中
            details.forEach(detail -> {
                String pref = productPrefGetter.apply(detail);
                if (StringUtils.isNotBlank(pref)) {
                    productInfoSetter.accept(detail, productMap.get(pref));
                }
            });

            log.debug("成功填充商品信息");
        } catch (Exception e) {
            log.error("填充商品信息失败", e);
        }
    }
}