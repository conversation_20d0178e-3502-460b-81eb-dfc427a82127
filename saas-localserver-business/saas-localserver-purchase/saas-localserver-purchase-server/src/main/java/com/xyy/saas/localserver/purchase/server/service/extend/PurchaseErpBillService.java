package com.xyy.saas.localserver.purchase.server.service.extend;

import com.xyy.saas.localserver.purchase.api.extend.dto.PurchaseErpBillDTO;
import com.xyy.saas.localserver.purchase.api.extend.dto.PurchaseErpBillPageReqDTO;
import com.xyy.saas.localserver.purchase.server.dal.dataobject.extend.PurchaseErpBillDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;

/**
 * 采购-三方erp单据信息 Service 接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface PurchaseErpBillService {

    /**
     * 创建采购-三方erp单据信息
     *
     * @param createDTO 创建信息
     * @return 创建结果
     */
    Long createPurchaseErpBill(PurchaseErpBillDTO createDTO);

    /**
     * 更新采购-三方erp单据信息
     *
     * @param updateDTO 更新信息
     */
    void updatePurchaseErpBill(PurchaseErpBillDTO updateDTO);

    /**
     * 删除采购-三方erp单据信息
     *
     * @param id 编号
     */
    void deletePurchaseErpBill(Long id);

    /**
     * 获得采购-三方erp单据信息
     *
     * @param id 编号
     * @return 采购-三方erp单据信息
     */
    PurchaseErpBillDO getPurchaseErpBill(Long id);

    /**
     * 获得采购-三方erp单据信息分页
     *
     * @param pageReqDTO 分页查询参数
     * @return 采购-三方erp单据信息分页
     */
    PageResult<PurchaseErpBillDTO> getPurchaseErpBillPage(PurchaseErpBillPageReqDTO pageReqDTO);
}