package com.xyy.saas.localserver.purchase.server.convert.supplier;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.localserver.purchase.api.supplier.dto.TenantSupplierRelationDTO;
import com.xyy.saas.localserver.purchase.api.supplier.dto.TenantSupplierRelationPageReqDTO;
import com.xyy.saas.localserver.purchase.server.admin.supplier.vo.*;
import com.xyy.saas.localserver.purchase.server.dal.dataobject.supplier.TenantSupplierRelationDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 租户供应商关系转换器
 * <p>
 * 主要功能：
 * 1. 基础转换 - VO/DO/DTO之间的转换
 */
@Mapper(uses = { SupplierConvert.class })
public interface TenantSupplierRelationConvert {

    TenantSupplierRelationConvert INSTANCE = Mappers.getMapper(TenantSupplierRelationConvert.class);

    // ========== 1. 基础转换方法 ==========

    /**
     * DTO 转 DO
     * 
     * @param tenantSupplierRelationDTO 租户供应商关系DTO
     * @return 租户供应商关系DO
     */
    TenantSupplierRelationDO convert2DO(TenantSupplierRelationDTO tenantSupplierRelationDTO);

    /**
     * DO 转 DTO
     * 
     * @param tenantSupplierRelation 租户供应商关系DO
     * @return 租户供应商关系DTO
     */
    TenantSupplierRelationDTO convert2DTO(TenantSupplierRelationDO tenantSupplierRelation);

    /**
     * DO 转 DTO
     * 
     * @param pageResult 租户供应商关系DO分页结果
     * @return 租户供应商关系DTO分页结果
     */
    PageResult<TenantSupplierRelationDTO> convert2DTO(PageResult<TenantSupplierRelationDO> pageResult);

    /**
     * VO 转 DTO
     * 
     * @param saveReqVO 租户供应商关系保存请求VO
     * @return 租户供应商关系DTO
     */
    TenantSupplierRelationDTO convert2DTO(TenantSupplierRelationSaveReqVO saveReqVO);

    /**
     * VO 转 DTO
     * 
     * @param pageReqVO 租户供应商关系分页查询VO
     * @return 租户供应商关系分页查询DTO
     */
    TenantSupplierRelationPageReqDTO convert2DTO(TenantSupplierRelationPageReqVO pageReqVO);

    /**
     * DTO 转 VO
     * 
     * @param tenantSupplierRelationDTO 租户供应商关系DTO
     * @return 租户供应商关系响应VO
     */
    TenantSupplierRelationRespVO convert2VO(TenantSupplierRelationDTO tenantSupplierRelationDTO);

    /**
     * DTO 转 VO
     * 
     * @param pageResultDTO 租户供应商关系DTO分页结果
     * @return 租户供应商关系响应VO分页结果
     */
    PageResult<TenantSupplierRelationRespVO> convert2VO(PageResult<TenantSupplierRelationDTO> pageResultDTO);

    /**
     * DTO列表 转 VO列表
     * 
     * @param list 租户供应商关系DTO列表
     * @return 租户供应商关系响应VO列表
     */
    List<TenantSupplierRelationRespVO> convert2VOList(List<TenantSupplierRelationDTO> list);
}
