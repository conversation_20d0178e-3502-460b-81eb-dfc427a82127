package com.xyy.saas.localserver.purchase.server.convert.inventory;

import com.xyy.saas.localserver.inventory.api.stock.dto.InventoryIncreaseDTO;
import com.xyy.saas.localserver.inventory.api.stock.dto.InventorySwapDTO;
import com.xyy.saas.localserver.inventory.enums.InventorySelectEnum;
import com.xyy.saas.localserver.purchase.api.purchase.dto.PurchaseBillDTO;
import com.xyy.saas.localserver.purchase.api.receive.dto.ReceiveBillDTO;
import com.xyy.saas.localserver.purchase.enums.purchase.PurchaseBillTypeEnum;
import com.xyy.saas.localserver.purchase.enums.receive.ReceiveBillTypeEnum;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.EnumMap;

/**
 * 库存增加转换器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface InventoryIncreaseConvert {

    InventoryIncreaseConvert INSTANCE = Mappers.getMapper(InventoryIncreaseConvert.class);

    /**
     * 构建库存增加DTO
     *
     * @param receiveBill 收货单
     * @return 库存增加DTO
     */
    default InventoryIncreaseDTO buildInventoryIncreaseDTO(ReceiveBillDTO receiveBill) {
        return InventoryIncreaseDTO.builder()
                .billType(ReceiveBillTypeEnum.fromCode(receiveBill.getBillType()).getBillType().billType)
                .billNo(receiveBill.getBillNo())
                .tenantId(receiveBill.getTenantId())
                .inTime(receiveBill.getWarehouseTime())
                .selectMap(new EnumMap<>(InventorySelectEnum.class))
                .build();
    }

    /**
     * 构建库存一入一出DTO
     *
     * @param distributionBill 配送单
     * @param receiveBill      收货单
     * @return 库存一入一出DTO
     */
    default InventorySwapDTO buildInventorySwapDTO(PurchaseBillDTO distributionBill, ReceiveBillDTO receiveBill) {
        return InventorySwapDTO.builder()
                .tenantId(distributionBill.getTenantId())
                .inBillType(ReceiveBillTypeEnum.fromCode(receiveBill.getBillType()).getBillType().billType)
                .inBillNo(receiveBill.getBillNo())
                .inTime(receiveBill.getWarehouseTime())
                .outBillType(PurchaseBillTypeEnum.fromCode(distributionBill.getBillType()).getBillType().billType)
                .outBillNo(distributionBill.getPurchaseBillNo())
                .outTime(distributionBill.getPurchaseTime())
                .selectMap(new EnumMap<>(InventorySelectEnum.class))
                .build();
    }
}