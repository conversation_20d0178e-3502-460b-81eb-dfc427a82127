package com.xyy.saas.localserver.purchase.server.convert.purchase;

import com.xyy.saas.localserver.purchase.api.extend.dto.ExtDTO;
import com.xyy.saas.localserver.purchase.api.purchase.dto.PurchaseBillDetailDTO;
import com.xyy.saas.localserver.purchase.server.admin.purchase.vo.PurchaseBillDetailSaveReqVO;
import com.xyy.saas.localserver.purchase.server.convert.extend.ExtConvert;
import com.xyy.saas.localserver.purchase.server.dal.dataobject.purchase.PurchaseBillDetailDO;
import com.xyy.saas.localserver.purchase.server.utils.ProductInfoUtil;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.Optional;

/**
 * 采购单明细转换器
 * <p>
 * 主要功能：
 * 1. 基础转换 - VO/DO/DTO之间的转换
 * 2. 商品信息填充 - 处理商品相关信息的填充
 */
@Mapper(uses = { ExtConvert.class })
public interface PurchaseBillDetailConvert {

    PurchaseBillDetailConvert INSTANCE = Mappers.getMapper(PurchaseBillDetailConvert.class);

    // ========== 1. 基础转换方法 ==========

    /**
     * 将详情DTO转换为DO
     *
     * @param purchaseBillDetailDTO 采购单明细DTO
     * @return 采购单明细DO
     */
    @Mapping(target = "ext", source = "ext")
    PurchaseBillDetailDO convert2DO(PurchaseBillDetailDTO purchaseBillDetailDTO);

    /**
     * 将详情DTO列表转换为DO列表
     *
     * @param purchaseBillDetailDTOs 采购单明细DTO列表
     * @return 采购单明细DO列表
     */
    @Mapping(target = "ext", source = "ext")
    List<PurchaseBillDetailDO> convert2DOList(List<PurchaseBillDetailDTO> purchaseBillDetailDTOs);

    /**
     * 将详情VO转换为DTO
     * 
     * @param saveReqVO 采购单明细保存请求VO
     * @return 采购单明细DTO
     */
    @Mapping(target = "ext", source = "ext")
    PurchaseBillDetailDTO convert2DTO(PurchaseBillDetailSaveReqVO saveReqVO);

    // ========== 2. 商品信息填充方法 ==========

    /**
     * 填充商品信息
     * 
     * @param details 详情列表
     */
    default void fillProductInfo(List<PurchaseBillDetailDTO> details) {
        ProductInfoUtil.fillProductInfo(
                details,
                PurchaseBillDetailDTO::getProductPref,
                (detail, productInfo) -> Optional.ofNullable(detail.getExt())
                        .or(() -> Optional.of(new ExtDTO()).map(e -> {
                            detail.setExt(e);
                            return e;
                        }))
                        .ifPresent(ext -> ext.setProductInfo(productInfo)));
    }
}