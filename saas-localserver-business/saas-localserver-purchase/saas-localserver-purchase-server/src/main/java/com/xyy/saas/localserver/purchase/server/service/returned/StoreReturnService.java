package com.xyy.saas.localserver.purchase.server.service.returned;

import com.xyy.saas.localserver.purchase.api.returned.dto.ReturnBillDTO;
import com.xyy.saas.localserver.purchase.api.returned.dto.ReturnBillPageReqDTO;
import jakarta.validation.*;
import cn.iocoder.yudao.framework.common.pojo.PageResult;

/**
 * 门店退货单 Service 接口
 *
 * <p>
 * 该接口定义了门店退货单相关的核心业务操作，包括：
 * </p>
 * <ul>
 * <li>基础操作：保存、删除、查询等</li>
 * <li>状态管理：撤销、复核、出库等</li>
 * <li>单据管理：分页查询等</li>
 * </ul>
 *
 * <p>
 * 主要特点：
 * </p>
 * <ul>
 * <li>支持门店退货的业务场景</li>
 * <li>支持库存预占和释放机制</li>
 * <li>支持完整的单据生命周期管理</li>
 * </ul>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface StoreReturnService {

    /**
     * 保存门店退货单
     * 处理流程：
     * 1. 填充商品信息
     * 2. 填充退货单内容
     * 3. 保存门店退货单
     * 4. 处理提交状态：
     * 4.1 预占库存和追溯码
     * 4.2 发起审批流
     *
     * @param returnBill 退货单信息
     * @return 退货单编号
     */
    Long saveStoreReturnBill(@Valid ReturnBillDTO returnBill);

    /**
     * 撤销门店退货单
     * 处理流程：
     * 1. 校验原单据状态
     * 2. 更新单据状态
     * 3. 释放库存预占
     *
     * @param returnBill 退货单信息
     */
    void revokeStoreReturn(ReturnBillDTO returnBill);

    /**
     * 复核并出库
     * 处理流程：
     * 1. 修改退货单状态为已出库
     * 2. 释放预占并扣减库存
     * 3. 生成总部收货对象
     * 4. 调用云端总部收货接口
     *
     * @param returnBill 退货单信息
     */
    void checkAndOutbound(@Valid ReturnBillDTO returnBill);

    /**
     * 删除门店退货单
     * 处理流程：
     * 1. 校验单据是否存在
     * 2. 删除单据信息
     *
     * @param id 退货单编号
     */
    void deleteStoreReturnBill(Long id);

    /**
     * 获取门店退货单信息
     * 处理流程：
     * 1. 根据ID查询单据
     * 2. 校验单据是否存在
     * 3. 转换并返回结果
     *
     * @param id 退货单编号
     * @return 退货单信息
     */
    ReturnBillDTO getStoreReturnBill(Long id);

    /**
     * 获取门店退货单分页信息
     * 处理流程：
     * 1. 根据查询条件获取分页数据
     * 2. 转换并返回结果
     *
     * @param pageReqDTO 分页查询条件
     * @return 分页结果
     */
    PageResult<ReturnBillDTO> getStoreReturnBillPage(ReturnBillPageReqDTO pageReqDTO);

    /**
     * 根据单号和租户ID获取门店退货单信息（包含明细）
     * 处理流程：
     * 1. 调用基础服务获取退货单信息
     *
     * @param billNo   单据编号
     * @param tenantId 租户编号
     * @return 退货单信息
     */
    ReturnBillDTO getStoreReturnBillWithDetails(String billNo, Long tenantId);
}