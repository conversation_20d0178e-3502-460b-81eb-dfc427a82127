package com.xyy.saas.localserver.purchase.server.service.extend;

import com.xyy.saas.localserver.purchase.api.extend.dto.PurchaseTransportDTO;
import com.xyy.saas.localserver.purchase.api.extend.dto.PurchaseTransportPageReqDTO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;

/**
 * 采购-运输信息 Service 接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface PurchaseTransportService {

    /**
     * 创建采购-运输信息
     *
     * @param createDTO 创建信息
     * @return 创建结果
     */
    Long createPurchaseTransport(PurchaseTransportDTO createDTO);

    /**
     * 更新采购-运输信息
     *
     * @param updateDTO 更新信息
     */
    void updatePurchaseTransport(PurchaseTransportDTO updateDTO);

    /**
     * 删除采购-运输信息
     *
     * @param id 编号
     */
    void deletePurchaseTransport(Long id);

    /**
     * 获得采购-运输信息
     *
     * @param id 编号
     * @return 采购-运输信息
     */
    PurchaseTransportDTO getPurchaseTransport(Long id);

    /**
     * 获得采购-运输信息分页
     *
     * @param pageReqDTO 分页查询参数
     * @return 采购-运输信息分页
     */
    PageResult<PurchaseTransportDTO> getPurchaseTransportPage(PurchaseTransportPageReqDTO pageReqDTO);

}