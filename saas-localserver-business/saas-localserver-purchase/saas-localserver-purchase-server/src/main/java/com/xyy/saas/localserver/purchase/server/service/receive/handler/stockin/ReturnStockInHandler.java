package com.xyy.saas.localserver.purchase.server.service.receive.handler.stockin;

import com.xyy.saas.localserver.entity.enums.tenant.TenantTypeEnum;
import com.xyy.saas.localserver.inventory.api.change.dto.InventoryChangeDetailDTO;
import com.xyy.saas.localserver.inventory.api.stock.dto.InventorySelectItemDTO;
import com.xyy.saas.localserver.inventory.enums.InventorySelectEnum;
import com.xyy.saas.localserver.purchase.api.receive.dto.ReceiveBillDTO;
import com.xyy.saas.localserver.purchase.api.receive.dto.ReceiveBillDetailDTO;
import com.xyy.saas.localserver.purchase.enums.receive.ReceiveBillTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 退货库存入库处理器
 * 
 * <p>
 * 该处理器专门处理退货类型的库存入库业务。
 * 主要职责包括：
 * </p>
 * <ul>
 * <li>处理退货单的库存入库逻辑</li>
 * <li>支持总部和门店两种租户类型</li>
 * <li>实现先进先出(FIFO)的库存分配策略</li>
 * </ul>
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
public class ReturnStockInHandler extends AbstractStockInHandler {

        // ========== 常量定义 ==========

        /** 支持的收货单类型 */
        private static final Set<ReceiveBillTypeEnum> SUPPORTED_BILL_TYPES = Set.of(ReceiveBillTypeEnum.RETURN_RECEIVE);

        /** 支持的租户类型 */
        private static final Set<TenantTypeEnum> SUPPORTED_TENANT_TYPES = Set.of(
                        TenantTypeEnum.CHAIN_HEADQUARTERS,
                        TenantTypeEnum.CHAIN_STORE);

        // ========== 公共方法 ==========

        /**
         * 获取支持的收货单类型
         * 仅支持退货收货单类型
         *
         * @return 支持的收货单类型集合
         */
        @Override
        public Set<ReceiveBillTypeEnum> getSupportedBillTypes() {
                return SUPPORTED_BILL_TYPES;
        }

        /**
         * 获取支持的租户类型
         * 支持总部和门店两种租户类型
         *
         * @return 支持的租户类型集合
         */
        @Override
        public Set<TenantTypeEnum> getSupportedTenantTypes() {
                return SUPPORTED_TENANT_TYPES;
        }

        /**
         * 根据入库单处理库存入库数据
         * 处理流程：
         * 1. 查询库存变更明细
         * 2. 按商品编码和批号分组
         * 3. 处理每个收货明细
         *
         * @param receiveBill 收货单信息
         * @param selectMap   库存选择项Map
         */
        @Override
        protected void processStockInItemsByReceiveBill(ReceiveBillDTO receiveBill,
                        Map<InventorySelectEnum, List<InventorySelectItemDTO>> selectMap) {
                if (receiveBill == null || CollectionUtils.isEmpty(receiveBill.getDetails())) {
                        log.warn("收货单或明细为空，跳过处理 - 单据号: {}", receiveBill != null ? receiveBill.getBillNo() : null);
                        return;
                }

                String billNo = receiveBill.getBillNo();
                log.info("开始处理退货库存入库 - 单据号: {}", billNo);

                // 1. 查询库存变更明细
                List<InventoryChangeDetailDTO> changeDetails = queryInventoryChangeDetails(
                                DataSource.LOCAL,
                                billNo,
                                receiveBill.getTenantId());

                if (CollectionUtils.isEmpty(changeDetails)) {
                        log.warn("未找到库存变更明细，跳过处理 - 单据号: {}", billNo);
                        return;
                }

                // 2. 按商品编码和批号分组
                Map<String, List<InventoryChangeDetailDTO>> groupedDetails = groupChangeDetailsByProductAndLot(
                                changeDetails);

                // 3. 根据源单据批次按先进先出原则分配入库批次
                for (ReceiveBillDetailDTO detail : receiveBill.getDetails()) {
                        boolean isHeadquarters = TenantTypeEnum.CHAIN_HEADQUARTERS.getCode() == receiveBill
                                        .getTenantType();
                        allocateStockInItemsBySourceFIFO(receiveBill, detail, groupedDetails, selectMap, !isHeadquarters);
                }

                log.info("退货库存入库处理完成 - 单据号: {}", billNo);
        }

}