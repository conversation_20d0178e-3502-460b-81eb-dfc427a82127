package com.xyy.saas.localserver.purchase.server.service.returned.impl;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import com.xyy.saas.localserver.inventory.api.campon.InventoryCampOnApi;
import com.xyy.saas.localserver.inventory.api.campon.dto.InventoryCampOnDTO;
import com.xyy.saas.localserver.purchase.api.returned.dto.ReturnBillDTO;
import com.xyy.saas.localserver.purchase.api.returned.dto.ReturnBillDetailDTO;
import com.xyy.saas.localserver.purchase.api.returned.dto.ReturnBillPageReqDTO;
import com.xyy.saas.localserver.purchase.enums.returned.ReturnBillStatusEnum;
import com.xyy.saas.localserver.purchase.server.convert.inventory.InventoryCampOnConvert;
import com.xyy.saas.localserver.purchase.server.convert.returned.ReturnBillConvert;
import com.xyy.saas.localserver.purchase.server.convert.returned.ReturnBillDetailConvert;
import com.xyy.saas.localserver.purchase.server.service.returned.PurchaseReturnService;
import com.xyy.saas.localserver.purchase.server.service.returned.ReturnBillService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.util.*;
import java.util.stream.Collectors;
import static com.xyy.saas.localserver.purchase.enums.returned.ReturnBillStatusEnum.*;

/**
 * 采购退货单 Service 实现类
 *
 * <p>
 * 该实现类主要处理采购退货相关的业务逻辑，包括：
 * </p>
 * <ul>
 * <li>退货单的保存和撤销</li>
 * <li>退货单的复核和出库</li>
 * <li>退货单的删除和查询</li>
 * <li>单据拆分（按供应商）</li>
 * </ul>
 *
 * <p>
 * 主要特点：
 * </p>
 * <ul>
 * <li>支持采购退货的业务场景</li>
 * <li>支持按供应商拆分单据</li>
 * <li>支持库存预占和释放机制</li>
 * <li>支持完整的单据生命周期管理</li>
 * </ul>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
public class PurchaseReturnServiceImpl implements PurchaseReturnService {

    @Resource
    private ReturnBillService returnBillService;

    @Resource
    private InventoryCampOnApi inventoryCampOnApi;

    /**
     * 保存采购退货单
     * 处理流程：
     * 1. 填充商品信息
     * 2. 拆分单据（按供应商）
     * 3. 填充退货单内容
     * 4. 保存退货单
     * 5. 处理提交状态：
     * 5.1 预占库存和追溯码
     * 5.2 发起审批流
     *
     * @param returnBill 退货单信息
     * @return 退货单编号
     */
    @Override
    public Long savePurchaseReturnBill(ReturnBillDTO returnBill) {
        // 1. 填充商品信息
        ReturnBillDetailConvert.INSTANCE.fillProductInfo(returnBill.getDetails());

        // 2. 拆单
        List<ReturnBillDTO> returnBills = splitBill(returnBill);

        // 3. 填充退货单内容
        returnBills.forEach(ReturnBillConvert.INSTANCE::fillReturnContent);

        // 4. 保存门店退货单
        returnBillService.saveReturnBills(returnBills);

        // 5. 如果提交&待审批状态，记录本地消息&发起审批流
        if (returnBill.getSubmitted()
                && Objects.equals(ReturnBillStatusEnum.fromCode(returnBill.getStatus()), PENDING_APPROVAL)) {
            // 5.1 预占库存&追溯码
            List<InventoryCampOnDTO> inventoryCampOnDTOs = returnBills.stream()
                    .map(InventoryCampOnConvert.INSTANCE::convert2InventoryCampOn)
                    .toList();
            inventoryCampOnApi.batchCampOn(inventoryCampOnDTOs);

            // TODO: 发起审批流
        }

        return returnBill.getId();
    }

    /**
     * 撤销采购退货单
     * 处理流程：
     * 1. 调用基础服务撤销或拒绝退货单
     *
     * @param returnBill 退货单信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void revokePurchaseReturn(ReturnBillDTO returnBill) {
        // 调用基础服务撤销或拒绝退货单
        returnBillService.revokeOrRejectPurchaseReturn(returnBill);
    }

    /**
     * 复核并出库
     * 处理流程：
     * 1. 调用基础服务执行复核出库
     *
     * @param returnBill 退货单信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void checkAndOutbound(ReturnBillDTO returnBill) {
        // 调用基础服务执行复核出库
        returnBillService.checkAndOutbound(returnBill,
                InventoryCampOnConvert.INSTANCE.convert2ReleaseCampOnDto(returnBill));
    }

    /**
     * 删除采购退货单
     * 处理流程：
     * 1. 调用基础服务删除退货单
     *
     * @param id 退货单编号
     */
    @Override
    public void deletePurchaseReturnBill(Long id) {
        // 调用基础服务删除退货单
        returnBillService.deleteReturnBill(id);
    }

    /**
     * 获取采购退货单信息
     * 处理流程：
     * 1. 调用基础服务获取退货单信息
     *
     * @param id 退货单编号
     * @return 退货单信息
     */
    @Override
    public ReturnBillDTO getPurchaseReturnBill(Long id) {
        // 调用基础服务获取退货单信息
        return returnBillService.getReturnBill(id);
    }

    /**
     * 获取采购退货单分页信息
     * 处理流程：
     * 1. 调用基础服务获取分页数据
     *
     * @param pageReqDTO 分页查询条件
     * @return 分页结果
     */
    @Override
    public PageResult<ReturnBillDTO> getPurchaseReturnBillPage(ReturnBillPageReqDTO pageReqDTO) {
        // 调用基础服务获取分页数据
        return returnBillService.getReturnBillPage(pageReqDTO);
    }

    /**
     * 根据单号和租户ID获取采购退货单信息（包含明细）
     * 处理流程：
     * 1. 调用基础服务获取退货单信息
     *
     * @param billNo   单据编号
     * @param tenantId 租户编号
     * @return 退货单信息
     */
    @Override
    public ReturnBillDTO getPurchaseReturnBillWithDetails(String billNo, Long tenantId) {
        // 调用基础服务获取退货单信息
        return returnBillService.getReturnBillWithDetails(billNo, tenantId);
    }

    /**
     * 拆分退货单
     * 处理流程：
     * 1. 检查是否需要拆分（未提交或单一供应商不需要拆分）
     * 2. 按供应商分组明细
     * 3. 为每个供应商创建新的退货单
     *
     * @param returnBill 原始退货单
     * @return 拆分后的退货单列表
     */
    private List<ReturnBillDTO> splitBill(ReturnBillDTO returnBill) {
        // 1. 如果单据未提交，则不需要拆单
        if (!returnBill.getSubmitted()) {
            return List.of(returnBill);
        }

        // 2. 按供应商分组
        Map<String, List<ReturnBillDetailDTO>> supplierDetailsMap = returnBill.getDetails().stream()
                .collect(Collectors.groupingBy(ReturnBillDetailDTO::getSupplierGuid));

        // 3. 如果只有一个供应商，则不需要拆单
        if (supplierDetailsMap.keySet().size() <= 1) {
            return List.of(returnBill);
        }

        // 4. 为每个供应商创建新的采购单
        return supplierDetailsMap.values().stream()
                .map(details -> createSplitBill(returnBill, details))
                .toList();
    }

    /**
     * 创建拆分后的退货单
     * 处理流程：
     * 1. 复制原始单据基本信息
     * 2. 重置单号
     * 3. 设置新的明细
     * 4. 设置供应商信息
     *
     * @param originalBill 原始退货单
     * @param details      新的明细列表
     * @return 拆分后的退货单
     */
    private ReturnBillDTO createSplitBill(ReturnBillDTO originalBill, List<ReturnBillDetailDTO> details) {
        // 1. 复制原始单据基本信息
        ReturnBillDTO newBill = new ReturnBillDTO();
        BeanUtils.copyProperties(originalBill, newBill);

        // 2. 重置单号
        newBill.setPurchaseBillNo(null);

        // 3. 设置新的明细
        newBill.setDetails(details);

        // 4. 设置供应商信息
        ReturnBillDetailDTO first = details.getFirst();
        newBill.setSupplierGuid(first.getSupplierGuid());
        newBill.setSupplierName(first.getSupplierName());
        return newBill;
    }
}