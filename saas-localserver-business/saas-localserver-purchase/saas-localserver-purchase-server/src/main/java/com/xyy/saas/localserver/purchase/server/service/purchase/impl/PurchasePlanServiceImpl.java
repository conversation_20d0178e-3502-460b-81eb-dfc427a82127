package com.xyy.saas.localserver.purchase.server.service.purchase.impl;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.localserver.purchase.api.purchase.dto.PurchaseBillDTO;
import com.xyy.saas.localserver.purchase.api.purchase.dto.PurchaseBillPageReqDTO;
import com.xyy.saas.localserver.purchase.server.convert.purchase.PurchaseBillConvert;
import com.xyy.saas.localserver.purchase.server.convert.purchase.PurchaseBillDetailConvert;
import com.xyy.saas.localserver.purchase.server.service.purchase.PurchaseBillService;
import com.xyy.saas.localserver.purchase.server.service.purchase.PurchasePlanService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;

/**
 * 采购计划单 Service 实现类
 * <p>
 * 主要功能：
 * 1. 采购计划单基础操作（保存、删除、查询）
 * 2. 采购计划单分页查询
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
@Slf4j
public class PurchasePlanServiceImpl implements PurchasePlanService {

    // ========== 依赖注入 ==========
    @Resource
    private PurchaseBillService purchaseBillService;

    // ========== 1. 基础操作 ==========

    /**
     * 保存采购计划单
     *
     * @param saveDTO 待保存的采购计划单信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void savePurchasePlan(PurchaseBillDTO saveDTO) {
        // 1. 填充商品信息
        PurchaseBillDetailConvert.INSTANCE.fillProductInfo(saveDTO.getDetails());

        // 2. 拆单
        List<PurchaseBillDTO> purchaseBills = PurchaseBillConvert.INSTANCE.splitBill(saveDTO);

        // 3. 填充采购内容
        purchaseBills.forEach(PurchaseBillConvert.INSTANCE::fillPurchaseContent);

        // 4. 保存单据信息
        purchaseBillService.savePurchaseBills(purchaseBills);
    }

    /**
     * 删除采购计划单
     *
     * @param id 采购计划单编号
     */
    @Override
    public void deletePurchasePlan(Long id) {
        purchaseBillService.deletePurchaseBill(id);
    }

    // ========== 2. 查询操作 ==========

    /**
     * 获得采购计划单
     *
     * @param id 采购计划单编号
     * @return 采购计划单信息
     */
    @Override
    public PurchaseBillDTO getPurchasePlan(Long id) {
        return purchaseBillService.getPurchaseBill(id);
    }

    /**
     * 获得采购计划单和详情
     *
     * @param billNo   采购计划单号
     * @param tenantId 租户ID
     * @return 采购计划单信息（包含详情）
     */
    @Override
    public PurchaseBillDTO getPurchasePlanWithDetails(String billNo, Long tenantId) {
        return purchaseBillService.getPurchaseBillWithDetails(billNo, tenantId);
    }

    /**
     * 获得采购计划单分页
     *
     * @param pageReqDTO 分页查询参数
     * @return 采购计划单分页结果
     */
    @Override
    public PageResult<PurchaseBillDTO> getPurchasePlanPage(PurchaseBillPageReqDTO pageReqDTO) {
        return purchaseBillService.getPurchaseBillPage(pageReqDTO);
    }
}