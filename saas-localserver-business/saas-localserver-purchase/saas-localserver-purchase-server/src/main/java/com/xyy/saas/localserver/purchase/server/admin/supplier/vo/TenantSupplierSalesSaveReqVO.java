package com.xyy.saas.localserver.purchase.server.admin.supplier.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import jakarta.validation.constraints.*;

import java.time.LocalDateTime;

/**
 * 管理后台 - 租户-供应商-销售人员信息新增/修改 Request VO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "管理后台 - 租户-供应商-销售人员信息新增/修改 Request VO")
@Data
public class TenantSupplierSalesSaveReqVO {

    /** 主键ID */
    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "19789")
    private Long id;

    /** 供应商编号 */
    @Schema(description = "供应商编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "供应商编号不能为空")
    private String supplierGuid;

    /** 销售人员姓名 */
    @Schema(description = "销售人员姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @NotEmpty(message = "销售人员姓名不能为空")
    private String salesName;

    /** 授权区域 */
    @Schema(description = "授权区域", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "授权区域不能为空")
    private String authorizedArea;

    /** 授权书号 */
    @Schema(description = "授权书号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "授权书号不能为空")
    private String authorizationNum;

    /** 授权书号有效期 */
    @Schema(description = "授权书号有效期")
    private LocalDateTime authorizationNumExpirationDate;

    /** 手机号码 */
    @Schema(description = "手机号码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "手机号码不能为空")
    private String phoneNumber;

    /** 授权信息 */
    @Schema(description = "授权信息", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "授权信息不能为空")
    private String authorizedVarieties;

    /** 身份证号 */
    @Schema(description = "身份证号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "身份证号不能为空")
    private String idCard;

    /** 身份证有效期 */
    @Schema(description = "身份证有效期")
    private LocalDateTime idCardExpirationDate;

    /** 身份证附件 */
    @Schema(description = "身份证附件", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "身份证附件不能为空")
    private String idCardAttachment;

    /** 授权书附件 */
    @Schema(description = "授权书附件", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "授权书附件不能为空")
    private String authorizationAttachment;

    /** 经营范围 */
    @Schema(description = "经营范围", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "经营范围不能为空")
    private String authorizedScope;

}