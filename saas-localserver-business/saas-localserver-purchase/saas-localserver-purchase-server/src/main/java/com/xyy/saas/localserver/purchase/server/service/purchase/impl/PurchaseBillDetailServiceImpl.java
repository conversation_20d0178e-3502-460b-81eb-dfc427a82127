//package com.xyy.saas.localserver.purchase.server.service.purchase.impl;
//
//import com.xyy.saas.localserver.purchase.server.admin.purchase.vo.PurchaseBillDetailPageReqVO;
//import com.xyy.saas.localserver.purchase.server.admin.purchase.vo.PurchaseBillDetailSaveReqVO;
//import com.xyy.saas.localserver.purchase.server.dal.dataobject.purchase.PurchaseBillDetailDO;
//import com.xyy.saas.localserver.purchase.server.dal.mysql.purchase.PurchaseBillDetailMapper;
//import com.xyy.saas.localserver.purchase.server.service.purchase.PurchaseBillDetailService;
//import org.springframework.stereotype.Service;
//import jakarta.annotation.Resource;
//import org.springframework.validation.annotation.Validated;
//import cn.iocoder.yudao.framework.common.pojo.PageResult;
//import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
//import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
//import static com.xyy.saas.localserver.purchase.enums.ErrorCodeConstants.*;
//
///**
// * 采购明细 Service 实现类
// *
// * <AUTHOR>
// */
//@Service
//@Validated
//public class PurchaseBillDetailServiceImpl implements PurchaseBillDetailService {
//
//    @Resource
//    private PurchaseBillDetailMapper purchaseBillDetailMapper;
//
//    @Override
//    public Long createPurchaseBillDetail(PurchaseBillDetailSaveReqVO createReqVO) {
//        // 插入
//        PurchaseBillDetailDO purchaseBillDetail = BeanUtils.toBean(createReqVO, PurchaseBillDetailDO.class);
//        purchaseBillDetailMapper.insert(purchaseBillDetail);
//        // 返回
//        return purchaseBillDetail.getId();
//    }
//
//    @Override
//    public void updatePurchaseBillDetail(PurchaseBillDetailSaveReqVO updateReqVO) {
//        // 校验存在
//        validatePurchaseBillDetailExists(updateReqVO.getId());
//        // 更新
//        PurchaseBillDetailDO updateObj = BeanUtils.toBean(updateReqVO, PurchaseBillDetailDO.class);
//        purchaseBillDetailMapper.updateById(updateObj);
//    }
//
//    @Override
//    public void deletePurchaseBillDetail(Long id) {
//        // 校验存在
//        validatePurchaseBillDetailExists(id);
//        // 删除
//        purchaseBillDetailMapper.deleteById(id);
//    }
//
//    private void validatePurchaseBillDetailExists(Long id) {
//        if (purchaseBillDetailMapper.selectById(id) == null) {
//            throw exception(PURCHASE_BILL_DETAIL_NOT_EXISTS);
//        }
//    }
//
//    @Override
//    public PurchaseBillDetailDO getPurchaseBillDetail(Long id) {
//        return purchaseBillDetailMapper.selectById(id);
//    }
//
//    @Override
//    public PageResult<PurchaseBillDetailDO> getPurchaseBillDetailPage(PurchaseBillDetailPageReqVO pageReqVO) {
//        return purchaseBillDetailMapper.selectPage(pageReqVO);
//    }
//
//}