//package com.xyy.saas.localserver.purchase.server.service.returned.impl;
//
//import com.xyy.saas.localserver.purchase.server.admin.returned.vo.ReturnBillDetailPageReqVO;
//import com.xyy.saas.localserver.purchase.server.admin.returned.vo.ReturnBillDetailSaveReqVO;
//import com.xyy.saas.localserver.purchase.server.dal.dataobject.returned.ReturnBillDetailDO;
//import com.xyy.saas.localserver.purchase.server.dal.mysql.returned.ReturnBillDetailMapper;
//import com.xyy.saas.localserver.purchase.server.service.returned.ReturnBillDetailService;
//import org.springframework.stereotype.Service;
//import jakarta.annotation.Resource;
//import org.springframework.validation.annotation.Validated;
//
//import cn.iocoder.yudao.framework.common.pojo.PageResult;
//import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
//import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
//import static com.xyy.saas.localserver.purchase.enums.ErrorCodeConstants.*;
//
///**
// * （单体/总部/门店）退货单明细 Service 实现类
// *
// * <AUTHOR>
// */
//@Service
//@Validated
//public class ReturnBillDetailServiceImpl implements ReturnBillDetailService {
//
//    @Resource
//    private ReturnBillDetailMapper returnBillDetailMapper;
//
//    @Override
//    public Long createPurchaseReturnBillDetail(ReturnBillDetailSaveReqVO createReqVO) {
//        // 插入
//        ReturnBillDetailDO purchaseReturnBillDetail = BeanUtils.toBean(createReqVO, ReturnBillDetailDO.class);
//        returnBillDetailMapper.insert(purchaseReturnBillDetail);
//        // 返回
//        return purchaseReturnBillDetail.getId();
//    }
//
//    @Override
//    public void updatePurchaseReturnBillDetail(ReturnBillDetailSaveReqVO updateReqVO) {
//        // 校验存在
//        validatePurchaseReturnBillDetailExists(updateReqVO.getId());
//        // 更新
//        ReturnBillDetailDO updateObj = BeanUtils.toBean(updateReqVO, ReturnBillDetailDO.class);
//        returnBillDetailMapper.updateById(updateObj);
//    }
//
//    @Override
//    public void deletePurchaseReturnBillDetail(Long id) {
//        // 校验存在
//        validatePurchaseReturnBillDetailExists(id);
//        // 删除
//        returnBillDetailMapper.deleteById(id);
//    }
//
//    private void validatePurchaseReturnBillDetailExists(Long id) {
//        if (returnBillDetailMapper.selectById(id) == null) {
//            throw exception(RETURN_BILL_DETAIL_NOT_EXISTS);
//        }
//    }
//
//    @Override
//    public ReturnBillDetailDO getPurchaseReturnBillDetail(Long id) {
//        return returnBillDetailMapper.selectById(id);
//    }
//
//    @Override
//    public PageResult<ReturnBillDetailDO> getPurchaseReturnBillDetailPage(ReturnBillDetailPageReqVO pageReqVO) {
//        return returnBillDetailMapper.selectPage(pageReqVO);
//    }
//
//}