package com.xyy.saas.localserver.purchase.server.service.purchase.impl;

import cn.hutool.core.lang.Assert;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.localserver.inventory.api.campon.InventoryCampOnApi;
import com.xyy.saas.localserver.purchase.api.purchase.dto.PurchaseBillDTO;
import com.xyy.saas.localserver.purchase.api.purchase.dto.PurchaseBillPageReqDTO;
import com.xyy.saas.localserver.purchase.enums.returned.ReturnBillStatusEnum;
import com.xyy.saas.localserver.purchase.server.admin.purchase.vo.PurchaseBillRespVO;
import com.xyy.saas.localserver.purchase.server.convert.purchase.PurchaseBillConvert;
import com.xyy.saas.localserver.purchase.server.convert.purchase.PurchaseBillDetailConvert;
import com.xyy.saas.localserver.purchase.server.protocol.CloudServiceClient;
import com.xyy.saas.localserver.purchase.server.protocol.purchase.PurchaseClient;
import com.xyy.saas.localserver.purchase.server.service.purchase.PurchaseBillService;
import com.xyy.saas.localserver.purchase.server.service.purchase.StoreAllocationService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;
import java.util.Objects;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.localserver.purchase.enums.ErrorCodeConstants.ALLOCATION_BILL_STATUS_NOT_SUPPORT_REVOKE;

/**
 * 门店调拨单 Service 实现类
 * <p>
 * 主要功能：
 * 1. 门店调拨单基础操作（保存、删除、更新、查询）
 * 2. 门店调拨单状态管理
 * 3. 门店调拨单分页查询
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
@Slf4j
public class StoreAllocationServiceImpl implements StoreAllocationService {

    // ========== 依赖注入 ==========
    @Resource
    private PurchaseBillService purchaseBillService;

    @Resource
    private InventoryCampOnApi inventoryCampOnApi;

    @Resource
    private CloudServiceClient cloudServiceClient;

    // ========== 1. 基础操作 ==========

    /**
     * 保存门店调拨单
     *
     * @param saveDTO 待保存的门店调拨单信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveAllocationBill(PurchaseBillDTO saveDTO) {
        // 1. 填充商品信息
        PurchaseBillDetailConvert.INSTANCE.fillProductInfo(saveDTO.getDetails());

        // 2. 填充调剂内容
        PurchaseBillConvert.INSTANCE.fillPurchaseContent(saveDTO);

        // 3. 保存单据信息
        purchaseBillService.savePurchaseBills(List.of(saveDTO));

        // 4. TODO: 创建审批流（门店审批通过后再去预占门店库存，如果提交，处理后续逻辑）
        if (saveDTO.getSubmitted()) {
            // TODO: 处理审批流相关逻辑
        }
    }

    /**
     * 撤销门店调拨单
     *
     * @param revokeDTO 待撤销的门店调拨单信息
     */
    @Override
    public void revokeAllocation(PurchaseBillDTO revokeDTO) {
        // 1. 调用云端服务查询原单据
        CommonResult<PurchaseBillRespVO> result = cloudServiceClient
                .call(() -> cloudServiceClient.getClient(PurchaseClient.class).get(revokeDTO.getId()));
        Assert.state(result.isSuccess(), result.getMsg());

        // 2. 判断原单据状态
        PurchaseBillRespVO originalAllocationBill = result.getData();
        if (!Objects.equals(originalAllocationBill.getStatus(), ReturnBillStatusEnum.PENDING_APPROVAL.getCode())) {
            throw exception(ALLOCATION_BILL_STATUS_NOT_SUPPORT_REVOKE);
        }

        // 3. 更新本地单据状态
        revokeDTO.setVersion(originalAllocationBill.getVersion());
        purchaseBillService.updatePurchaseBill(revokeDTO);

        // 4. TODO: 调用云端库存接口释放预占
        // inventoryCampOnApi.releaseCampOn(InventorySimpleReleaseDTO.builder()
        // .sourceNo(originalAllocationBill.getPurchaseBillNo())
        // .tenantId(originalAllocationBill.getInboundTenantId())
        // .build());
    }

    /**
     * 删除门店调拨单
     *
     * @param id 门店调拨单编号
     */
    @Override
    public void deleteAllocationBill(Long id) {
        purchaseBillService.deletePurchaseBill(id);
    }

    // ========== 2. 查询操作 ==========

    /**
     * 获得门店调拨单
     *
     * @param id 门店调拨单编号
     * @return 门店调拨单信息
     */
    @Override
    public PurchaseBillDTO getAllocationBill(Long id) {
        return purchaseBillService.getPurchaseBill(id);
    }

    /**
     * 获得门店调拨单和详情
     *
     * @param billNo   门店调拨单号
     * @param tenantId 租户ID
     * @return 门店调拨单信息（包含详情）
     */
    @Override
    public PurchaseBillDTO getAllocationBillWithDetails(String billNo, Long tenantId) {
        return purchaseBillService.getPurchaseBillWithDetails(billNo, tenantId);
    }

    /**
     * 获得门店调拨单分页
     *
     * @param pageReqDTO 分页查询参数
     * @return 门店调拨单分页结果
     */
    @Override
    public PageResult<PurchaseBillDTO> getAllocationBillPage(PurchaseBillPageReqDTO pageReqDTO) {
        return purchaseBillService.getPurchaseBillPage(pageReqDTO);
    }
}