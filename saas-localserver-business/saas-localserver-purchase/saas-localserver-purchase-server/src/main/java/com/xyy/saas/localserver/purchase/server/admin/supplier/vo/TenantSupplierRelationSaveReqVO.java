package com.xyy.saas.localserver.purchase.server.admin.supplier.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import jakarta.validation.constraints.*;

/**
 * 管理后台 - 租户-供应商-关联关系新增/修改 Request VO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "管理后台 - 租户-供应商-关联关系新增/修改 Request VO")
@Data
public class TenantSupplierRelationSaveReqVO {

    /** 主键ID */
    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "17200")
    private Long id;

    /** 供应商编号 */
    @Schema(description = "供应商编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "供应商编号不能为空")
    private String supplierGuid;

    /** 首营状态 */
    @Schema(description = "首营状态：1-审核中，2-审核通过，3-审核未通过", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "首营状态不能为空")
    private Integer status = 0;

    /** 供应商信息 */
    @Schema(description = "供应商信息", example = "供应商xxx")
    @NotNull(message = "供应商信息不能为空")
    private SupplierSaveReqVO supplier;
}