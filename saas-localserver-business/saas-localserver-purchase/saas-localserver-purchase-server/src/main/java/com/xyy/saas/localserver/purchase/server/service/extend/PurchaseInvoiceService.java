package com.xyy.saas.localserver.purchase.server.service.extend;

import com.xyy.saas.localserver.purchase.api.extend.dto.PurchaseInvoiceDTO;
import com.xyy.saas.localserver.purchase.api.extend.dto.PurchaseInvoicePageReqDTO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;

/**
 * 采购-发票信息 Service 接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface PurchaseInvoiceService {

    /**
     * 创建采购-发票信息
     *
     * @param createDTO 创建信息
     * @return 创建结果
     */
    Long createPurchaseInvoice(PurchaseInvoiceDTO createDTO);

    /**
     * 更新采购-发票信息
     *
     * @param updateDTO 更新信息
     */
    void updatePurchaseInvoice(PurchaseInvoiceDTO updateDTO);

    /**
     * 删除采购-发票信息
     *
     * @param id 编号
     */
    void deletePurchaseInvoice(Long id);

    /**
     * 获得采购-发票信息
     *
     * @param id 编号
     * @return 采购-发票信息
     */
    PurchaseInvoiceDTO getPurchaseInvoice(Long id);

    /**
     * 获得采购-发票信息分页
     *
     * @param pageReqDTO 分页查询参数
     * @return 采购-发票信息分页
     */
    PageResult<PurchaseInvoiceDTO> getPurchaseInvoicePage(PurchaseInvoicePageReqDTO pageReqDTO);

}