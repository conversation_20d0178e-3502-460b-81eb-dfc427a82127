package com.xyy.saas.localserver.purchase.server.convert.extend;

import com.xyy.saas.localserver.purchase.api.extend.dto.PurchaseTransportDTO;
import com.xyy.saas.localserver.purchase.api.extend.dto.PurchaseTransportPageReqDTO;
import com.xyy.saas.localserver.purchase.server.admin.extend.vo.PurchaseTransportPageReqVO;
import com.xyy.saas.localserver.purchase.server.admin.extend.vo.PurchaseTransportRespVO;
import com.xyy.saas.localserver.purchase.server.admin.extend.vo.PurchaseTransportSaveReqVO;
import com.xyy.saas.localserver.purchase.server.dal.dataobject.extend.PurchaseTransportDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 采购-运输信息 Convert
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface PurchaseTransportConvert {

    PurchaseTransportConvert INSTANCE = Mappers.getMapper(PurchaseTransportConvert.class);

    /**
     * DTO 转 DO
     *
     * @param transportDTO DTO
     * @return DO
     */
    PurchaseTransportDO convert2DO(PurchaseTransportDTO transportDTO);

    /**
     * DTO列表 转 DO列表
     *
     * @param transportDTOs DTO列表
     * @return DO
     */
    List<PurchaseTransportDO> convert2DOList(List<PurchaseTransportDTO> transportDTOs);

    /**
     * VO 转 DTO
     *
     * @param pageReqVO VO
     * @return DTO
     */
    PurchaseTransportPageReqDTO convert2DTO(PurchaseTransportPageReqVO pageReqVO);

    /**
     * DO 转 DTO
     *
     * @param transport DO
     * @return DTO
     */
    PurchaseTransportDTO convert2DTO(PurchaseTransportDO transport);

    /**
     * VO 转 DTO
     *
     * @param saveReqVO VO
     * @return DTO
     */
    PurchaseTransportDTO convert2DTO(PurchaseTransportSaveReqVO saveReqVO);

    /**
     * DTO 转 VO
     *
     * @param transportDTO DTO
     * @return VO
     */
    PurchaseTransportRespVO convert2VO(PurchaseTransportDTO transportDTO);

    /**
     * DO 列表转 VO 列表
     *
     * @param list DO 列表
     * @return VO 列表
     */
    List<PurchaseTransportRespVO> convert2VOList(List<PurchaseTransportDTO> list);

    /**
     * DO 分页转 DTO 分页
     *
     * @param pageResult DO 分页结果
     * @return DTO 分页
     */
    PageResult<PurchaseTransportDTO> convert2DTO(PageResult<PurchaseTransportDO> pageResult);

    /**
     * DTO 分页转 VO 分页
     *
     * @param pageResult DTO 分页结果
     * @return VO 分页
     */
    PageResult<PurchaseTransportRespVO> convert2VO(PageResult<PurchaseTransportDTO> pageResult);
}
