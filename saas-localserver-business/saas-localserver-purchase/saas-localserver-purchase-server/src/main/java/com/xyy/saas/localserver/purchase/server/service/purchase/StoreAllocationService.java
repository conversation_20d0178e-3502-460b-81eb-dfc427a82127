package com.xyy.saas.localserver.purchase.server.service.purchase;

import cn.iocoder.yudao.framework.common.exception.ServiceException;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.localserver.purchase.api.purchase.dto.PurchaseBillDTO;
import com.xyy.saas.localserver.purchase.api.purchase.dto.PurchaseBillPageReqDTO;

/**
 * 门店调拨 Service 接口
 * <p>
 * 主要功能：
 * 1. 门店调拨单基础操作（保存、删除、更新、查询）
 * 2. 门店调拨单状态管理
 * 3. 门店调拨单分页查询
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface StoreAllocationService {

    // ========== 1. 基础操作 ==========

    /**
     * 保存门店调拨单
     *
     * @param saveDTO 待保存的门店调拨单信息
     * @throws ServiceException 如果门店调拨单信息为空
     */
    void saveAllocationBill(PurchaseBillDTO saveDTO);

    /**
     * 撤销门店调拨单
     *
     * @param revokeDTO 待撤销的门店调拨单信息
     * @throws ServiceException 如果门店调拨单不存在
     */
    void revokeAllocation(PurchaseBillDTO revokeDTO);

    /**
     * 删除门店调拨单
     *
     * @param id 门店调拨单编号
     * @throws ServiceException 如果门店调拨单不存在
     */
    void deleteAllocationBill(Long id);

    // ========== 2. 查询操作 ==========

    /**
     * 获得门店调拨单
     *
     * @param id 门店调拨单编号
     * @return 门店调拨单信息
     * @throws ServiceException 如果门店调拨单不存在
     */
    PurchaseBillDTO getAllocationBill(Long id);

    /**
     * 获得门店调拨单和详情
     *
     * @param billNo   门店调拨单号
     * @param tenantId 租户ID
     * @return 门店调拨单信息（包含详情）
     * @throws ServiceException 如果门店调拨单不存在
     */
    PurchaseBillDTO getAllocationBillWithDetails(String billNo, Long tenantId);

    /**
     * 获得门店调拨单分页
     *
     * @param pageReqDTO 分页查询参数
     * @return 门店调拨单分页结果
     */
    PageResult<PurchaseBillDTO> getAllocationBillPage(PurchaseBillPageReqDTO pageReqDTO);
}