package com.xyy.saas.localserver.purchase.server.service.extend.impl;

import com.xyy.saas.localserver.purchase.api.extend.dto.PurchaseTransportDTO;
import com.xyy.saas.localserver.purchase.api.extend.dto.PurchaseTransportPageReqDTO;
import com.xyy.saas.localserver.purchase.server.convert.extend.PurchaseTransportConvert;
import com.xyy.saas.localserver.purchase.server.dal.dataobject.extend.PurchaseTransportDO;
import com.xyy.saas.localserver.purchase.server.dal.mysql.extend.PurchaseTransportMapper;
import com.xyy.saas.localserver.purchase.server.service.extend.PurchaseTransportService;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import jakarta.annotation.Resource;

/**
 * 采购-运输信息 Service 实现类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
@Validated
public class PurchaseTransportServiceImpl implements PurchaseTransportService {

    @Resource
    private PurchaseTransportMapper purchaseTransportMapper;

    /**
     * 创建采购-运输信息
     *
     * @param createDTO 创建信息
     * @return 编号
     */
    @Override
    public Long createPurchaseTransport(PurchaseTransportDTO createDTO) {
        // 1. 将 DTO 转换为 DO
        PurchaseTransportDO transport = PurchaseTransportConvert.INSTANCE.convert2DO(createDTO);
        // 2. 插入数据
        purchaseTransportMapper.insert(transport);
        // 3. 返回主键
        return transport.getId();
    }

    /**
     * 更新采购-运输信息
     *
     * @param updateDTO 更新信息
     */
    @Override
    public void updatePurchaseTransport(PurchaseTransportDTO updateDTO) {
        // 1. 校验存在
        validatePurchaseTransportExists(updateDTO.getId());
        // 2. 将 DTO 转换为 DO
        PurchaseTransportDO updateObj = PurchaseTransportConvert.INSTANCE.convert2DO(updateDTO);
        // 3. 更新数据
        purchaseTransportMapper.updateById(updateObj);
    }

    /**
     * 删除采购-运输信息
     *
     * @param id 编号
     */
    @Override
    public void deletePurchaseTransport(Long id) {
        // 1. 校验存在
        validatePurchaseTransportExists(id);
        // 2. 删除数据
        purchaseTransportMapper.deleteById(id);
    }

    /**
     * 获得采购-运输信息
     *
     * @param id 编号
     * @return 采购-运输信息
     */
    @Override
    public PurchaseTransportDTO getPurchaseTransport(Long id) {
        // 1. 查询数据
        PurchaseTransportDO transport = purchaseTransportMapper.selectById(id);
        // 2. 转换结果
        return PurchaseTransportConvert.INSTANCE.convert2DTO(transport);
    }

    /**
     * 获得采购-运输信息分页
     *
     * @param pageReqDTO 分页查询参数
     * @return 采购-运输信息分页
     */
    @Override
    public PageResult<PurchaseTransportDTO> getPurchaseTransportPage(PurchaseTransportPageReqDTO pageReqDTO) {
        // 1. 分页查询
        PageResult<PurchaseTransportDO> pageResult = purchaseTransportMapper.selectPage(pageReqDTO);
        // 2. 转换结果
        return PurchaseTransportConvert.INSTANCE.convert2DTO(pageResult);
    }

    private void validatePurchaseTransportExists(Long id) {
        if (purchaseTransportMapper.selectById(id) == null) {
            throw new RuntimeException("采购-运输信息不存在");
        }
    }

}