package com.xyy.saas.localserver.purchase.server.convert.receive;

import com.xyy.saas.localserver.purchase.api.extend.dto.ExtDTO;
import com.xyy.saas.localserver.purchase.api.receive.dto.ReceiveBillDetailDTO;
import com.xyy.saas.localserver.purchase.server.admin.receive.vo.ReceiveBillDetailSaveReqVO;
import com.xyy.saas.localserver.purchase.server.convert.extend.ExtConvert;
import com.xyy.saas.localserver.purchase.server.dal.dataobject.purchase.PurchaseBillDetailDO;
import com.xyy.saas.localserver.purchase.server.dal.dataobject.receive.ReceiveBillDetailDO;
import com.xyy.saas.localserver.purchase.server.utils.ProductInfoUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import static com.xyy.saas.localserver.purchase.enums.receive.ReceiveBillStatusEnum.PENDING_RECEIVE;

/**
 * 收货单明细转换器
 * <p>
 * 主要功能：
 * 1. 基础转换 - VO/DO/DTO之间的转换
 * 2. 单据转换 - 处理不同类型单据的转换
 * 3. 状态重置 - 处理状态相关的字段重置
 * 4. 商品信息填充 - 处理商品相关信息的填充
 */
@Mapper(uses = { ExtConvert.class })
public interface ReceiveBillDetailConvert {

    ReceiveBillDetailConvert INSTANCE = Mappers.getMapper(ReceiveBillDetailConvert.class);

    // ========== 1. 基础转换方法 ==========

    /**
     * 将详情VO转换为DO
     * 
     * @param detail 收货单明细保存请求VO
     * @return 收货单明细DO
     */
    ReceiveBillDetailDO convert2DO(ReceiveBillDetailSaveReqVO detail);

    /**
     * 将详情DTO转换为DO
     *
     * @param detail 收货单明细DTO
     * @return 收货单明细DO
     */
    ReceiveBillDetailDO convert2DO(ReceiveBillDetailDTO detail);

    /**
     * 将详情DTO列表转换为DO列表
     *
     * @param details 收货单明细DTO列表
     * @return 收货单明细DO列表
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "ext", source = "ext")
    List<ReceiveBillDetailDO> convert2DOList(List<ReceiveBillDetailDTO> details);

    // ========== 2. 单据转换方法 ==========

    /**
     * 将收货单详情转换为拒收详情
     * 
     * @param sourceDetail 源详情
     * @return 拒收详情VO
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "billNo", ignore = true)
    @Mapping(target = "ext", source = "ext")
    ReceiveBillDetailSaveReqVO convert2RejectDetail(ReceiveBillDetailDTO sourceDetail);

    /**
     * 生成拒收详情
     * 
     * @param sourceDetail 源详情
     * @return 拒收详情VO
     */
    default ReceiveBillDetailSaveReqVO generateRejectDetail(ReceiveBillDetailDTO sourceDetail) {
        ReceiveBillDetailSaveReqVO rejectDetail = convert2RejectDetail(sourceDetail);

        // 数量处理
        BigDecimal rejectQty = Optional.ofNullable(sourceDetail.getRejectQuantity())
                .orElse(BigDecimal.ZERO);

        rejectDetail.setArriveQuantity(rejectQty);
        rejectDetail.setReceiveQuantity(rejectQty);
        rejectDetail.setWarehouseQuantity(BigDecimal.ZERO);
        rejectDetail.setQualifiedQuantity(BigDecimal.ZERO);
        rejectDetail.setUnqualifiedQuantity(rejectQty);

        // 金额计算
        BigDecimal discountedPrice = Optional.ofNullable(sourceDetail.getDiscountedPrice())
                .orElse(BigDecimal.ZERO);
        rejectDetail.setReceiveAmount(rejectQty.multiply(discountedPrice)
                .setScale(2, RoundingMode.HALF_UP));
        rejectDetail.setUnqualifiedAmount(rejectDetail.getReceiveAmount());
        rejectDetail.setWarehouseAmount(BigDecimal.ZERO);

        // 其他字段
        rejectDetail.setUnqualifiedReason("门店拒收"); // 明确原因
        rejectDetail.setSampleQuantity(rejectQty);
        rejectDetail.setAcceptConclusion(false);

        return rejectDetail;
    }

    /**
     * 批量生成拒收详情
     * 
     * @param details 源详情列表
     * @return 拒收详情VO列表
     */
    default List<ReceiveBillDetailSaveReqVO> generateRejectDetails(List<ReceiveBillDetailDTO> details) {
        if (CollectionUtils.isEmpty(details)) {
            return Collections.emptyList();
        }
        return details.stream()
                .map(this::generateRejectDetail)
                .toList();
    }

    /**
     * 将收货单详情转换为采购单详情
     * 
     * @param receiveDetail 收货单详情
     * @return 采购单详情
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "ext", source = "ext")
    PurchaseBillDetailDO convert2PurchaseDetail(ReceiveBillDetailDO receiveDetail);

    /**
     * 生成采购单详情
     * 
     * @param receiveDetail 收货单详情
     * @return 采购单详情
     */
    default PurchaseBillDetailDO generatePurchaseDetail(ReceiveBillDetailDO receiveDetail) {
        PurchaseBillDetailDO purchaseDetail = convert2PurchaseDetail(receiveDetail);
        // 置空id
        purchaseDetail.setId(null);

        // 设置采购数量、可退数量和金额
        purchaseDetail.setPurchaseQuantity(receiveDetail.getReceiveQuantity());
        purchaseDetail.setReturnableQuantity(receiveDetail.getReceiveQuantity());
        purchaseDetail.setPurchaseAmount(receiveDetail.getReceiveAmount());

        return purchaseDetail;
    }

    /**
     * 批量生成采购单详情
     * 
     * @param receiveDetails 收货单详情列表
     * @return 采购单详情列表
     */
    default List<PurchaseBillDetailDO> generatePurchaseDetails(List<ReceiveBillDetailDO> receiveDetails) {
        if (CollectionUtils.isEmpty(receiveDetails)) {
            return Collections.emptyList();
        }
        return receiveDetails.stream()
                .map(this::generatePurchaseDetail)
                .toList();
    }

    // ========== 3. 状态重置方法 ==========

    /**
     * 根据目标状态重置详情字段
     * 
     * @param receiveDetails 收货单详情列表
     * @param targetStatus   目标状态
     */
    default void resetDetailFields(List<ReceiveBillDetailDTO> receiveDetails, Integer targetStatus) {
        Optional.ofNullable(receiveDetails)
                .ifPresent(detailList -> detailList.forEach(detail -> {
                    if (PENDING_RECEIVE.getCode().equals(targetStatus)) {
                        resetAcceptanceFields(detail);
                    } else {
                        resetWarehouseFields(detail);
                    }
                }));
    }

    /**
     * 重置验收相关字段
     * 
     * @param receiveDetail 收货单详情
     */
    default void resetAcceptanceFields(ReceiveBillDetailDTO receiveDetail) {
        receiveDetail.setAcceptConclusion(null);
        receiveDetail.setQualifiedAmount(BigDecimal.ZERO);
        receiveDetail.setQualifiedQuantity(BigDecimal.ZERO);
        receiveDetail.setUnqualifiedQuantity(BigDecimal.ZERO);
        receiveDetail.setUnqualifiedAmount(BigDecimal.ZERO);
        receiveDetail.setTreatment(null);
        receiveDetail.setUnqualifiedReason(null);
        receiveDetail.setSampleQuantity(BigDecimal.ZERO);
    }

    /**
     * 重置入库相关字段
     * 
     * @param receiveDetail 收货单详情
     */
    default void resetWarehouseFields(ReceiveBillDetailDTO receiveDetail) {
        receiveDetail.setWarehouseAmount(BigDecimal.ZERO);
        receiveDetail.setWarehouseQuantity(BigDecimal.ZERO);
        receiveDetail.setQualifiedPositionGuid(null);
        receiveDetail.setUnqualifiedPositionGuid(null);
    }

    // ========== 4. 商品信息填充方法 ==========

    /**
     * 填充商品信息
     * 
     * @param details 详情列表
     */
    default void fillProductInfo(List<ReceiveBillDetailDTO> details) {
        ProductInfoUtil.fillProductInfo(
                details,
                ReceiveBillDetailDTO::getProductPref,
                (detail, productInfo) -> Optional.ofNullable(detail.getExt())
                        .or(() -> Optional.of(new ExtDTO()).map(e -> {
                            detail.setExt(e);
                            return e;
                        }))
                        .ifPresent(ext -> ext.setProductInfo(productInfo)));
    }
}