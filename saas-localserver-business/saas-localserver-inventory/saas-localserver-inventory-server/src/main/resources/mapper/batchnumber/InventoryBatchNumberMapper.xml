<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xyy.saas.localserver.inventory.server.dal.mysql.batchnumber.InventoryBatchNumberMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="selectInventoryBatchNumberAsc" resultType="com.xyy.saas.localserver.inventory.server.dal.dataobject.batchnumber.InventoryBatchNumberDO">
        select
            a.*
        from saas_inventory_batch_number a
        left join saas_inventory_lot_number b on a.tenant_id = b.tenant_id and a.product_pref = b.product_pref and a.lot_no = b.lot_no
        where 1 = 1
        <if test="item.productPref != null">
            and a.product_pref = #{item.productPref,jdbcType=VARCHAR}
        </if>
        <if test="item.lotNo != null">
            and a.lot_no = #{item.lotNo,jdbcType=VARCHAR}
        </if>
        <if test="item.positionGuid != null">
            and a.position_guid = #{item.positionGuid,jdbcType=VARCHAR}
        </if>
        order by b.expiry_date asc, a.in_time asc
    </select>

    <select id="selectInventoryBatchNumberDesc" resultType="com.xyy.saas.localserver.inventory.server.dal.dataobject.batchnumber.InventoryBatchNumberDO">
        select
            a.*
        from saas_inventory_batch_number a
        left join saas_inventory_lot_number b on a.tenant_id = b.tenant_id and a.product_pref = b.product_pref and a.lot_no = b.lot_no
        where 1 = 1
        <if test="item.productPref != null">
            and a.product_pref = #{item.productPref,jdbcType=VARCHAR}
        </if>
        <if test="item.lotNo != null">
            and a.lot_no = #{item.lotNo,jdbcType=VARCHAR}
        </if>
        <if test="item.positionGuid != null">
            and a.position_guid = #{item.positionGuid,jdbcType=VARCHAR}
        </if>
        order by b.expiry_date asc, a.in_time desc
    </select>

    <update id="reduceStockNumber">
        <foreach collection="items" item="item" separator=";">
            UPDATE saas_inventory_batch_number
            SET stock_number = stock_number - #{item.number}, version = #{item.version} + 1
            WHERE id = #{item.id} And version = #{item.version}
        </foreach>
    </update>

    <update id="increaseStockNumber">
        <foreach collection="items" item="item" separator=";">
            UPDATE saas_inventory_batch_number
            SET stock_number = stock_number + #{item.number}, version = #{item.version} + 1
            WHERE id = #{item.id} And version = #{item.version}
        </foreach>
    </update>

    <update id="increaseCampOnNumber">
        <foreach collection="items" item="item" separator=";">
            UPDATE saas_inventory_batch_number
            SET camp_on_number = camp_on_number + #{item.number}, version = #{item.version} + 1
            WHERE id = #{item.id} And version = #{item.version}
        </foreach>
    </update>

    <update id="releaseCampOnNumber">
        <foreach collection="items" item="item" separator=";">
            UPDATE saas_inventory_batch_number
            SET camp_on_number = camp_on_number - #{item.number}, version = #{item.version} + 1
            WHERE id = #{item.id} And version = #{item.version}
        </foreach>
    </update>

    <update id="releaseStockNumber">
        <foreach collection="items" item="item" separator=";">
            UPDATE saas_inventory_batch_number
            SET camp_on_number = camp_on_number - #{item.number}, stock_number = stock_number - #{item.number}, version = #{item.version} + 1
            WHERE id = #{item.id} And version = #{item.version}
        </foreach>
    </update>

</mapper>