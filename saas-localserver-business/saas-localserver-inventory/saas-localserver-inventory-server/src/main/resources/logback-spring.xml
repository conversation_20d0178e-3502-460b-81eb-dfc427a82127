<?xml version="1.0" encoding="UTF-8"?>
<configuration>
  <property name="log.dir" value="${log.dir:-/data/logs/saas-localserver-inventory-server}"/>
  <property name="log.name" value="${log.name:-saas-localserver-inventory-server}"/>

  <property name="PATTERN" value="%date %-5p [%36.36(%32.34logger:%line)] [%X{traceId}] - %m%n"/>
  <property name="log.pattern" value="${PATTERN}"/>
  <property name="log.pattern2" value="${PATTERN}"/>

  <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
    <encoder>
      <pattern>${log.pattern2}</pattern>
    </encoder>
  </appender>

  <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
    <file>${log.dir}/${log.name}.log</file>
    <encoder>
      <pattern>${log.pattern}</pattern>
    </encoder>
    <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
      <fileNamePattern>${log.dir}/%d{yyyy-MM, aux}/${log.name}.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
      <maxHistory>30</maxHistory>
      <maxFileSize>100MB</maxFileSize>
      <totalSizeCap>32GB</totalSizeCap>
    </rollingPolicy>
  </appender>

  <root level="INFO">
    <appender-ref ref="CONSOLE"/>
    <appender-ref ref="FILE"/>
  </root>
</configuration> 