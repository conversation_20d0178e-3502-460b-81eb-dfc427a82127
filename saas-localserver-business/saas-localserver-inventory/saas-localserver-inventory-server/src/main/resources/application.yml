server:
  port: 48082

spring:
  profiles:
    active: test
  application:
    name: saas-localserver-inventory-server
  #  cloud:
  #    nacos:
  #      discovery:
  #        heart-beat:
  #          enabled: true
  #      config:
  #        server-addr: ${spring.cloud.nacos.server-addr}
  #        import-check:
  #          enabled: false

  #  datasource:
  #    dynamic:
  #      primary: master # 设置默认数据源
  #      strict: false # 严格模式关闭
  #      datasource:
  #        master:
  #          driver-class-name: org.sqlite.JDBC
  #          url: jdbc:sqlite:file:${user.dir}/db/saas-local.db?mode=rwc
  #          hikari:
  #            minimum-idle: 5
  #            maximum-pool-size: 15
  #            is-auto-commit: true
  #            idle-timeout: 30000
  #            max-lifetime: 1800000
  #            connection-timeout: 30000
  #            connectionTestQuery: SELECT 1

  datasource:
    driver-class-name: org.sqlite.JDBC
    url: jdbc:sqlite:file:${user.dir}/db/saas-local.db?mode=rwc
    hikari:
      minimum-idle: 5
      maximum-pool-size: 15
      is-auto-commit: true
      idle-timeout: 30000
      max-lifetime: 1800000
      connection-timeout: 30000
      connectionTestQuery: SELECT 1

  servlet:
    multipart:
      max-file-size: 10MB

#dubbo:
#  application:
#    name: ${spring.application.name}
#    qos-port: 22230
#  registry:
#    address: nacos://${spring.cloud.nacos.server-addr}
#    register-mode: instance
#    parameters:
#      namespace: ${spring.cloud.nacos.discovery.namespace}
#    group: dubbo
#  config-center:
#    address: nacos://${spring.cloud.nacos.server-addr}
#    group: ${spring.cloud.nacos.config.group}
#    namespace: ${spring.cloud.nacos.config.namespace}
#  protocol:
#    name: dubbo
#    port: -1
#  consumer:
#    timeout: 5000
#    check: false
#    filter: dubboTenantFilter
#    provider-namespace: ${spring.cloud.nacos.discovery.namespace}
#    group: ${dubbo.registry.group}
#  provider:
#    timeout: 10000
#    filter: dubboTenantFilter
#    group: ${dubbo.registry.group}

mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    db-config:
      sql-parser-cache: true
      id-type: auto
      logic-delete-value: 1
      logic-not-delete-value: 0
      # 明确指定方言
      db-type: sqlite

yudao:
  info:
    version: 1.0.0
    base-package: cn.iocoder.yudao,com.xyy.saas
  web:
    admin-ui:
      url: http://localhost:8080 # 管理后台地址
#  security:
#    permit-all_urls:
#      - /admin-api/mp/open/** # 微信公众号开放平台，微信回调接口，不需要登录

logging:
  level:
    com.baomidou.mybatisplus: info
    com.xyy.saas: DEBUG