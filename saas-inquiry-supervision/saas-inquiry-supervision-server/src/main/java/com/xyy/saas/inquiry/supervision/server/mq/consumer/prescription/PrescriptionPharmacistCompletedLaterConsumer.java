package com.xyy.saas.inquiry.supervision.server.mq.consumer.prescription;


import com.xyy.saas.eventbus.rocketmq.annotation.EventBusConsumer;
import com.xyy.saas.eventbus.rocketmq.annotation.EventBusListener;
import com.xyy.saas.inquiry.mq.prescription.PrescriptionPharmacistCompletedEvent;
import com.xyy.saas.inquiry.mq.prescription.PrescriptionPharmacistCompletedLaterEvent;
import com.xyy.saas.inquiry.supervision.server.service.prescription.PrescriptionSupervisionService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;


@Component
@Slf4j
@EventBusConsumer(groupId = "com_xyy_saas_inquiry_supervision_server_mq_consumer_PrescriptionPharmacistCompletedLaterConsumer",
    topic = PrescriptionPharmacistCompletedLaterEvent.TOPIC)
public class PrescriptionPharmacistCompletedLaterConsumer {

    @Resource
    private PrescriptionSupervisionService prescriptionSupervisionService;

    @EventBusListener
    public void prescriptionPharmacistCompletedLaterConsumer(PrescriptionPharmacistCompletedEvent event) {
        prescriptionSupervisionService.prescriptionSupervisionLater(event.getMsg());

    }

}
