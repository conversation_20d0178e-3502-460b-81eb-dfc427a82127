package com.xyy.saas.inquiry.supervision.server.convert.prescription;

import com.xyy.saas.inquiry.hospital.api.doctor.dto.InquiryDoctorDto;
import com.xyy.saas.inquiry.hospital.api.doctor.dto.InquiryDoctorExtDto;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionRespDTO;
import com.xyy.saas.inquiry.mq.prescription.dto.PrescriptionMqCommonMessage;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDetailDto;
import com.xyy.saas.inquiry.pojo.TenantDto;
import com.xyy.saas.inquiry.pojo.transmitter.internet.PrescriptionTransmitterDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;
import java.util.Optional;

/**
 * 处方外配
 *
 * @Author:chenxiaoyi
 * @Date:2024/10/12 17:17
 */
@Mapper
public interface PrescriptionSupervisionConvert {

    PrescriptionSupervisionConvert INSTANCE = Mappers.getMapper(PrescriptionSupervisionConvert.class);


    default PrescriptionTransmitterDTO convertPrescriptionTransmission(InquiryPrescriptionRespDTO prescriptionRespVO, InquiryRecordDetailDto inquiryRecordDetail, TenantDto tenant, PrescriptionMqCommonMessage prescription,
        InquiryDoctorDto inquiryDoctorDto) {
        PrescriptionTransmitterDTO prescriptionTransmitterDTO = convert(prescriptionRespVO, prescription);
        prescriptionTransmitterDTO.setIdCard(inquiryRecordDetail.getPatientIdCard());
        prescriptionTransmitterDTO.setTenantInfo(tenant);
        prescriptionTransmitterDTO.setFillingStatus4CdRegulatory(Optional.ofNullable(inquiryDoctorDto.getExt()).orElse(new InquiryDoctorExtDto()).getFillingStatus4CdRegulatory());
        return prescriptionTransmitterDTO;
    }

    @Mapping(target = "fullName", source = "prescriptionRespVO.patientName")
    @Mapping(target = "businessNo", source = "prescriptionRespVO.pref")
    @Mapping(target = "inquiryPref" ,source = "prescriptionRespVO.inquiryPref")
    @Mapping(target = "auditorType", source = "prescription.auditorType")
    @Mapping(target = "prescriptionImgUrl", expression = "java(org.apache.commons.lang3.StringUtils.defaultIfBlank(prescription.getPrescriptionImgUrl(),prescriptionRespVO.getPrescriptionImgUrl()))")
    @Mapping(target = "prescriptionPdfUrl", expression = "java(org.apache.commons.lang3.StringUtils.defaultIfBlank(prescription.getPrescriptionPdfUrl(),prescriptionRespVO.getPrescriptionPdfUrl()))")
    @Mapping(target = "userId", expression = "java(cn.iocoder.yudao.framework.web.core.util.LoginUserContextUtils.getLoginUserId())")
    PrescriptionTransmitterDTO convert(InquiryPrescriptionRespDTO prescriptionRespVO, PrescriptionMqCommonMessage prescription);


}

