package com.xyy.saas.inquiry.supervision.server.api.prescription;

import com.xyy.saas.inquiry.mq.prescription.dto.PrescriptionMqCommonMessage;
import com.xyy.saas.inquiry.supervision.api.prescription.PrescriptionSupervisionApi;
import com.xyy.saas.inquiry.supervision.server.service.prescription.PrescriptionSupervisionService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;

/**
 * @Author:chen<PERSON><PERSON>i
 * @Date:2025/07/24 17:25
 */
@Slf4j
@DubboService
public class PrescriptionSupervisionApiImpl implements PrescriptionSupervisionApi {

    @Resource
    private PrescriptionSupervisionService prescriptionSupervisionService;

    @Override
    public void prescriptionSupervision(PrescriptionMqCommonMessage prescription) {
        prescriptionSupervisionService.prescriptionSupervision(prescription);
    }

}
