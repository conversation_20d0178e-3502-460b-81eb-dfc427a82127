package com.xyy.saas.inquiry.supervision.server.mq.consumer.prescription;

import com.xyy.saas.eventbus.rocketmq.annotation.EventBusConsumer;
import com.xyy.saas.eventbus.rocketmq.annotation.EventBusListener;
import com.xyy.saas.inquiry.mq.prescription.PrescriptionPharmacistCompletedEvent;
import com.xyy.saas.inquiry.supervision.server.service.prescription.PrescriptionSupervisionService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 药师审方完成对接监管
 *
 * @Author:chenxiaoyi
 * @Date:2024/12/03 16:29
 */
@Component
@Slf4j
@EventBusConsumer(groupId = "com_xyy_saas_inquiry_supervision_server_mq_consumer_PrescriptionPharmacistCompletedSupervisionConsumer",
    topic = PrescriptionPharmacistCompletedEvent.TOPIC)
public class PrescriptionPharmacistCompletedSupervisionConsumer {

    @Resource
    private PrescriptionSupervisionService prescriptionSupervisionService;

    @EventBusListener
    public void prescriptionPharmacistCompletedSupervisionConsumer(PrescriptionPharmacistCompletedEvent event) {
        prescriptionSupervisionService.prescriptionSupervision(event.getMsg());
    }


}
