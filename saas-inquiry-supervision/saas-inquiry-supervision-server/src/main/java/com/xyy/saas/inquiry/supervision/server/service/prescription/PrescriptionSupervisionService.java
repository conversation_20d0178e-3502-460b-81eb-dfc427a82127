package com.xyy.saas.inquiry.supervision.server.service.prescription;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.tenant.core.util.TenantUtils;
import cn.iocoder.yudao.module.system.api.tenant.TenantApi;
import com.xyy.saas.inquiry.enums.transmitter.NodeTypeEnum;
import com.xyy.saas.inquiry.hospital.api.doctor.InquiryDoctorApi;
import com.xyy.saas.inquiry.hospital.api.doctor.dto.InquiryDoctorDto;
import com.xyy.saas.inquiry.hospital.api.prescription.InquiryPrescriptionApi;
import com.xyy.saas.inquiry.hospital.api.prescription.InquiryPrescriptionDetailApi;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionQueryDTO;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionRespDTO;
import com.xyy.saas.inquiry.mq.prescription.dto.PrescriptionMqCommonMessage;
import com.xyy.saas.inquiry.patient.api.inquiry.InquiryApi;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDetailDto;
import com.xyy.saas.inquiry.pojo.TenantDto;
import com.xyy.saas.inquiry.pojo.transmitter.internet.PrescriptionTransmitterDTO;
import com.xyy.saas.inquiry.supervision.server.convert.prescription.PrescriptionSupervisionConvert;
import com.xyy.saas.inquiry.supervision.server.service.SupervisionBaseService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

/**
 * @Author:chenxiaoyi
 * @Date:2025/07/24 17:25
 */
@Component
@Slf4j
public class PrescriptionSupervisionService extends SupervisionBaseService {


    @DubboReference
    private InquiryPrescriptionApi inquiryPrescriptionApi;

    @DubboReference
    private InquiryPrescriptionDetailApi inquiryPrescriptionDetailApi;

    @DubboReference
    private InquiryApi inquiryApi;

    @DubboReference
    private TenantApi tenantApi;

    @DubboReference
    private InquiryDoctorApi inquiryDoctorApi;


    public void prescriptionSupervision(PrescriptionMqCommonMessage prescription) {

        PrescriptionTransmitterDTO dto = getPrescriptionTransmitterDTO(prescription);

        if (dto == null) {
            return;
        }

        // 处方信息上报
        supervisionPrescription(dto, NodeTypeEnum.DRUG_SUPERVISION_PRESCRIPTION_INFO_REPORT, CommonResult.class, null, null);

        // 处方明细上报
        supervisionPrescription(dto, NodeTypeEnum.DRUG_SUPERVISION_PRESCRIPTION_DETAIL_REPORT, CommonResult.class, null, null);


    }

    private PrescriptionTransmitterDTO getPrescriptionTransmitterDTO(PrescriptionMqCommonMessage prescriptionMqCommonMessage) {
        InquiryPrescriptionRespDTO prescription = inquiryPrescriptionApi.getInquiryPrescription(InquiryPrescriptionQueryDTO.builder().pref(prescriptionMqCommonMessage.getPrescriptionPref()).build());
        if (prescription == null) {
            return null;
        }
        TenantDto tenant = TenantUtils.execute(prescription.getTenantId(), () -> tenantApi.getTenantAndCertDto());
        InquiryRecordDetailDto inquiryRecordDetail = inquiryApi.getInquiryRecordDetail(prescription.getInquiryPref());
        InquiryDoctorDto inquiryDoctorDto = inquiryDoctorApi.getInquiryDoctorByDoctorPref(prescription.getDoctorPref());
        return PrescriptionSupervisionConvert.INSTANCE.convertPrescriptionTransmission(prescription, inquiryRecordDetail, tenant, prescriptionMqCommonMessage,inquiryDoctorDto);
    }

    public void prescriptionSupervisionLater(PrescriptionMqCommonMessage prescription) {
        PrescriptionTransmitterDTO dto = getPrescriptionTransmitterDTO(prescription);

        if (dto == null) {
            return;
        }

        // 处方明细延迟上报
        supervisionPrescription(dto, NodeTypeEnum.DRUG_SUPERVISION_PRESCRIPTION_DETAIL_REPORT_LATER, CommonResult.class, null, null);

    }
}
