###  1.药师登录  请求 /login 接口 => 成功（无验证码)
POST {{baseAdminSystemUrl}}/system/auth/login
Content-Type: application/json

{
  "username": "15926359991",
  "password": "Abc123456"
}

> {%
  client.global.set("tenantId", response.body.data.tenantId == null ? response.body.data.tenantList[0].id : response.body.data.tenantId);
  client.global.set("token", response.body.data.accessToken);
  console.log(response.body.data.accessToken);
  client.global.set("loginUserId", response.body.data.userId);
%}


### 2.药师出诊
PUT {{baseAppKernelUrl}}/kernel/pharmacist/inquiry-pharmacist/start-receipt
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{tenantId}}

> {%
  if (response.body.code !== 0) {
    throw new Error(response.body.msg);
  }
%}

### 3.药师获取审方池待审数量
GET {{baseAppKernelUrl}}/kernel/pharmacist/prescription-audit/wait-receive-count
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{tenantId}}

> {%
  if (response.body.code !== 0) {
    throw new Error(response.body.msg);
  }
  if (response.body.data === 0) {
    throw new Error('药师待审数量为空');
  }
%}


### 4.药师领取一个待审处方
GET {{baseAppKernelUrl}}/kernel/pharmacist/prescription-audit/receive-prescription
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{tenantId}}

> {%
  if (response.body.code !== 0) {
    throw new Error(response.body.msg);
  }
  client.global.set("auditRecordId", response.body.data.auditRecordId);
  client.global.set("prescriptionPref", response.body.data.pref);

%}

### 5.药师审核通过一个处方
POST {{baseAppKernelUrl}}/kernel/pharmacist/prescription-audit/audit-pass
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{tenantId}}

{
  "auditRecordId": {{auditRecordId}},
  "pref": "{{prescriptionPref}}",
  "auditWayType": "2"
}

> {%
  if (response.body.code !== 0) {
    throw new Error(response.body.msg);
  }
%}


### 2.药师停诊
PUT {{baseAppKernelUrl}}/kernel/pharmacist/inquiry-pharmacist/stop-receipt
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{tenantId}}

> {%
  if (response.body.code !== 0) {
    throw new Error(response.body.msg);
  }
%}