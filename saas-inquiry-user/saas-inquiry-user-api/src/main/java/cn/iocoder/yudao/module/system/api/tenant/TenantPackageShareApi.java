package cn.iocoder.yudao.module.system.api.tenant;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantPackageShareRelationDto;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantPackageShareRelationPageReqDto;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantPackageShareRelationRespDto;
import com.xyy.saas.inquiry.enums.system.BizTypeEnum;
import java.util.List;

/**
 * 门店套餐共享Api
 *
 * <AUTHOR>
 */
public interface TenantPackageShareApi {


    default List<TenantPackageShareRelationDto> getTenantPackageShareRelationList(Long tenantId) {
        return getTenantPackageShareRelationList(tenantId, BizTypeEnum.HYWZ);
    }

    List<TenantPackageShareRelationDto> getTenantPackageShareRelationList(Long tenantId, BizTypeEnum bizTypeEnum);

    /**
     * 获得门店共享套餐包信息分页
     *
     * @param pageReqDto 分页查询
     * @return 门店套餐包信息分页
     */
    PageResult<TenantPackageShareRelationRespDto> pageTenantPackageShareRelation(TenantPackageShareRelationPageReqDto pageReqDto);


}
