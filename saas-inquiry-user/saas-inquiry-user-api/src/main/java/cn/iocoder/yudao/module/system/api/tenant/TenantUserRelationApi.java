package cn.iocoder.yudao.module.system.api.tenant;

import cn.iocoder.yudao.module.system.api.tenant.dto.TenantUserClockInDto;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantUserRelationBindDto;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantUserRelationDto;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantUserRelationReqDto;
import com.xyy.saas.inquiry.enums.system.RoleCodeEnum;
import java.util.List;

/**
 * 多门店的 API 接口
 *
 * <AUTHOR>
 */
public interface TenantUserRelationApi {


    /**
     * 获取门店下有某个角色的用户列表 对外暴露，根据header中tenantId获取
     *
     * @param roleCodeEnum 角色Code
     * @return 门店List<TenantUserRelationDto>
     */
    List<TenantUserRelationDto> getTenantUserRelationsByRoleCode(RoleCodeEnum roleCodeEnum);

    /**
     * 获取当前用户 在哪些门店，有roleCodeEnum角色
     *
     * @param roleCodeEnum 角色Code
     * @return
     */
    List<TenantUserRelationDto> getUserTenantRelationsByRoleCode(RoleCodeEnum roleCodeEnum);

    /**
     * 获取某个用户所关联的门店
     *
     * @return 门店关系List<TenantUserRelationDto>
     */
    List<TenantUserRelationDto> getAvailableTenantListByUserId(Long userId);


    /**
     * 获取某个用户 在某个门店的关联可用关系
     *
     * @param userId
     * @param tenantId
     * @return
     */
    TenantUserRelationDto getAvailableTenantUserRelation(Long userId, Long tenantId);

    /**
     * 解除当前用户其他绑定关系+角色，重新绑定门店列表
     *
     * @param userId
     * @param bindTenantIds
     */
    void reBindNewTenantUserRelation(TenantUserRelationBindDto bindDto);

    /**
     * 门店用户打卡
     */
    void tenantUserClockIn(TenantUserClockInDto clockInDto);

    /**
     * 分页查询
     *
     * @param tenantUserRelationReqDto
     * @return
     */
    List<TenantUserRelationDto> getByCondition(TenantUserRelationReqDto tenantUserRelationReqDto);
}
