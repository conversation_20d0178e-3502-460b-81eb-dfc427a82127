package cn.iocoder.yudao.module.system.service.tenant;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.*;
import static cn.iocoder.yudao.module.system.enums.LogRecordConstants.*;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantPackageReqDto;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.packages.TenantPackageListReqVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.packages.TenantPackagePageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.packages.TenantPackageRespVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.packages.TenantPackageSaveReqVO;
import cn.iocoder.yudao.module.system.convert.tenant.TenantPackageConvert;
import cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantPackageDO;
import cn.iocoder.yudao.module.system.dal.mysql.tenant.TenantPackageMapper;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.service.impl.DiffParseFunction;
import com.mzt.logapi.starter.annotation.LogRecord;
import com.xyy.saas.inquiry.enums.inquiry.InquiryBizTypeEnum;
import com.xyy.saas.inquiry.enums.system.BizTypeEnum;
import com.xyy.saas.inquiry.enums.tenant.TenantTypeEnum;
import com.xyy.saas.inquiry.generic.api.InquiryGenericApiService;
import com.xyy.saas.inquiry.generic.api.dto.hospital.InquiryHospitalReqDto;
import com.xyy.saas.inquiry.generic.api.dto.hospital.InquiryHospitalRespDto;
import com.xyy.saas.inquiry.pojo.inquiry.InquiryPackageItem;
import jakarta.annotation.Resource;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

/**
 * 门店套餐 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class TenantPackageServiceImpl implements TenantPackageService {

    @Resource
    private TenantPackageMapper tenantPackageMapper;
    @Resource
    @Lazy // 避免循环依赖的报错
    private TenantService tenantService;
    @Resource
    @Lazy // 避免循环依赖的报错
    private TenantPackageRelationService tenantPackageRelationService;

    @Resource
    private InquiryGenericApiService inquiryGenericApiService;

    @Override
    @LogRecord(type = SYSTEM_TENANT_PACKAGE, subType = SYSTEM_TENANT_PACKAGE_CREATE_SUB_TYPE, bizNo = "{{#tenantPackage.id}}",
        success = SYSTEM_TENANTT_PACKAGE_CREATE_SUCCESS)
    public Long createTenantPackage(TenantPackageSaveReqVO createReqVO) {
        validPackage(createReqVO, null);
        // 插入 packageInitReqVO2DO
        TenantPackageDO tenantPackage = TenantPackageConvert.INSTANCE.packageInitReqVO2DO(createReqVO);
        // 判断医院||处方类型是否重复
        validPackageItemsDuplicate(tenantPackage.getInquiryPackageItems(), tenantPackage.getInquiryBizType());

        tenantPackageMapper.insert(tenantPackage);

        // 3. 记录操作日志上下文
        LogRecordContext.putVariable("tenantPackage", tenantPackage);
        // 返回
        return tenantPackage.getId();
    }

    @Override
    @DSTransactional // 多数据源，使用 @DSTransactional 保证本地事务，以及数据源的切换
    @LogRecord(type = SYSTEM_TENANT_PACKAGE, subType = SYSTEM_TENANTT_PACKAGE_UPDATE_SUB_TYPE, bizNo = "{{#tenantPackage.id}}",
        success = SYSTEM_TENANTT_PACKAGE_UPDATE_SUCCESS)
    public void updateTenantPackage(TenantPackageSaveReqVO updateReqVO) {
        // 校验存在
        TenantPackageDO tenantPackage = validateTenantPackageExists(updateReqVO.getId());
        validPackage(updateReqVO, updateReqVO.getId());
        // 更新
        TenantPackageDO updateObj = TenantPackageConvert.INSTANCE.packageSaveReqVO2DO(updateReqVO);

        tenantPackageMapper.updateById(updateObj);
        // 如果菜单发生变化，则修改每个门店的菜单
//        if (!CollUtil.isEqualList(tenantPackage.getMenuIds(), updateReqVO.getMenuIds())) {
//            List<TenantDO> tenants = tenantService.getTenantListByPackageId(tenantPackage.getId());
//            tenants.forEach(tenant -> tenantService.updateTenantRoleMenu(tenant.getId(), updateReqVO.getMenuIds()));
//        }
        // 3. 记录操作日志上下文
        LogRecordContext.putVariable(DiffParseFunction.OLD_OBJECT, BeanUtils.toBean(tenantPackage, TenantPackageSaveReqVO.class));
        LogRecordContext.putVariable("tenantPackage", tenantPackage);
    }

    private void validPackage(TenantPackageSaveReqVO reqVO, Long id) {
        final TenantPackageDO packageDO = tenantPackageMapper.selectOne(TenantPackageDO::getName, reqVO.getName());
        if (packageDO == null) {
            return;
        }
        // 如果 id 为空，说明不用比较是否为相同名字的套餐
        if (id == null) {
            throw exception(TENANT_PACKAGE_NAME_DUPLICATE, reqVO.getName());
        }
        if (!packageDO.getId().equals(id)) {
            throw exception(TENANT_PACKAGE_NAME_DUPLICATE, reqVO.getName());
        }

        if(BizTypeEnum.HYWZ.getCode() == reqVO.getBizType()) {
            if(!CollectionUtils.isEmpty(reqVO.getHeadMenuIds()) || !CollectionUtils.isEmpty(reqVO.getStoreMenuIds())) {
                throw exception(TENANT_PACKAGE_MENU_ERROR, reqVO.getName());
            }
        } else if(BizTypeEnum.ZHL.getCode() == reqVO.getBizType()) {
            if(TenantTypeEnum.SINGLE_STORE.getCode() == reqVO.getPackageType()) {
                if(!CollectionUtils.isEmpty(reqVO.getHeadMenuIds()) || CollectionUtils.isEmpty(reqVO.getStoreMenuIds())) {
                    throw exception(TENANT_PACKAGE_MENU_ERROR, reqVO.getName());
                }
            }
            if(TenantTypeEnum.CHAIN_HEADQUARTERS.getCode() == reqVO.getPackageType()) {
                if(CollectionUtils.isEmpty(reqVO.getHeadMenuIds()) || CollectionUtils.isEmpty(reqVO.getStoreMenuIds())) {
                    throw exception(TENANT_PACKAGE_MENU_ERROR, reqVO.getName());
                }
            }
        }
    }

    public void validPackageItemsDuplicate(List<InquiryPackageItem> packageItems, Integer inquiryBizType) {

        if (CollUtil.isNotEmpty(packageItems) && Objects.equals(inquiryBizType, InquiryBizTypeEnum.DRUGSTORE_INQUIRY.getCode())) {

            if (packageItems.stream().anyMatch(p -> CollUtil.isEmpty(p.getItems()))) {
                throw exception(TENANT_PACKAGE_ITEM_FAIL);
            }

            // 检查空值情况：如果有一个空值，则整个列表只能有这一个元素
            long emptyCount = packageItems.stream().filter(item -> CollUtil.isEmpty(item.getPrescriptionValue())).count();

            if (emptyCount > 0) {
                // 如果有空值但不止一个元素，或者有空值但还有其他非空元素
                if (emptyCount > 1 || packageItems.size() > 1) {
                    throw exception(TENANT_PACKAGE_PRESCRIPTION_VALUE_DUPLICATE);
                }
            } else {
                Set<Integer> allPrescriptionValues = new HashSet<>();
                if (packageItems.stream()
                    .filter(item -> CollUtil.isNotEmpty(item.getPrescriptionValue()))
                    .flatMap(item -> item.getPrescriptionValue().stream())
                    .anyMatch(pre -> !allPrescriptionValues.add(pre))) {
                    throw exception(TENANT_PACKAGE_PRESCRIPTION_VALUE_DUPLICATE);
                }
            }
        }
    }

    @Override
    @LogRecord(type = SYSTEM_TENANT_PACKAGE, subType = SYSTEM_TENANTT_PACKAGE_DELETE_SUB_TYPE, bizNo = "{{#id}}",
        success = SYSTEM_TENANTT_PACKAGE_DELETE_SUCCESS)
    public void deleteTenantPackage(Long id) {
        // 校验存在
        TenantPackageDO packageDO = validateTenantPackageExists(id);
        // 校验正在使用
        validateTenantUsed(id);
        // 删除
        tenantPackageMapper.deleteById(id);
        // 3. 记录操作日志上下文
        LogRecordContext.putVariable("tenantPackage", packageDO);
    }

    private TenantPackageDO validateTenantPackageExists(Long id) {
        TenantPackageDO tenantPackage = tenantPackageMapper.selectById(id);
        if (tenantPackage == null) {
            throw exception(TENANT_PACKAGE_NOT_EXISTS);
        }
        return tenantPackage;
    }

    private void validateTenantUsed(Long id) {
        if (tenantPackageRelationService.getTenantCountByPackageId(id) > 0) {
            throw exception(TENANT_PACKAGE_USED);
        }
    }

    @Override
    public TenantPackageRespVO getTenantPackage(Long id) {
        TenantPackageDO tenantPackageDO = tenantPackageMapper.selectById(id);
        List<TenantPackageRespVO> packageRespVOS = getTenantPackageRespVOS(Collections.singletonList(tenantPackageDO));
        if (CollUtil.isEmpty(packageRespVOS)) {
            return null;
        }
        return packageRespVOS.get(0);
    }

    @Override
    public PageResult<TenantPackageRespVO> getTenantPackagePage(TenantPackagePageReqVO pageReqVO) {
        PageResult<TenantPackageDO> pageResult = tenantPackageMapper.selectPage(pageReqVO);
        if (pageResult == null || pageResult.getTotal() == 0) {
            return PageResult.empty();
        }
        List<TenantPackageRespVO> list = getTenantPackageRespVOS(pageResult.getList());
        return new PageResult<>(list, pageResult.getTotal());
    }

    @Override
    public Map<Long, TenantPackageRespVO> getTenantPackageVoByCondition(TenantPackageReqDto packageDto) {
        List<TenantPackageDO> tenantPackageDOS = tenantPackageMapper.selectListByCondition(packageDto);
        return getTenantPackageRespVOS(tenantPackageDOS).stream().collect(Collectors.toMap(TenantPackageRespVO::getId, Function.identity(), (a, b) -> b));
    }

    @Override
    public List<TenantPackageDO> getTenantPackageDoByCondition(TenantPackageReqDto packageDto) {
        if (CollUtil.isEmpty(packageDto.getPackageIds())) {
            return Collections.emptyList();
        }
        return tenantPackageMapper.selectListByCondition(packageDto);
    }

    @Override
    public List<TenantPackageDO> getTenantPackageList(TenantPackageListReqVO reqVO) {
        return tenantPackageMapper.getTenantPackageList(reqVO);
    }

    /**
     * 套餐DO转VO 填充医院信息
     *
     * @param packageDOList 套餐DOs
     * @return 套餐VOs
     */
    private List<TenantPackageRespVO> getTenantPackageRespVOS(List<TenantPackageDO> packageDOList) {
        List<String> inquiryHospitalPrefs = Optional.ofNullable(packageDOList).orElse(List.of()).stream().filter(tpd -> CollUtil.isNotEmpty(tpd.getHospitalPrefs())).flatMap(tpd -> tpd.getHospitalPrefs().stream()).distinct()
            .collect(Collectors.toList());

        // List<String> itemHosPrefs = Optional.ofNullable(packageDOList).orElse(List.of()).stream()
        //     .flatMap(p -> Optional.ofNullable(p.getInquiryPackageItems()).orElse(List.of()).stream().flatMap(item -> Optional.ofNullable(item.getHospitalPref()).orElse(List.of()).stream()))
        //     .distinct().toList();
        //
        // inquiryHospitalPrefs.addAll(itemHosPrefs);

        Map<String, InquiryHospitalRespDto> inquiryHospitalsMap = CollUtil.isEmpty(inquiryHospitalPrefs) ? Map.of() :
            inquiryGenericApiService.getInquiryHospitalsBaseInfoMap(InquiryHospitalReqDto.builder().inquiryHospitalPrefs(inquiryHospitalPrefs).build());

        return packageDOList.stream().map(tp -> {
            // 使用兼容性转换方法
            TenantPackageRespVO vo = TenantPackageConvert.INSTANCE.convertVo(tp);

            Optional.ofNullable(vo.getHospitalPrefs()).ifPresent(hids -> {
                String hospitalName = hids.stream().distinct().map(hid -> inquiryHospitalsMap.getOrDefault(hid, new InquiryHospitalRespDto()).getName()).filter(StringUtils::isNotBlank).collect(Collectors.joining(","));
                vo.setHospitalName(hospitalName);
            });

            Optional.ofNullable(vo.getInquiryPackageItems()).orElse(List.of()).stream().filter(item -> CollUtil.isNotEmpty(item.getHospitalPref()))
                .forEach(item -> {
                    String hospitalName = item.getHospitalPref().stream().distinct().map(hid -> inquiryHospitalsMap.getOrDefault(hid, new InquiryHospitalRespDto()).getName()).filter(StringUtils::isNotBlank).collect(Collectors.joining(","));
                    item.setHospitalName(hospitalName);
                });

            return vo;
        }).collect(Collectors.toList());
    }

    @Override
    public TenantPackageDO validTenantPackage(Long id) {
        TenantPackageDO tenantPackage = tenantPackageMapper.selectById(id);
        if (tenantPackage == null) {
            throw exception(TENANT_PACKAGE_NOT_EXISTS);
        }
        if (tenantPackage.getStatus().equals(CommonStatusEnum.DISABLE.getStatus())) {
            throw exception(TENANT_PACKAGE_DISABLE, tenantPackage.getName());
        }
        return tenantPackage;
    }

    @Override
    public List<TenantPackageDO> getTenantPackageListByStatus(Integer status) {
        return tenantPackageMapper.selectListByStatus(status);
    }

}
