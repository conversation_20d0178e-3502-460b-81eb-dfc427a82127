package cn.iocoder.yudao.module.system.convert.tenant;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantServicePackRelationDto;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantServicePackRelationReqDto;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.servicepack.TenantServicePackRelationBatchChangeVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.servicepack.TenantServicePackRelationDetailRespVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.servicepack.TenantServicePackRelationItemVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.servicepack.TenantServicePackRelationPageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.servicepack.TenantServicePackRelationRespVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.servicepack.TenantServicePackRelationSaveReqVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant.TenantRespVO;
import cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantTransmissionServicePackRelationDO;
import com.xyy.saas.inquiry.enums.tenant.TenantServicePackRelationStatusEnum;
import com.xyy.saas.inquiry.enums.transmitter.OrganTypeEnum;
import com.xyy.saas.inquiry.pojo.servicepack.ServicePackRelationExtDto;
import com.xyy.saas.inquiry.transmitter.api.servicepack.dto.TenantTransmissionServicePackRespDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Stream;

/**
 * 门店 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface TenantServicePackRelationConvert {

    TenantServicePackRelationConvert INSTANCE = Mappers.getMapper(TenantServicePackRelationConvert.class);

    TenantServicePackRelationItemVO convertDo2Vo(TenantTransmissionServicePackRelationDO s);

    default TenantTransmissionServicePackRelationDO convertDo(TenantServicePackRelationItemVO s, TenantTransmissionServicePackRespDTO p) {

        p = Optional.ofNullable(p).orElseGet(TenantTransmissionServicePackRespDTO::new);

        return TenantTransmissionServicePackRelationDO.builder()
            .tenantId(s.getTenantId()).status(s.getStatus()).catalogId(s.getCatalogId())
            .ext(s.getExt()).servicePackId(s.getServicePackId())
            .servicePackVersion(p.getVersion())
            .organId(p.getOrganId())
            .organType(s.getOrganType()).build();
    }

    default TenantTransmissionServicePackRelationDO convertDo(Long tenantId, ServicePackRelationExtDto ext, TenantTransmissionServicePackRelationDO relationDO, TenantTransmissionServicePackRespDTO p) {
        return TenantTransmissionServicePackRelationDO.builder()
            .tenantId(tenantId)
            .status(TenantServicePackRelationStatusEnum.OPEN.getCode())
            .catalogId(relationDO == null ? null : relationDO.getCatalogId())
            .ext(ext)
            .servicePackId(p.getId())
            .servicePackVersion(p.getVersion())
            .organId(p.getOrganId())
            .organType(p.getOrganType()).build();
    }

    TenantServicePackRelationPageReqVO convertReqVo(TenantServicePackRelationReqDto reqDto);

    List<TenantServicePackRelationDto> convertDto(List<TenantTransmissionServicePackRelationDO> relationDOS);


    default List<TenantServicePackRelationItemVO> convertSaveList(TenantServicePackRelationSaveReqVO createReqVO) {
        return Stream.of(
            Optional.ofNullable(createReqVO.getMedicalInsurances()).orElseGet(ArrayList::new).stream()
                .peek(m -> m.setTenantId(createReqVO.getTenantId()).setStatus(createReqVO.getMedicalInsuranceStatus())).toList(),

            Optional.ofNullable(createReqVO.getDrugSupervisions()).orElseGet(ArrayList::new).stream()
                .peek(m -> m.setTenantId(createReqVO.getTenantId()).setStatus(createReqVO.getDrugSupervisionStatus())).toList(),

            Optional.ofNullable(createReqVO.getInternetHospitals()).orElseGet(ArrayList::new).stream()
                .peek(m -> m.setTenantId(createReqVO.getTenantId()).setStatus(createReqVO.getInternetHospitalStatus())).toList()
        ).flatMap(Collection::stream).toList();
    }

    default void convertRespVo(TenantServicePackRelationDetailRespVO packRelationRespVO, List<TenantServicePackRelationItemVO> relationDOS) {

        packRelationRespVO.setMedicalInsurances(relationDOS.stream().filter(s -> Objects.equals(s.getOrganType(), OrganTypeEnum.MEDICAL_INSURANCE.getCode())).toList());
        packRelationRespVO.setMedicalInsuranceStatus(CollUtil.isEmpty(packRelationRespVO.getMedicalInsurances()) ? TenantServicePackRelationStatusEnum.UN_OPEN.getCode() :
            packRelationRespVO.getMedicalInsurances().getFirst().getStatus());

        packRelationRespVO.setDrugSupervisions(relationDOS.stream().filter(s -> Objects.equals(s.getOrganType(), OrganTypeEnum.DRUG_SUPERVISION.getCode())).toList());
        packRelationRespVO.setDrugSupervisionStatus(CollUtil.isEmpty(packRelationRespVO.getDrugSupervisions()) ? TenantServicePackRelationStatusEnum.UN_OPEN.getCode() :
            packRelationRespVO.getDrugSupervisions().getFirst().getStatus());

        packRelationRespVO.setInternetHospitals(relationDOS.stream().filter(s -> Objects.equals(s.getOrganType(), OrganTypeEnum.INTERNET_SUPERVISION.getCode())).toList());
        packRelationRespVO.setInternetHospitalStatus(CollUtil.isEmpty(packRelationRespVO.getInternetHospitals()) ? TenantServicePackRelationStatusEnum.UN_OPEN.getCode() :
            packRelationRespVO.getInternetHospitals().getFirst().getStatus());
    }

    List<TenantServicePackRelationItemVO> convertVos(List<TenantTransmissionServicePackRelationDO> relationDOS);

    default List<TenantServicePackRelationItemVO> convertChangeList(TenantServicePackRelationBatchChangeVO changeVO) {
        return changeVO.getTenantIds().stream().map(t -> new TenantServicePackRelationItemVO().setTenantId(t)
            .setServicePackId(changeVO.getServicePackId())
            .setCatalogId(changeVO.getCatalogId())
            .setOrganType(changeVO.getOrganType())
            .setExt(changeVO.getExt())
            .setStatus(TenantServicePackRelationStatusEnum.OPEN.getCode())).toList();

    }

    TenantTransmissionServicePackRelationDO convertDo2Do(TenantTransmissionServicePackRelationDO s);

    List<TenantServicePackRelationRespVO> convertRelationVos(List<TenantRespVO> records);

    @Mapping(target = "tenantId", source = "id")
    TenantServicePackRelationRespVO convertRelationVo(TenantRespVO record);


    TenantServicePackRelationDto convertDo2Dto(TenantTransmissionServicePackRelationDO packRelationDO);
}
