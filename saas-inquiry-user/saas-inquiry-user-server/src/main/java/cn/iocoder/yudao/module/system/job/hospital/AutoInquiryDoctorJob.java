package cn.iocoder.yudao.module.system.job.hospital;

import cn.iocoder.yudao.framework.quartz.core.handler.JobHandler;
import com.xyy.saas.inquiry.generic.api.InquiryGenericApiService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class AutoInquiryDoctorJob implements JobHandler {

    @Resource
    private InquiryGenericApiService inquiryGenericApiService;

    @Override
    public String execute(String param) {
        log.info("定时任务处理自动开方医生 出停诊 - 开始");
        inquiryGenericApiService.jobHandAutoInquiryDoctor();
        log.info("定时任务处理自动开方医生 出停诊 - 结束");
        return "";
    }

}
