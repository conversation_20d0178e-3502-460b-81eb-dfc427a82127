package cn.iocoder.yudao.module.system.job.pharmacist;

import cn.iocoder.yudao.framework.quartz.core.handler.JobHandler;
import com.xyy.saas.inquiry.generic.api.InquiryGenericApiService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class PharmacistOffLineJob implements JobHandler {

    @Resource
    private InquiryGenericApiService inquiryGenericApiService;

    @Override
    public String execute(String param) {
        log.info("定时任务处理药师 停诊 - 开始");
        inquiryGenericApiService.jobHandPharmacistOffline();
        log.info("定时任务处理药师 停诊 - 结束");
        return "";
    }

}
