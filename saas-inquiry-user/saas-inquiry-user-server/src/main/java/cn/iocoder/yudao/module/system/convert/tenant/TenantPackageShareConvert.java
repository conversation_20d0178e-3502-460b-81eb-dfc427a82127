package cn.iocoder.yudao.module.system.convert.tenant;

import cn.iocoder.yudao.module.system.api.tenant.dto.TenantPackageShareRelationDto;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantPackageShareRelationPageReqDto;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantPackageShareRelationRespDto;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.packages.share.TenantPackageShareRelationPageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.packages.share.TenantPackageShareRelationRespVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.packages.share.TenantPackageShareRelationSaveReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantDO;
import cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantPackageShareRelationDO;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface TenantPackageShareConvert {

    TenantPackageShareConvert INSTANCE = Mappers.getMapper(TenantPackageShareConvert.class);


    TenantPackageShareRelationRespVO convert(TenantPackageShareRelationDO s);

    TenantPackageShareRelationPageReqVO convert(TenantPackageShareRelationSaveReqVO createReqVO);

    default List<TenantPackageShareRelationDO> convertDos(TenantPackageShareRelationSaveReqVO createReqVO) {
        return createReqVO.getTenantIds().stream().map(tenantId -> {
            TenantPackageShareRelationDO shareRelationDO = new TenantPackageShareRelationDO();
            shareRelationDO.setHeadTenantId(createReqVO.getHeadTenantId());
            shareRelationDO.setTenantId(tenantId);
            shareRelationDO.setTenantPackageId(createReqVO.getTenantPackageId());
            shareRelationDO.setBizType(createReqVO.getBizType());
            return shareRelationDO;
        }).toList();
    }

    List<TenantPackageShareRelationDto> convertDo2Dto(List<TenantPackageShareRelationDO> shareRelationList);

    default void convertFill(TenantPackageShareRelationRespVO respVO, TenantDO tenantDO) {
        if (tenantDO == null) {
            return;
        }
        respVO.setTenantName(tenantDO.getName());
        respVO.setTenantPref(tenantDO.getPref());
        respVO.setContactName(tenantDO.getContactName());
        respVO.setContactMobile(tenantDO.getContactMobile());
        respVO.setProvince(tenantDO.getProvince());
        respVO.setProvinceCode(tenantDO.getProvinceCode());
        respVO.setCity(tenantDO.getCity());
        respVO.setCityCode(tenantDO.getCityCode());
        respVO.setArea(tenantDO.getArea());
        respVO.setAreaCode(tenantDO.getAreaCode());
        respVO.setAddress(tenantDO.getAddress());
    }

    TenantPackageShareRelationPageReqVO convertDto2Vo(TenantPackageShareRelationPageReqDto pageReqDto);

    List<TenantPackageShareRelationRespDto> convertVo2Dto(List<TenantPackageShareRelationRespVO> list);
}
