package cn.iocoder.yudao.module.system.job.pharmacist;

import cn.iocoder.yudao.framework.quartz.core.handler.JobHandler;
import com.xyy.saas.inquiry.generic.api.InquiryGenericApiService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class SaasMigrationJob implements JobHandler {

    @Resource
    private InquiryGenericApiService inquiryGenericApiService;

    @Override
    public String execute(String param) {
        log.info("定时任务处理老系统迁移 - 开始");
        inquiryGenericApiService.jobHandSaasMigration();
        log.info("定时任务处理老系统迁移 - 结束");
        return "";
    }

}
