package cn.iocoder.yudao.module.system.service.tenant;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantUserClockInDto;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantUserRelationDto;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant.TenantUserRelationUpdateVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant.TenantUserRelationPageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant.TenantUserRelationRespVO;
import cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantUserRelationDO;
import com.xyy.saas.inquiry.enums.system.RoleCodeEnum;
import jakarta.validation.Valid;
import java.util.List;

/**
 * 门店员工关系 Service 接口
 *
 * <AUTHOR>
 */
public interface TenantUserRelationService {

    /**
     * 创建门店员工关系
     *
     * @param relationDto 关系dto
     * @return
     */
    Long createTenantUserRelation(@Valid TenantUserRelationDto relationDto);


    /**
     * 修改门店员工表
     *
     * @param tenantUserRelationDto
     */
    void updateTenantUserRelation(TenantUserRelationDto tenantUserRelationDto);


    /**
     * 删除门店员工关系
     *
     * @param id 员工id
     */
    void deleteTenantUserRelation(Long userId);

    /**
     * 获得门店员工关系
     *
     * @param id 编号
     * @return 门店员工关系
     */
    TenantUserRelationDO getTenantUserRelation(Long id);

    /**
     * 获取门店员工关系
     *
     * @param userId   userid
     * @param tenantId 门店好号
     * @return
     */
    TenantUserRelationDO getTenantUserRelation(Long userId, Long tenantId);

    /**
     * 根据userId获取所有门店列表
     *
     * @param userId 用户id
     * @return 门店列表
     */
    List<TenantUserRelationDO> getTenantListByUserId(Long userId);

    /**
     * 根据userId获取可用的门店列表
     *
     * @param userId 用户id
     * @return 门店列表
     */
    List<TenantUserRelationDO> getAvailableTenantListByUserId(Long userId);

    /**
     * 根据手机号获取门店员工关系
     *
     * @param mobile
     * @return
     */
    TenantUserRelationDO getTenantUserRelationByMobile(String mobile);

    /**
     * 查询门店对应user关系总数
     *
     * @return
     */
    long selectCount();

    /**
     * 查用户门店关系表
     *
     * @param reqVO user信息
     * @return
     */
    PageResult<TenantUserRelationRespVO> pageTenantUserRelation(TenantUserRelationPageReqVO reqVO);

    /**
     * 获取当前门店下有某角色的用户列表
     *
     * @param roleCodeEnum 角色Code
     * @return
     */
    List<TenantUserRelationDto> getTenantUserRelationsByRoleCode(RoleCodeEnum roleCodeEnum);

    /**
     * 获取当前用户 在哪些门店，有roleCodeEnum角色
     *
     * @param roleCodeEnum 角色Code
     * @return
     */
    List<TenantUserRelationDto> getUserTenantRelationsByRoleCode(RoleCodeEnum roleCodeEnum);

    /**
     * 更新员工关系状态
     *
     * @param id     关系表id
     * @param status 状态
     */
    void updateTenantUserRelationStatus(Long id, Integer status);

    /**
     * 批量更新员工状态
     *
     * @param ids    关系表ids
     * @param status 状态
     */
    void updateTenantUserRelationStatus(List<Long> ids, Integer status);

    /**
     * 获取用户绑定门店关系 如果不存在获取user
     *
     * @param userId
     * @return
     */
    TenantUserRelationDO getUserBindTenantRelationDo(Long userId);

    /**
     * 用户绑定门店
     *
     * @param bindVO
     * @return
     */
    void bindTenant(TenantUserRelationUpdateVO bindVO);

    /**
     * 用户解绑门店
     *
     * @param bindVO
     * @return
     */
    void unBindTenant(TenantUserRelationUpdateVO bindVO);

    /**
     * 验证用户关系是否存在
     *
     * @param id 行id
     * @return
     */
    TenantUserRelationDO validateUserRelationExists(Long id);

    /**
     * 重新绑定用户关系
     *
     * @param userId
     * @param bindTenantIds
     */
    void reBindNewTenantUserRelation(TenantUserRelationUpdateVO bindVO);

    /**
     * 修改打卡状态
     *
     * @param relationBindVO
     */
    void updateNeedClockIn(TenantUserRelationUpdateVO relationUpdateVO);

    /**
     * 门店用户打卡
     *
     * @param clockInDto
     */
    Long tenantUserClockIn(@Valid TenantUserClockInDto clockInDto);

    /**
     * 获取门店用户关系
     *
     * @param tenantUserRelationPageReqVO
     * @return
     */
    List<TenantUserRelationDO> getByCondition(TenantUserRelationPageReqVO tenantUserRelationPageReqVO);
}