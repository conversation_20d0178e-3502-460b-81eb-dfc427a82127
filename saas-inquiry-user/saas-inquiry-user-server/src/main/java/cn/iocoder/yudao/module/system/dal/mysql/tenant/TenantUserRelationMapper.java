package cn.iocoder.yudao.module.system.dal.mysql.tenant;

import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantUserRelationDto;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant.TenantUserRelationPageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant.TenantUserRelationRespVO;
import cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantUserRelationDO;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xyy.saas.inquiry.enums.user.UserStatusEnum;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 门店员工关系 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface TenantUserRelationMapper extends BaseMapperX<TenantUserRelationDO> {

    default List<TenantUserRelationDO> selectListByUserId(Long userId) {
        return selectList(new LambdaQueryWrapperX<TenantUserRelationDO>().eq(TenantUserRelationDO::getUserId, userId));
    }

    default List<TenantUserRelationDO> selectByTenantUserId(Long userId) {
        return selectList(new LambdaQueryWrapperX<TenantUserRelationDO>().eq(TenantUserRelationDO::getUserId, userId));
    }

    @TenantIgnore
    default TenantUserRelationDO selectByTenantUserId(Long userId, Long tenantId) {
        return selectOne(new LambdaQueryWrapperX<TenantUserRelationDO>().eq(TenantUserRelationDO::getUserId, userId)
            .eq(tenantId != null, TenantUserRelationDO::getTenantId, tenantId));
    }

    default List<TenantUserRelationDO> selectAvailableListByUserId(Long userId) {
        return selectList(new LambdaQueryWrapperX<TenantUserRelationDO>().eq(TenantUserRelationDO::getUserId, userId).eq(TenantUserRelationDO::getStatus, UserStatusEnum.ENABLE.getCode()));
    }

    default TenantUserRelationDO getTenantUserRelationByMobile(String mobile) {
        return selectOne(TenantUserRelationDO::getMobile, mobile);
    }


    IPage<TenantUserRelationRespVO> pageTenantUserRelation(Page<TenantUserRelationRespVO> objectPage, TenantUserRelationPageReqVO pageReqVO);

    List<TenantUserRelationDto> getTenantUserRelationsByRoleCode(@Param("tenantId") Long tenantId, @Param("userId") Long userId, @Param("roleId") Long roleId);

    /**
     * 获取用户关联的门店且存在角色
     *
     * @param tenantId getAvailableUserTenantRelationsByRoleCode
     * @param userId
     * @param roleId
     * @return
     */
    List<TenantUserRelationDto> getAvailableUserTenantRelationsByRoleCode(@Param("userId") Long userId, @Param("roleId") Long roleId);

    default void updateTenantUserRelationStatus(Long id, Integer status) {
        updateById(new TenantUserRelationDO().setId(id).setStatus(status));
    }

    default void updateTenantUserRelationStatus(List<Long> ids, Integer status) {
        update(new LambdaUpdateWrapper<TenantUserRelationDO>().set(TenantUserRelationDO::getStatus, status).in(TenantUserRelationDO::getId, ids));
    }


    @TenantIgnore
    default List<TenantUserRelationDO> selectByTenantsUserId(Long userId, List<Long> tenantIds) {
        return selectList(new LambdaQueryWrapperX<TenantUserRelationDO>().eq(TenantUserRelationDO::getUserId, userId)
            .inIfPresent(TenantUserRelationDO::getTenantId, tenantIds));
    }

    @TenantIgnore
    default List<TenantUserRelationDO> selectByTenantsId(Long tenantId) {
        return selectList(new LambdaQueryWrapperX<TenantUserRelationDO>()
            .eq(TenantUserRelationDO::getTenantId, tenantId));
    }

    @TenantIgnore
    default List<TenantUserRelationDO> selectByCondition(TenantUserRelationPageReqVO tenantUserRelationPageReqVO) {
        return selectList(new LambdaQueryWrapperX<TenantUserRelationDO>()
            .inIfPresent(TenantUserRelationDO::getTenantId, tenantUserRelationPageReqVO.getTenantIds())
            .eqIfPresent(TenantUserRelationDO::getStatus, tenantUserRelationPageReqVO.getStatus()));
    }

}