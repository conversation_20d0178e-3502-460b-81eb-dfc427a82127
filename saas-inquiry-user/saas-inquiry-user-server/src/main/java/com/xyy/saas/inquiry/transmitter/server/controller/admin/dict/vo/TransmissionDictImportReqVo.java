package com.xyy.saas.inquiry.transmitter.server.controller.admin.dict.vo;

import com.xyy.saas.inquiry.pojo.excel.ImportReqDto;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * @Author:chenxiaoyi
 * @Date:2025/01/22 15:49
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TransmissionDictImportReqVo extends ImportReqDto {

    @NotNull(message = "服务商不能为空")
    Integer organId;

    @NotBlank(message = "字典类型不能为空")
    String dictValue;

}
