package com.xyy.saas.inquiry.transmitter.server.convert.servicepack;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantServicePackRelationDto;
import com.baomidou.mybatisplus.core.metadata.IPage;
import java.util.List;
import com.xyy.saas.inquiry.transmitter.api.servicepack.dto.TenantTransmissionServicePackRespDTO;
import com.xyy.saas.inquiry.transmitter.api.servicepack.dto.TransmissionServicePackDTO;
import com.xyy.saas.inquiry.transmitter.server.controller.admin.servicepack.vo.TransmissionServicePackPageReqVO;
import com.xyy.saas.inquiry.transmitter.server.controller.admin.servicepack.vo.TransmissionServicePackRespVO;
import com.xyy.saas.inquiry.transmitter.server.controller.admin.servicepack.vo.TransmissionServicePackSaveReqVO;
import com.xyy.saas.inquiry.transmitter.server.convert.config.TransmissionConfigPackageConvert;
import com.xyy.saas.inquiry.transmitter.server.convert.organ.TransmissionOrganConvert;
import com.xyy.saas.inquiry.transmitter.server.dal.dataobject.organ.TransmissionOrganDO;
import com.xyy.saas.inquiry.transmitter.server.dal.dataobject.servicepack.TransmissionServicePackDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper(uses = {TransmissionOrganConvert.class, TransmissionConfigPackageConvert.class})
public interface TransmissionServicePackConvert {

    TransmissionServicePackConvert INSTANCE = Mappers.getMapper(TransmissionServicePackConvert.class);

    /**
     * DO 转 DTO
     */
    @Mapping(target = "organ", ignore = true)
    @Mapping(target = "configPackage", ignore = true)
    TransmissionServicePackDTO convert2DTO(TransmissionServicePackDO bean);

    /**
     * DO 列表转 DTO 列表
     */
    List<TransmissionServicePackDTO> convert2DTOList(List<TransmissionServicePackDO> list);

    /**
     * respDTO 转 DTO
     */
    @Mapping(target = "organ", ignore = true)
    @Mapping(target = "configPackage", ignore = true)
    TransmissionServicePackDTO convertRespDTO2DTO(TenantTransmissionServicePackRespDTO bean);


    /**
     * respDTO 列表转 DTO 列表
     */
    List<TransmissionServicePackDTO> convertRespDTO2DTOList(List<TenantTransmissionServicePackRespDTO> list);


    List<TenantTransmissionServicePackRespDTO> convert(List<TransmissionServicePackDO> servicepackDOList);

    TransmissionServicePackRespVO convert(TransmissionServicePackDO bean);

    /**
     * SaveReqVO 转换为 DO
     */
    TransmissionServicePackDO convert(TransmissionServicePackSaveReqVO bean);

    List<TransmissionServicePackRespVO> convert2RespVO(List<TransmissionServicePackDO> servicepackDOList);


    /**
     * DO 分页转换为 RespVO 分页
     */
    default PageResult<TransmissionServicePackRespVO> convertPage(IPage<TransmissionServicePackDO> page) {
        if (page == null) {
            return null;
        }
        PageResult<TransmissionServicePackRespVO> result = new PageResult<>();
        result.setTotal(page.getTotal());
        result.setList(convert2RespVO(page.getRecords()));
        return result;
    }

    /**
     * 从机构信息中复制地区信息到服务包
     */
    default TransmissionServicePackDO copyAreaInfo(TransmissionServicePackDO servicePack, TransmissionOrganDO organ) {
        if (servicePack == null || organ == null) {
            return servicePack;
        }
        servicePack.setProvinceCode(organ.getProvinceCode());
        servicePack.setProvince(organ.getProvince());
        servicePack.setCityCode(organ.getCityCode());
        servicePack.setCity(organ.getCity());
        servicePack.setAreaCode(organ.getAreaCode());
        servicePack.setArea(organ.getArea());
        return servicePack;
    }

    @Mapping(target = "createTime", ignore = true)
    TransmissionServicePackPageReqVO convert(TransmissionServicePackDTO packDTO);

    TenantTransmissionServicePackRespDTO convertRespDto(TransmissionServicePackDO servicePackDO);

    List<TenantServicePackRelationDto> convertTenantServicePackRelationDto(List<TenantTransmissionServicePackRespDTO> servicePacks);
}
