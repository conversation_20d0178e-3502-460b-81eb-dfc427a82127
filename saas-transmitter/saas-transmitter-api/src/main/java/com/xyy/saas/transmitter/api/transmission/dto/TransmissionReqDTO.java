package com.xyy.saas.transmitter.api.transmission.dto;

import cn.iocoder.yudao.framework.common.util.json.JsonUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.json.JsonMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.xyy.saas.inquiry.enums.transmitter.NodeTypeEnum;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class TransmissionReqDTO implements Serializable {


    /**
     * 服务节点
     */
    @NotNull(message = "业务节点信息不能为空")
    private TransmissionConfigReqDTO config;

    /**
     * 入参 对象 {@link NodeTypeEnum} reqClazz
     */
    private Object data;

    /**
     * 任务ID
     */
    private Long taskId;

    /**
     * 传输服务 请求入参业务扩展map
     */
    private Map<String, Object> aux = new HashMap<>();

    /**
     * 姓名
     */
    private String fullName;

    /**
     * 身份证号
     */
    private String idCard;

    /**
     * 业务单号
     */
    private String businessNo;

    /**
     * 是否异步处理
     */
    @Builder.Default
    private boolean async = false;

    /**
     * 是否重传，默认为false
     */
    @Builder.Default
    private boolean retry = false;

    public static ObjectMapper mapper ;

    private static final DateTimeFormatter ISO_FORMATTER =
        DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss");
    static {
        mapper= new ObjectMapper();

        // 配置Java时间模块
        JavaTimeModule timeModule = new JavaTimeModule();

        // 1. 配置LocalDateTime的反序列化（JSON → Object）
        timeModule.addDeserializer(LocalDateTime.class,
            new LocalDateTimeDeserializer(ISO_FORMATTER));

        // 2. 配置LocalDateTime的序列化（Object → JSON）
        timeModule.addSerializer(LocalDateTime.class,
            new LocalDateTimeSerializer(ISO_FORMATTER));

        // 注册时间模块
        mapper.registerModule(timeModule);

        // 其他重要配置
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        mapper.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
    }

    /**
     * 下游节点
     */
    private List<TransmissionReqDTO> downstreamReqList = new ArrayList<>();

    @JsonIgnore
    public List<TransmissionReqDTO> downstreamReqListGet() {
        if (downstreamReqList == null) {
            downstreamReqList = new ArrayList<>();
        }
        return downstreamReqList;
    }


    public String getOriginalParamsJson() {
        return JSON.toJSONString(getOriginalParams());
    }

    public Map<String, Object> getOriginalParams() {
        Map<String, Object> originalParams = new HashMap<>();
        originalParams.put("data", data);
        originalParams.put("aux", aux);
        return originalParams;
    }


    public String getAuxJson() {
        return JSON.toJSONString(data);
    }

    public <T> T getDataObj(Class<T> clazz) {
        if (data == null) {
            return null;
        }
        return JsonUtils.parseObject(JsonUtils.toJsonString(data), clazz);
    }

    public static TransmissionReqDTO buildReq(TransmissionConfigReqDTO config, Object data) {
        return new TransmissionReqDTO().setConfig(config).setData(data);
    }

}