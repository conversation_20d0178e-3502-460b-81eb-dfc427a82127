<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xyy.saas.transmitter.server.dal.mysql.servicepack.TransmissionServicePackMapper">

  <select id="selectTransmissionServicePackPage" resultType="com.xyy.saas.transmitter.server.dal.dataobject.servicepack.TransmissionServicePackDO">
    SELECT
    p.*
    FROM saas_transmission_service_pack p
    <where>
      p.deleted = 0
      <if test="reqVO.organType != null">
        AND p.organ_type = #{reqVO.organType}
      </if>
      <if test="reqVO.bizType != null">
        AND p.biz_type = #{reqVO.bizType}
      </if>
      <if test="reqVO.organId != null">
        AND p.organ_id = #{reqVO.organId}
      </if>
      <if test="reqVO.configPackageId != null">
        AND p.config_package_id = #{reqVO.configPackageId}
      </if>
      <if test="reqVO.name != null and reqVO.name != ''">
        AND p.name LIKE CONCAT('%', #{reqVO.name}, '%')
      </if>
      <if test="reqVO.version != null">
        AND p.version = #{reqVO.version}
      </if>
      <if test="reqVO.disable != null">
        AND p.disable = #{reqVO.disable}
      </if>
      <if test="reqVO.env != null">
        AND p.env = #{reqVO.env}
      </if>
      <if test="reqVO.createTime != null and reqVO.createTime.length == 2">
        AND p.create_time BETWEEN #{reqVO.createTime[0]} AND #{reqVO.createTime[1]}
      </if>
      <if test="reqVO.provinceCode != null and reqVO.provinceCode != ''">
        AND exists ( select 1 from saas_transmission_organ i where p.organ_id = i.id
        AND i.province_code = #{reqVO.provinceCode}
        <if test="reqVO.cityCode != null and reqVO.cityCode != ''">
          AND i.city_code = #{reqVO.cityCode}
        </if>
        <if test="reqVO.areaCode != null and reqVO.areaCode != ''">
          AND i.area_code = #{reqVO.areaCode}
        </if>
        )
      </if>
    </where>
    ORDER BY p.id DESC
  </select>


</mapper> 