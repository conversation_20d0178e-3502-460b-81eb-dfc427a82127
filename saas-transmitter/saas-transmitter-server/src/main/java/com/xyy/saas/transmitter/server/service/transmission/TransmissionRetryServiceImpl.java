package com.xyy.saas.transmitter.server.service.transmission;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.json.JsonUtils;
import com.xyy.saas.inquiry.enums.transmitter.RequestStatusEnum;
import com.xyy.saas.transmitter.api.servicepack.dto.TransmissionServicePackDTO;
import com.xyy.saas.transmitter.api.transmission.dto.TransmissionConfigReqDTO;
import com.xyy.saas.transmitter.api.transmission.dto.TransmissionReqDTO;
import com.xyy.saas.transmitter.server.controller.admin.servicepack.vo.TransmissionServicePackPageReqVO;
import com.xyy.saas.transmitter.server.controller.admin.task.vo.TransmissionTaskRecordPageReqVO;
import com.xyy.saas.transmitter.server.controller.admin.task.vo.TransmissionTaskRecordRespVO;
import com.xyy.saas.transmitter.server.controller.admin.task.vo.TransmissionTaskRetryReqVO;
import com.xyy.saas.transmitter.server.convert.servicepack.TransmissionServicePackConvert;
import com.xyy.saas.transmitter.server.convert.transmission.TransmissionConvert;
import com.xyy.saas.transmitter.server.dal.mysql.servicepack.TransmissionServicePackMapper;
import com.xyy.saas.transmitter.server.service.task.TransmissionTaskRecordService;
import jakarta.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * @Author:chenxiaoyi
 * @Date:2025/04/01 16:41
 */
@Service
@Slf4j
public class TransmissionRetryServiceImpl implements TransmissionRetryService {

    @Resource
    private TransmissionService transmissionService;

    @Resource
    private TransmissionServicePackMapper servicePackMapper;

    @Resource
    private TransmissionTaskRecordService transmissionTaskRecordService;

    /**
     * 批量size
     */
    private static final Integer BATCH_SIZE = 100;


    @Override
    public void retryContractInvokeAsync(TransmissionTaskRetryReqVO taskRetryReqVO) {

        TransmissionTaskRecordPageReqVO taskRecordPageReqVO = TransmissionConvert.INSTANCE.convertRetry(taskRetryReqVO);
        taskRecordPageReqVO.setRequestStatusList(List.of(RequestStatusEnum.NOT_REQUESTED.getCode(), RequestStatusEnum.FAILED.getCode()));
        log.info("[retryContractInvokeAsync]重传,taskRecordPageReqVO:{}", taskRecordPageReqVO);

        if (CollUtil.isNotEmpty(taskRecordPageReqVO.getIds())) {
            // 指定ids重传
            retryContractInvoke(taskRecordPageReqVO);
        } else {
            // 分批异步重传
            retryContractInvokeBatch(taskRecordPageReqVO);
        }
    }

    @Override
    public <T> CommonResult<T> retryContractInvokeSync(Long taskId, Class<T> clazz) {
        TransmissionTaskRecordRespVO taskRecord = transmissionTaskRecordService.getTransmissionTaskRecord(taskId);
        // 1.构建dto
        TransmissionReqDTO reqDTO = TransmissionReqDTO.builder().taskId(taskRecord.getId()).build();
        // 2. 填充请求参数
        fillRetryRequestParams(reqDTO, taskRecord);
        // 3. 执行重传请求
        return transmissionService.contractInvoke(reqDTO, clazz);
    }

    @Async("transmissionAsyncExecutor")
    public void retryContractInvoke(TransmissionTaskRecordPageReqVO taskRecordPageReqVO) {

        List<TransmissionTaskRecordRespVO> list = transmissionTaskRecordService.getTransmissionTaskRecordList(taskRecordPageReqVO);
        // 异步处理任务
        retryTaskSync(list);
    }

    // 辅助方法：获取服务包信息映射
   private Map<Integer, TransmissionServicePackDTO> getServicePackMap(List<Integer> servicePackIds) {
        return TransmissionServicePackConvert.INSTANCE.convert2DTOList(
                servicePackMapper.selectList(TransmissionServicePackPageReqVO.builder().ids(servicePackIds).build()))
            .stream().collect(Collectors.toMap(TransmissionServicePackDTO::getId, servicePackDTO -> servicePackDTO));
    }

    @Override
    public CommonResult<?> pushContractInvoke(TransmissionTaskRetryReqVO taskRetryReqVO) {
        if(CollUtil.isEmpty(taskRetryReqVO.getIds()) || CollUtil.size(taskRetryReqVO.getIds()) > 1) {
            return CommonResult.error("入参异常！");
        }
        TransmissionTaskRecordRespVO taskRecord = transmissionTaskRecordService.getTransmissionTaskRecord(taskRetryReqVO.getIds().getFirst());
        if(taskRecord == null){
            return CommonResult.error("未查询到任务！");
        }
        //推送前需先推送父级任务
        if(taskRecord.getUpstreamTaskId() != null && taskRecord.getUpstreamTaskId() >0){
            TransmissionTaskRecordRespVO parentTask = transmissionTaskRecordService.getTransmissionTaskRecord(taskRecord.getUpstreamTaskId());
            if(parentTask == null){
                return CommonResult.error("上游任务查询异常！");
            }
            if(!Objects.equals(parentTask.getRequestStatus(),RequestStatusEnum.SUCCESS.getCode())){
                //查询服务包名称
                TransmissionServicePackDTO servicePack = getServicePackMap(List.of(parentTask.getServicePackId())).getOrDefault(parentTask.getServicePackId(),TransmissionServicePackDTO.builder().build());
                return CommonResult.error("请先将【"+servicePack.getName()+"-业务单号："+ parentTask.getBusinessNo()+"】的数据上传后，再推送此数据！");
            }
        }
        try {
            // 1.构建dto
            TransmissionReqDTO reqDTO = TransmissionReqDTO.builder().taskId(taskRecord.getId()).build();
            // 2. 填充请求参数
            fillRetryRequestParams(reqDTO, taskRecord);
            // 3. 执行重传请求
            CommonResult<Map> result = transmissionService.contractInvoke(reqDTO, Map.class);
            if(result.isSuccess()){
                return CommonResult.success("推送成功！");
            }
            return CommonResult.error(result.getMsg());
        } catch (Exception e) {
            log.error("[retryContractInvokeAsync][任务推送失败] taskId:{} error:{}", taskRecord.getId(), e.getMessage(), e);
            return CommonResult.error(e.getMessage());
        }
    }

    /**
     * 分批重传
     *
     * @param taskRecordPageReqVO
     */
    @Async("transmissionAsyncExecutor")
    public void retryContractInvokeBatch(TransmissionTaskRecordPageReqVO taskRecordPageReqVO) {

        taskRecordPageReqVO.setPageNo(1);
        taskRecordPageReqVO.setPageSize(BATCH_SIZE);
        // 分批重传
        PageResult<TransmissionTaskRecordRespVO> recordPage = transmissionTaskRecordService.getTaskRecordPage(taskRecordPageReqVO);
        if (CollUtil.isEmpty(recordPage.getList())) {
            return;
        }
        // 重传第一批
        retryTaskSync(recordPage.getList());
        // 仅有第一批 return
        Long total = recordPage.getTotal();
        if (Objects.equals(recordPage.getList().size(), total.intValue())) {
            return;
        }

        // 跳过第一页
        total = total - BATCH_SIZE;
        // 计算重传分页size
        long totalPage = total % BATCH_SIZE == 0 ? (total / BATCH_SIZE) : ((total / BATCH_SIZE) + 1);

        for (int i = 0; i < totalPage; i++) {
            log.info("[retryContractInvokeBatch]批量重传任务,i={} totalPage={}", i + 1, totalPage);
            retryTaskSync(transmissionTaskRecordService.getTaskRecordPage(taskRecordPageReqVO).getList());
        }
    }

    /**
     * 重传任务，同步，一个线程慢慢传
     *
     * @param recordDOList 重传任列表
     */
    private void retryTaskSync(List<TransmissionTaskRecordRespVO> recordDOList) {
        if (CollUtil.isEmpty(recordDOList)) {
            return;
        }
        // 异步处理每个任务
        recordDOList.forEach(t -> {
            try {
                // 1.构建dto
                TransmissionReqDTO reqDTO = TransmissionReqDTO.builder().taskId(t.getId()).build();
                // 2. 填充请求参数
                fillRetryRequestParams(reqDTO, t);
                // 3. 执行重传请求
                transmissionService.contractInvoke(reqDTO, Map.class);
            } catch (Exception e) {
                log.error("[retryContractInvokeAsync][任务重传失败] taskId:{} error:{}", t.getId(), e.getMessage(), e);
            }
        });
    }


    private void fillRetryRequestParams(TransmissionReqDTO transmissionReqDTO,
        TransmissionTaskRecordRespVO originalTask) {
        // 1. 填充基础信息
        transmissionReqDTO.setFullName(originalTask.getFullName());
        transmissionReqDTO.setIdCard(originalTask.getIdCard());
        transmissionReqDTO.setBusinessNo(originalTask.getBusinessNo());
        transmissionReqDTO.setRetry(true);

        // 2. 填充配置信息
        TransmissionConfigReqDTO config = TransmissionConvert.INSTANCE.convertConfig(originalTask);
        transmissionReqDTO.setConfig(config);

        // 3. 解析并填充请求参数
        if (StringUtils.isNotBlank(originalTask.getOriginalParams())) {
            Map<String, Object> requestParams = JsonUtils.parseObject(originalTask.getOriginalParams(), Map.class);
            if (requestParams != null) {
                transmissionReqDTO.setData(requestParams.get("data"));
                Object aux = requestParams.get("aux");
                if (aux instanceof Map) {
                    transmissionReqDTO.setAux((Map<String, Object>) aux);
                } else {
                    transmissionReqDTO.setAux(new HashMap<>());
                }
            }
        } else {
            // 如果requestParams为空，设置默认空Map
            transmissionReqDTO.setAux(new HashMap<>());
        }
    }


}
