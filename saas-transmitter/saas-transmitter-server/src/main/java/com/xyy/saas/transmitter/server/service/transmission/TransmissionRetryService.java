package com.xyy.saas.transmitter.server.service.transmission;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import com.xyy.saas.transmitter.server.controller.admin.task.vo.TransmissionTaskRetryReqVO;

/**
 * 数据传输服务 - 数据重传服务
 *
 * <AUTHOR>
 */
public interface TransmissionRetryService {

    /**
     * 异步重传任务
     *
     * @param taskRetryReqVO
     */
    void retryContractInvokeAsync(TransmissionTaskRetryReqVO taskRetryReqVO);

    /**
     * 同步重传任务
     *
     * @param taskId 任务id
     * @param clazz
     * @param <T>
     * @return
     */
    <T> CommonResult<T> retryContractInvokeSync(Long taskId, Class<T> clazz);

    /**
     * 推送任务
     *
     * @param taskRetryReqVO
     * @return
     */
    CommonResult<?> pushContractInvoke(TransmissionTaskRetryReqVO taskRetryReqVO);
}