package detector.cycle;

import java.util.*;

/**
 * 测试修复后的循环检测器
 */
public class CycleDetectorFixTest {
    
    public static void main(String[] args) {
        System.out.println("=== 循环检测器修复验证测试 ===\n");
        
        // 测试1: 简单的真实循环
        testSimpleCycle();
        
        // 测试2: 复杂的强连通分量但无真实循环
        testComplexSCCWithoutCycle();
        
        // 测试3: 混合情况
        testMixedCase();
    }
    
    private static void testSimpleCycle() {
        System.out.println("测试1: 简单的真实循环 A -> B -> C -> A");
        
        Map<String, Set<String>> graph = new HashMap<>();
        graph.put("A", Set.of("B"));
        graph.put("B", Set.of("C"));
        graph.put("C", Set.of("A"));
        graph.put("D", Set.of("E"));
        graph.put("E", Set.of());
        
        List<List<String>> cycles = new EnhancedCycleDetector.OptimizedCycleDetector(graph).findCycles();
        
        System.out.println("发现的循环:");
        for (List<String> cycle : cycles) {
            System.out.println("  " + cycle);
        }
        System.out.println("预期: 1个循环 [A, B, C]\n");
    }
    
    private static void testComplexSCCWithoutCycle() {
        System.out.println("测试2: 复杂的强连通分量但无真实循环");
        System.out.println("图结构: A -> B, A -> C, B -> D, C -> D, D -> A (但不形成简单循环)");
        
        Map<String, Set<String>> graph = new HashMap<>();
        graph.put("A", Set.of("B", "C"));
        graph.put("B", Set.of("D"));
        graph.put("C", Set.of("D"));
        graph.put("D", Set.of("A"));
        
        List<List<String>> cycles = new EnhancedCycleDetector.OptimizedCycleDetector(graph).findCycles();
        
        System.out.println("发现的循环:");
        for (List<String> cycle : cycles) {
            System.out.println("  " + cycle);
        }
        System.out.println("预期: 应该发现真实的循环路径\n");
    }
    
    private static void testMixedCase() {
        System.out.println("测试3: 混合情况 - 包含真实循环和非循环SCC");
        
        Map<String, Set<String>> graph = new HashMap<>();
        // 真实循环: X -> Y -> X
        graph.put("X", Set.of("Y"));
        graph.put("Y", Set.of("X"));
        
        // 复杂但非循环的连接
        graph.put("P", Set.of("Q", "R"));
        graph.put("Q", Set.of("S"));
        graph.put("R", Set.of("S"));
        graph.put("S", Set.of("P"));
        
        // 独立节点
        graph.put("Z", Set.of());
        
        List<List<String>> cycles = new EnhancedCycleDetector.OptimizedCycleDetector(graph).findCycles();
        
        System.out.println("发现的循环:");
        for (List<String> cycle : cycles) {
            System.out.println("  " + cycle);
        }
        System.out.println("预期: 应该发现真实的循环，而不是所有SCC\n");
    }
}
