package detector.cycle;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 循环检测器性能测试
 */
public class CycleDetectorPerformanceTest {
    
    public static void main(String[] args) {
        System.out.println("=== 循环检测器性能对比测试 ===\n");
        
        // 测试不同规模的图
        int[] sizes = {50, 100, 200, 327, 500};
        
        for (int size : sizes) {
            System.out.printf("测试图规模: %d 个节点\n", size);
            System.out.println("-".repeat(50));
            
            Map<String, Set<String>> testGraph = generateTestGraph(size);
            
            // 测试原始的TarjanCycleDetector2
            testDetector("TarjanCycleDetector2", testGraph, () -> 
                new EnhancedCycleDetector.TarjanCycleDetector2(testGraph).findCycles());
            
            // 测试优化的OptimizedCycleDetector
            testDetector("OptimizedCycleDetector", testGraph, () -> 
                new EnhancedCycleDetector.OptimizedCycleDetector(testGraph).findCycles());
            
            System.out.println();
        }
    }
    
    private static void testDetector(String name, Map<String, Set<String>> graph, 
                                   Runnable detector) {
        System.out.printf("测试 %s:\n", name);
        
        long startTime = System.nanoTime();
        long startMemory = getUsedMemory();
        
        try {
            detector.run();
            
            long endTime = System.nanoTime();
            long endMemory = getUsedMemory();
            
            long duration = endTime - startTime;
            long memoryUsed = endMemory - startMemory;
            
            System.out.printf("  ✅ 耗时: %d ms\n", TimeUnit.NANOSECONDS.toMillis(duration));
            System.out.printf("  📊 内存: %d KB\n", memoryUsed / 1024);
            
        } catch (Exception e) {
            System.out.printf("  ❌ 执行失败: %s\n", e.getMessage());
        }
    }
    
    private static long getUsedMemory() {
        Runtime runtime = Runtime.getRuntime();
        return runtime.totalMemory() - runtime.freeMemory();
    }
    
    /**
     * 生成测试图 - 包含一些循环依赖
     */
    private static Map<String, Set<String>> generateTestGraph(int nodeCount) {
        Map<String, Set<String>> graph = new HashMap<>();
        Random random = new Random(42); // 固定种子确保可重现
        
        // 初始化所有节点
        for (int i = 0; i < nodeCount; i++) {
            graph.put("Node" + i, new HashSet<>());
        }
        
        // 添加随机边
        for (int i = 0; i < nodeCount; i++) {
            String node = "Node" + i;
            Set<String> dependencies = graph.get(node);
            
            // 每个节点随机连接到2-5个其他节点
            int edgeCount = 2 + random.nextInt(4);
            for (int j = 0; j < edgeCount; j++) {
                int targetIndex = random.nextInt(nodeCount);
                if (targetIndex != i) { // 避免自引用
                    dependencies.add("Node" + targetIndex);
                }
            }
        }
        
        // 人为添加一些循环
        addCycle(graph, Arrays.asList("Node0", "Node1", "Node2"));
        if (nodeCount > 10) {
            addCycle(graph, Arrays.asList("Node5", "Node6", "Node7", "Node8"));
        }
        if (nodeCount > 20) {
            addCycle(graph, Arrays.asList("Node10", "Node11", "Node12", "Node13", "Node14"));
        }
        
        return graph;
    }
    
    private static void addCycle(Map<String, Set<String>> graph, List<String> cycle) {
        for (int i = 0; i < cycle.size(); i++) {
            String current = cycle.get(i);
            String next = cycle.get((i + 1) % cycle.size());
            
            if (graph.containsKey(current)) {
                graph.get(current).add(next);
            }
        }
    }
}
