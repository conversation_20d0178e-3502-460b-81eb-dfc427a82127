package detector.cycle;

import com.tngtech.archunit.core.domain.*;
import com.tngtech.archunit.core.importer.ClassFileImporter;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Lazy;

import java.nio.file.Path;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * desc
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
public class CycleDetectorRefined {

    // 1. 定义包含 @Resource 但排除 @Lazy 的依赖关系收集器
    private static Map<String, Set<String>> getDependencies(JavaClasses classes) {
        Map<String, Set<String>> dependencyMap = new HashMap<>();

        for (JavaClass javaClass : classes) {
            String className = javaClass.getName();
            dependencyMap.putIfAbsent(className, new HashSet<>());

            // 收集字段依赖 (@Resource)
            collectFieldDependencies(javaClass, dependencyMap);

            // 收集构造器依赖
            collectConstructorDependencies(javaClass, dependencyMap);

            // 收集方法依赖
            collectMethodDependencies(javaClass, dependencyMap);
        }

        return dependencyMap;
    }

    private static void collectFieldDependencies(JavaClass javaClass, Map<String, Set<String>> dependencyMap) {
        Set<JavaField> resourceFields = javaClass.getAllFields().stream()
                .filter(f -> f.isAnnotatedWith(Resource.class))
                .collect(Collectors.toSet());

        for (JavaField field : resourceFields) {
            // 排除 @Lazy 注解的字段
            if (!field.isAnnotatedWith(Lazy.class)) {
                JavaType fieldType = field.getRawType();
                if (!fieldType.getName().equals(javaClass.getName())) {
                    dependencyMap.get(javaClass.getName()).add(fieldType.getName());
                }
            }
        }
    }

    private static void collectConstructorDependencies(JavaClass javaClass, Map<String, Set<String>> dependencyMap) {
        for (JavaConstructor constructor : javaClass.getConstructors()) {
            // 检查方法参数是否使用 @Resource 注入
            Set<JavaClass> constructorDependencies = new HashSet<>();
            boolean hasNonLazyResourceParam = false;

            for (JavaParameter parameter : constructor.getParameters()) {
                if (parameter.isAnnotatedWith(Resource.class) && !parameter.isAnnotatedWith(Lazy.class)) {
                    constructorDependencies.add(parameter.getRawType());
                    hasNonLazyResourceParam = true;
                }
            }

            if (hasNonLazyResourceParam) {
                dependencyMap.get(javaClass.getName()).addAll(
                        constructorDependencies.stream()
                                .map(JavaClass::getName)
                                .filter(name -> !name.equals(javaClass.getName()))
                                .collect(Collectors.toSet())
                );
            }
        }
    }

    private static void collectMethodDependencies(JavaClass javaClass, Map<String, Set<String>> dependencyMap) {
        for (JavaMethod method : javaClass.getMethods()) {
            // 检查方法是否使用 @Resource 进行依赖注入
            if (method.isAnnotatedWith(Resource.class)) {
                Set<String> methodDependencies = new HashSet<>();
                boolean hasNonLazyParam = false;

                for (JavaParameter param : method.getParameters()) {
                    if (!param.isAnnotatedWith(Lazy.class)) {
                        methodDependencies.add(param.getRawType().getName());
                        hasNonLazyParam = true;
                    }
                }

                if (hasNonLazyParam) {
                    dependencyMap.get(javaClass.getName()).addAll(
                            methodDependencies.stream()
                                    .filter(name -> !name.equals(javaClass.getName()))
                                    .collect(Collectors.toSet())
                    );
                }
            }
        }
    }

    // 2. 深度优先搜索方法检测环
    private static List<List<String>> findCycles(Map<String, Set<String>> dependencyMap) {
        List<List<String>> cycles = new ArrayList<>();
        Map<String, Boolean> visited = new HashMap<>();
        Map<String, String> parentMap = new HashMap<>();
        Stack<String> stack = new Stack<>();
        Map<String, Boolean> recStack = new HashMap<>();

        for (String node : dependencyMap.keySet()) {
            if (visited.containsKey(node)) continue;
            detectCyclesDFS(node, dependencyMap, visited, recStack, stack, parentMap, cycles);
        }

        return deduplicateCycles(cycles);
    }

    private static void detectCyclesDFS(String current, Map<String, Set<String>> graph,
                                        Map<String, Boolean> visited, Map<String, Boolean> recStack,
                                        Stack<String> stack, Map<String, String> parentMap,
                                        List<List<String>> cycles) {

        visited.put(current, true);
        recStack.put(current, true);
        stack.push(current);

        if (!graph.containsKey(current)) {
            stack.pop();
            recStack.put(current, false);
            return;
        }

        for (String neighbor : graph.get(current)) {
            if (!visited.getOrDefault(neighbor, false)) {
                parentMap.put(neighbor, current);
                detectCyclesDFS(neighbor, graph, visited, recStack, stack, parentMap, cycles);
            } else if (recStack.getOrDefault(neighbor, false)) {
                // 发现环 - 回溯路径
                List<String> cycle = new ArrayList<>();
                String temp = current;
                int count = 0;
                while (temp != null && !temp.equals(neighbor)) {
                    cycle.add(temp);
                    temp = parentMap.get(temp);

                    // 防止无限循环
                    if (count++ > 100) break;
                }

                if (temp != null && temp.equals(neighbor)) {
                    cycle.add(neighbor);
                    Collections.reverse(cycle);
                    cycles.add(cycle);
                }
            }
        }

        stack.pop();
        recStack.put(current, false);
    }

    // 3. 去重相似的环
    private static List<List<String>> deduplicateCycles(List<List<String>> cycles) {
        Set<String> uniqueCycles = new HashSet<>();
        List<List<String>> result = new ArrayList<>();

        for (List<String> cycle : cycles) {
            // 找到环中最小元素作为起始点
            String minElement = Collections.min(cycle);
            int startIndex = cycle.indexOf(minElement);

            // 创建标准化环表示
            List<String> normalized = new ArrayList<>();
            for (int i = 0; i < cycle.size(); i++) {
                normalized.add(cycle.get((startIndex + i) % cycle.size()));
            }

            String key = String.join("->", normalized);

            if (!uniqueCycles.contains(key)) {
                uniqueCycles.add(key);
                result.add(cycle);
            }
        }

        return result;
    }

    // 使用正则表达式匹配相关包名
    private static boolean matchesAnyPackage(String packageName, List<Pattern> packagePatterns) {
        return packagePatterns.stream()
                .anyMatch(pattern -> pattern.matcher(packageName).matches());
    }

    // 4. 获取相关类（使用正则过滤）
    private static Set<JavaClass> getRelevantClasses(JavaClasses allClasses, List<Pattern> packagePatterns) {
        return allClasses.stream()
                .filter(jc -> matchesAnyPackage(jc.getPackageName(), packagePatterns))
                .collect(Collectors.toSet());
    }

    // 5. 执行扫描的主方法
    // 参数 packageRegexes: 接受正则表达式列表
    public static void detectApiServiceCycles(String[] classPaths, String[] packageRegexes) {
        // 编译正则表达式
        List<Pattern> packagePatterns = Arrays.stream(packageRegexes)
                .map(Pattern::compile)
                .collect(Collectors.toList());

        JavaClasses classes = new ClassFileImporter().importPaths(
                Arrays.stream(classPaths).map(Path::of).toArray(Path[]::new)
        );

        // 收集项目中所有相关类
        Set<JavaClass> allRelevantClasses = getRelevantClasses(classes, packagePatterns);

        System.out.println("\n====== 开始扫描循环依赖 ======");
        System.out.println("扫描类数量: " + allRelevantClasses.size());
        System.out.println("使用正则表达式过滤包: " + String.join(", ", packageRegexes));
        System.out.println("过滤规则: 排除包含 @Lazy 的 @Resource 依赖\n");

        // 1. 创建有效类名的集合
        Set<String> relevantClassNames = allRelevantClasses.stream()
                .map(JavaClass::getName)
                .collect(Collectors.toSet());

        // 2. 获取所有依赖关系
        Map<String, Set<String>> allDependencies = getDependencies(classes);

        // 3. 过滤只保留相关类的依赖关系
        Map<String, Set<String>> filteredDependencies = new HashMap<>();
        for (Map.Entry<String, Set<String>> entry : allDependencies.entrySet()) {
            String className = entry.getKey();
            // 只处理我们关心的类
            if (relevantClassNames.contains(className)) {
                // 过滤只保留我们关心的类
                Set<String> relevantDeps = entry.getValue().stream()
                        .filter(relevantClassNames::contains)
                        .collect(Collectors.toSet());

                if (!relevantDeps.isEmpty()) {
                    filteredDependencies.put(className, relevantDeps);
                }
            }
        }

        // 4. 检测循环依赖
        List<List<String>> cycles = findCycles(filteredDependencies);

        // 5. 输出结果
        if (cycles.isEmpty()) {
            System.out.println("✅ 检测完成，未发现循环依赖");
        } else {
            System.out.println("⚠️ 发现 " + cycles.size() + " 个循环依赖:\n");
            for (int i = 0; i < cycles.size(); i++) {
                List<String> cycle = cycles.get(i);
                System.out.println("循环链 " + (i + 1) + ":");
                for (int j = 0; j < cycle.size(); j++) {
                    String arrow = (j == 0) ? "➤" : "→";
                    System.out.println("  " + arrow + " " + abbreviateClassName(cycle.get(j)));
                }
                // 闭合循环
                System.out.println("  ❮ " + abbreviateClassName(cycle.get(0)));
                System.out.println();
            }
        }
    }

    // 辅助方法：简化类名显示
    private static String abbreviateClassName(String fullName) {
        String[] parts = fullName.split("\\.");
        if (parts.length < 2) return fullName;

        // 保留最后两级包名
        if (parts.length > 3) {
            int start = Math.max(parts.length - 4, 0);
            return Arrays.stream(parts, start, parts.length)
                    .collect(Collectors.joining("."));
        }
        return fullName;
    }

}
