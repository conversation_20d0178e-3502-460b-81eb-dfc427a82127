server:
  port: 8071

datasync:
  url: http://localhost:48070

spring:
  profiles:
    active: local,test
  application:
    name: saas-localserver
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  aop:
    proxy-target-class: false # 使用 JDK 代理，而不是默认的 CGLIB 代理

  cloud:
    nacos:
      discovery:
        enabled: false
      config:
        enabled: false
#        import-check:
#          enabled: false

  #  cloud:
  #    nacos:
  #      discovery:
  #        heart-beat:
  #          enabled: true
  #      config:
  #        server-addr: ${spring.cloud.nacos.server-addr}
  #        import-check:
  #          enabled: false

  #  datasource:
  #    dynamic:
  #      primary: master # 设置默认数据源
  #      strict: false # 严格模式关闭
  #      datasource:
  #        master:
  #          driver-class-name: org.sqlite.JDBC
  #          url: jdbc:sqlite:file:${user.dir}/db/saas-local.db?mode=rwc
  #          hikari:
  #            minimum-idle: 5
  #            maximum-pool-size: 15
  #            is-auto-commit: true
  #            idle-timeout: 30000
  #            max-lifetime: 1800000
  #            connection-timeout: 30000
  #            connectionTestQuery: SELECT 1

  datasource:
    dynamic:
      datasource:
        master:
          url: jdbc:h2:file:${user.dir}/db/h2/saas-local
    driver-class-name: org.h2.Driver
    url: jdbc:h2:file:${user.dir}/db/h2/saas-local
    hikari:
      minimum-idle: 5
      maximum-pool-size: 15
      is-auto-commit: true
      idle-timeout: 30000
      max-lifetime: 1800000
      connection-timeout: 30000
      connectionTestQuery: SELECT 1

    h2:
      url-args: ;AUTO_SERVER=TRUE;MODE=MySQL;DATABASE_TO_UPPER=false;CASE_INSENSITIVE_IDENTIFIERS=TRUE

  servlet:
    multipart:
      max-file-size: 10MB

  flyway:
    enabled: true
    locations: ./db/migration
    baseline-on-migrate: true
    validate-on-migrate: false
    clean-disabled: true

#  application:
#    name: ${spring.application.name}
#    qos-port: 22230
#  registry:
#    address: nacos://${spring.cloud.nacos.server-addr}
#    register-mode: instance
#    parameters:
#      namespace: ${spring.cloud.nacos.discovery.namespace}
#    group: dubbo
#  config-center:
#    address: nacos://${spring.cloud.nacos.server-addr}
#    group: ${spring.cloud.nacos.config.group}
#    namespace: ${spring.cloud.nacos.config.namespace}
#  protocol:
#    name: dubbo
#    port: -1
#  consumer:
#    timeout: 5000
#    check: false
#    filter: dubboTenantFilter
#    provider-namespace: ${spring.cloud.nacos.discovery.namespace}
#    group: ${dubbo.registry.group}
#  provider:
#    timeout: 10000
#    filter: dubboTenantFilter
#    group: ${dubbo.registry.group}

#  data:
#    redis:
#      host: db01-medicare-test.redis.ybm100.top # 地址
#      port: 30002 # 端口
#      password: JEf8CVrnY0G3RPEZ # 密码，建议生产环境开启
#      database: 15

dubbo:
  enabled: false

# MQTT配置
mqtt:
  enabled: false  # MQTT功能开关，默认关闭
  server-url: tcp://localhost:1883
  access-key: your-access-key
  secret-key: your-secret-key
  parent-topic: saas/localserver
  keep-alive-interval: 60
  connection-timeout: 30
  automatic-reconnect: true
  instance-id: your-instance-id
  group-id: your-group-id
  client-id: localserver-client

mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    db-config:
      sql-parser-cache: true
      id-type: auto
      logic-delete-value: 1
      logic-not-delete-value: 0
      # 明确指定方言
      db-type: h2

---
#################### 芋道相关配置 ####################
---
yudao:
  info:
    version: 1.0.0
    base-package: cn.iocoder.yudao,com.xyy.saas
  web:
    admin-ui:
      url: http://localhost:8080 # 管理后台地址
  xss:
    enable: false
  security:
    permit-all_urls:
      - /admin-api/mp/open/** # 微信公众号开放平台，微信回调接口，不需要登录
      - /isDubboActive # 判断平滑发版服务探活
      - /actuator/** # spring监控
  websocket:
    enable: false # websocket的开关
    path: /infra/ws # 路径
    sender-type: local # 消息发送的类型，可选值为 local、redis、rocketmq、kafka、rabbitmq
    sender-rocketmq:
      topic: ${spring.application.name}-websocket # 消息发送的 RocketMQ Topic
      consumer-group: ${spring.application.name}-websocket-consumer # 消息发送的 RocketMQ Consumer Group
    sender-rabbitmq:
      exchange: ${spring.application.name}-websocket-exchange # 消息发送的 RabbitMQ Exchange
      queue: ${spring.application.name}-websocket-queue # 消息发送的 RabbitMQ Queue
    sender-kafka:
      topic: ${spring.application.name}-websocket # 消息发送的 Kafka Topic
      consumer-group: ${spring.application.name}-websocket-consumer # 消息发送的 Kafka Consumer Group
  codegen:
    base-package: ${yudao.info.base-package}
    db-schemas: ${spring.datasource.dynamic.datasource.master.name}
    front-type: 10 # 前端模版的类型，参见 CodegenFrontTypeEnum 枚举类
    unit-test-enable: true
  tenant: # 多门店相关配置项
    enable: true
    ignore-urls:
      - /**/system/tenant/default-config # App获取系统默认配置
      - /**/system/tenant/get-list-by-user # 获取当前用户关联的门店列表
      - /**/system/auth/login # 更改登录后选多门店逻辑 此处忽略门店id
      - /**/system/auth/weixin-mini-app-login # 更改登录后选多门店逻辑 此处忽略门店id
      - /**/system/auth/sms-login # 更改登录后选多门店逻辑 此处忽略门店id
      - /**/system/auth/login-confirm-with-tenant-id # 多门店二次选择门店登录接口放行
      - /**/system/auth/OA-login # OA登录放行
      - /**/system/auth/send-sms-code # 发送验证码放行
      - /**/system/auth/api-login # 三方授权登陆放行
      - /**/system/tenant/get-id-by-name # 基于名字获取门店，不许带门店编号
      - /**/system/tenant/get-by-website # 基于域名获取门店，不许带门店编号
      - /**/system/captcha/get # 获取图片验证码，和门店无关
      - /**/system/captcha/check # 校验图片验证码，和门店无关
      - /admin-api/infra/file/*/get/** # 获取图片，和门店无关
      - /admin-api/system/sms/callback/* # 短信回调接口，无法带上门店编号
      - /admin-api/pay/notify/** # 支付回调通知，不携带门店编号
      - /jmreport/* # 积木报表，无法携带门店编号
      - /admin-api/mp/open/** # 微信公众号开放平台，微信回调接口，无法携带门店编号
      - /**/product/search/** # 商品搜搜服务忽略
      - /**/product/rational/** # 合理用药审核接口
      - /**/infra/file/*/get/** # 获取图片，和门店无关
      - /**/system/sms/callback/* # 短信回调接口，无法带上门店编号
      - /**/pay/notify/** # 支付回调通知，不携带门店编号
      - /**/kernel/fdd/notify/** # fdd外部回调放行
      - /**/im/callback/** # IM回调全部放行
      - /**/drugstore/base-info/can-inquiry # 小程序查询门店是否可问诊
      - /actuator/** # spring监控
    ignore-tables:
      - system_tenant
      - system_tenant_certificate
      - saas_app_version
      - saas_app_version_detail
      - saas_tenant_package_relation
      - system_users # 更改登录后选多门店逻辑 此表忽略门店id
      - saas_oa_white_list
      - system_role_menu
      - system_tenant_package
      - system_dict_data
      - system_dict_type
      - system_error_code
      - system_menu
      - system_sms_channel
      - system_sms_template
      - system_sms_log
      - system_sensitive_word
      - system_oauth2_client
      - system_mail_account
      - system_mail_template
      - system_mail_log
      - system_notify_template
      - system_social_user
      - system_social_user_bind
      - member_user
      - saas_transmission_organ
      - saas_transmission_organ_dict
      - saas_transmission_organ_dict_match
      - saas_tenant_transmission_service_pack_relation
      - saas_transmission_service_pack
      - saas_transmission_config_item
      - saas_transmission_config_package
      - saas_transmission_task_record
      - infra_codegen_column
      - infra_codegen_table
      - infra_config
      - infra_file_config
      - infra_file
      - infra_file_content
      - infra_job
      - infra_job_log
      - infra_data_source_config
      - jimu_dict
      - jimu_dict_item
      - jimu_report
      - jimu_report_data_source
      - jimu_report_db
      - jimu_report_db_field
      - jimu_report_db_param
      - jimu_report_link
      - jimu_report_map
      - jimu_report_share
      - rep_demo_dxtj
      - rep_demo_employee
      - rep_demo_gongsi
      - rep_demo_jianpiao
      - tmp_report_data_1
      - tmp_report_data_income
      - saas_product_stdlib
      - saas_product_info
      - saas_product_qualification_info
      - saas_product_use_info
      - saas_product_transfer_record
      - saas_product_quality_change_record
      - saas_product_quality_change_detail
      - saas_product_price_adjustment_record
      - saas_product_price_adjustment_detail
      - saas_bpm_business_relation
      - saas_product_stdlib_sync
      - saas_product_category
      - saas_catalog
      - saas_regulatory_catalog_detail
      - saas_inquiry_main_suit
      - infra_data_source_config
      - saas_inquiry_doctor
      - saas_inquiry_doctor_practice
      - saas_inquiry_doctor_status
      - saas_inquiry_filing
      - saas_doctor_billing
      - saas_doctor_work_record
      - saas_doctor_audited_record
      - saas_inquiry_doctor_video
      - saas_doctor_reviews
      - saas_inquiry_complain
      - saas_inquiry_hospital_doctor_relation
      - saas_inquiry_hospital
      - saas_inquiry_hospital_department
      - saas_inquiry_hospital_department_relation
      - saas_inquiry_hospital_dept_doctor_relation
      - saas_inquiry_clinical_case
      - saas_inquiry_diagnosis_department_relation
      - saas_inquiry_option_config
      - saas_inquiry_hospital_setting
      - saas_inquiry_pharmacist
      - saas_inquiry_profession_identification
      - saas_inquiry_profession_sign
      - saas_inquiry_patient_info
      - saas_inquiry_record
      - saas_inquiry_record_detail
      - saas_inquiry_prescription
      - saas_inquiry_prescription_detail
      - saas_inquiry_prescription_audit
      - saas_medical_registration
      - saas_prescription_external
      - saas_medical_insurance_order
      - saas_doctor_quick_reply_msg
      - saas_inquiry_prescription_template
      - saas_inquiry_user_signature_information
      - saas_inquiry_signature_platform
      - saas_inquiry_signature_person
      - saas_inquiry_signature_ca_auth
      - saas_inquiry_signature_contract
      - saas_inquiry_signature_callback_log
      - saas_inquiry_im_user
      - saas_inquiry_im_message
      - saas_inquiry_transcoding
      - saas_third_party_pre_inquiry
      - saas_third_party_pre_inquiry_detail
      - saas_third_party_drug_match_fail_record
      - local_mock_message_queue
      - local_mock_kv_store
    ignore-caches:
      - permission_menu_ids
      - oauth_client
      - notify_template
      - mail_account
      - mail_template
      - sms_template
    ignore-tenant-ids: # 忽略的门店id
      - -1
      - -2
  trade:
    order:
      app-id: 1 # 商户编号
      pay-expire-time: 2h # 支付的过期时间
      receive-expire-time: 14d # 收货的过期时间
      comment-expire-time: 7d # 评论的过期时间
    express:
      client: kd_niao
      kd-niao:
        api-key: cb022f1e-48f1-4c4a-a723-9001ac9676b8
        business-id: 1809751
      kd100:
        key: pLXUGAwK5305
        customer: E77DF18BE109F454A5CD319E44BF5177

  # 短信验证码相关的配置项
  sms-code:
    expire-times: 5m
    send-frequency: 1m
    send-maximum-quantity-per-day: 50 # 每日发送最大数量不能为空
    begin-code: 000000 # 这里配置 1111 的原因是，测试方便。
    end-code: 999999 # 这里配置 1111 的原因是，测试方便。
---
#################### 微信公众号相关配置 ####################
---
wx: # 参见 https://github.com/Wechat-Group/WxJava/blob/develop/spring-boot-starters/wx-java-mp-spring-boot-starter/README.md 文档
  mp:
    # 公众号配置(必填)
    app-id: wx041349c6f39b268b
    secret: 5abee519483bc9f8cb37ce280e814bd0
    # 存储配置，解决 AccessToken 的跨节点的共享
    config-storage:
      type: RedisTemplate # 采用 RedisTemplate 操作 Redis，会自动从 Spring 中获取
      key-prefix: wx # Redis Key 的前缀
      http-client-type: HttpClient # 采用 HttpClient 请求微信公众号平台
  miniapp: # 小程序配置（必填），参见 https://github.com/Wechat-Group/WxJava/blob/develop/spring-boot-starters/wx-java-miniapp-spring-boot-starter/README.md 文档
    appid: wxd1425c818778593e
    secret: 32ab10e980c95f7eeb6d7163d35c2649
    config-storage:
      type: RedisTemplate # 采用 RedisTemplate 操作 Redis，会自动从 Spring 中获取
      key-prefix: wa # Redis Key 的前缀
      http-client-type: HttpClient # 采用 HttpClient 请求微信公众号平台

---
#token配置
---
jose4j:
  token:
    # 配置尽量不要调整
    # 令牌密钥
    keypair: |
      {
        "p": "63GJh-NOKm0lyueKJWlSfcWx8ugXEcFVhNt33bmwggMN5kOop4AW3R31-uSe8zdTYQQ4BFpegXPQERI-UAgj4YUwg4n7bIin7sAm0HosikqYPc3qjGkvLSROHl29wv7qQxltaoyGJuwdHa1m7MWagY8FSWl4QwM3qkor4wh-AHc",
        "kty": "RSA",
        "q": "rHYNxuE6ml7tFDEzE3eFa5JI39ojlbjt13WRhaqLHDCCYfv8xqeknupX3m97cIYyF6VD9M-kRieSUQGE2X7QoOYdmACxNN759x-0f-j44bt3V13oKwNN6OvS9guID6-iWe4_z0_iowsM3wOF8KLy6kTPectrtv145vGV1YLyYts",
        "d": "nXLX2iLBTggEsxBgmKXUCNrDsZ0ToqkdIAPPWhI2ZO_c4qPpsVx_gQdqD0do62S4hGgJpw99h5ilicqXjlJ87mzk57FRQv0R5BGF2bmm1mmG1guM76fyQ9jQNDGKAn3z9quCQj77J9_3LVWoOnF6wzVcgFLOttXl9SzDm-Ig1Ghl3r6_f49PifYQR9LWvtpab2itCbm9JlVpJ7EH8kYb4258npLIc5byOqMeMSp5me3wFAIKlCJTDlQFwqBUNECEkslTgDmpypzL6JqjQugBG0716mhM7qLModwdPM9HFQ_t4HIfVjEctJ1Lue9ExSzKM5Ff00jUzwMDwVBNzowXgQ",
        "e": "AQAB",
        "use": "sig",
        "kid": "8986c8b17fc641d6be2522844e4faef9",
        "qi": "NBwbU0f72bHqlqrKAqxVGw6Hw4LZbc6rdWM6hKo8Zw6Xjc1Bqn_mTIdfNl4halKHoI7p4A7agx57qgSxwCsbGZYIfqtxnf425gIHHQ8O1RerkGCk18D9jTpZfbc6MLFI8WXdi7ergTUT1ZymARPhh37X_NiKLE8qatMSrdRBIFE",
        "dp": "r7p_TxheGBZ9NbOZjZDNzCgJSGboCQ2HGfGAxtI4puFGt43aTKKy7fjVxpxq8tdheTDf6ofUr8RUENnP_oYE2edCLXuIWBqrVOzAS6xUoCdK-B5AKOq9FnFZiRqobuk6yjgpTXRzEIv8s1DgWGqohnjMFl4NL98v5f8UQJDvEsM",
        "alg": "PS512",
        "dq": "cYSbeOdT7bzCDOJ9nji0B6SYd1Bcz_aUB5iJmxQw0PNVPy3gqto6T6gtAmsbM2wBmPLdkk0C8nKlqtB3I7qHlQEjRaRfAFAs9O9XnkI5YExHN6jGY1mfdMuVZwMcIkcXdF6QfeM1aOCjLkBhb42ym2WDs8WkNyUM6ebnG01AQrU",
        "n": "npzbaF5MP8DcWDqBW5IeqCobf_Coe6SUX1OsQNsraIpFFAZYOrRC2uFRj57B3k53CFAkUzUoQpMAw99kNM0LtuC4rt4ZY-mrAca-kI6L1kIZLsod810wpwQ7_X0IpDvmxVFxotKIJiKf-UAt5ilCkYDxenTqQN49fl-sK3c8R5D_ECf8SC5qfAJaXcG_78T_Kqj1tirQp4rRymciznDQGqv8PjIc9d1hZ_aOfA33Nm-55b8VlXksycBmcpsVNXBjhKuREJTe3yiY9QmS_ZI8D3E9gNBKNqfhkl4FxsGesWj9LFHud1vEcotTiwDBaH7f7eEt4ZBlYd6aUR3UXnXzzQ"
      }
    # 令牌有效期（默认30分钟）
    expireSeconds: 18000

---
# 腾讯IM、TRTC配置
---
tencent:
  im:
    sdkAppId: **********
    secretKey: fcc8e19a1e8c155faa99627c045613554c0075402acc1bdabc7440623565a081
    expire: 86400
    domain: https://console.tim.qq.com
    adminUserId: administrator
    api:
      createUser: v4/im_open_login_svc/account_import
      sendMessage: v4/openim/sendmsg
      sendBatchMessage: v4/openim/batchsendmsg
  trtc:
    vod:
      endPoint: vod.tencentcloudapi.com
      secretId: AKIDUlobU7KebLSElDlQAfvwlLlwsa4QeVDm
      secretKey: VlmX3KQL8i3A2f3E9tQilY6PbYNzOYT9
      transcodingTemplateId: 1010
    rav:
      sdkAppId: **********
      userSignKey: dea0688d518cfba5516af279ef5a36e3dfed0c02f3d6a6b37b774b85ffa98352
      endPoint: trtc.tencentcloudapi.com
      region: ap-beijing
      bizId: 61303
    live:
      endPoint: live.tencentcloudapi.com
      pushDomain: 61303.livepush.myqcloud.com
      apiKey: 4b70e52606e1e90be172761d5f5738b3

---

rocketmq:
  name-server: 10.25.17.9:19876
event:
  bus:
#    name-server: 10.25.17.9:9876
    consumer:
      group-id: com_xyy_saas_localserver_LocalserverApplication_mq_consumer_reception_RecetionAreaConsumer

logging:
  level:
    com.baomidou.mybatisplus: info
    com.xyy.saas.inquiry.localmock: INFO
    com.xyy.saas: DEBUG


inquiry:
  forward:
    sign: 954414404a914644bb8f9933d6d8d925
    domain: https://saasremote.api.test.ybm100.com
    http-time-out: 10
    dubbo:
      domain: https://saas-cloud.test.ybm100.com
      httpTimeOut: 6
  doctor:
    sendNum: 3
  product:
    search:
      switch:
        self: true


