<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <property name="log.dir" value="${log.dir:-logs}" />
    <property name="log.name" value="${log.name:-localserver_medicare}" />

    <property name="PATTERN" value="%date{HH:mm:ss.SSS} %-5p [%36.36(%32.34logger:%line)] [%X{traceId}] - %m%n"/>
    <property name="PATTERN_LIMIT" value="%date{HH:mm:ss.SSS} %-5p [%36.36(%32.34logger:%line)] [%X{traceId}] - %replace(%msg){'^(.{2048}).*$', '$1 ...截断'}%n"/>
    <property name="log.pattern" value="${PATTERN}"/>
    <!--<property name="log.pattern" value="%date{HH:mm:ss.SSS} %-5p [%32.32logger:%3line] [%X{traceId}] - %.-1024m%n"/>-->
    <!--<property name="log.pattern2" value="%date{HH:mm:ss.SSS} %-5p [%32.32logger:%3line] [%X{traceId}] - %replace(%msg){'^(.{1024}).*$', '$1(截断)'}%n"/>-->
    <property name="log.pattern2" value="${PATTERN_LIMIT}"/>

    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${log.pattern2}</pattern>
        </encoder>
<!--        <filter class="xyy.saas.localserver.log.LogFilter" />-->
    </appender>

    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.dir}/${log.name}.log</file>
        <encoder>
            <pattern>${log.pattern2}</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${log.dir}/%d{yyyy-MM, aux}/${log.name}.%d{yyyy-MM-dd}.%i.log.zip</fileNamePattern>
            <maxHistory>90</maxHistory>
            <maxFileSize>100MB</maxFileSize>
            <totalSizeCap>32GB</totalSizeCap>
        </rollingPolicy>
        <!--<filter class="xyy.saas.localserver.log.ThirdMatchFilter">-->
        <!--    <logNamePatterns>-->
        <!--        com\.xyy\.saas\.localserver\.medical(\.\w+)+-->
        <!--    </logNamePatterns>-->
        <!--    <OnMatch>DENY</OnMatch>-->
        <!--    <OnMismatch>ACCEPT</OnMismatch>-->
        <!--</filter>-->
    </appender>

    <appender name="FILE_THIRD" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.dir}/third/med_ins.log</file>
        <encoder>
            <pattern>${log.pattern}</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${log.dir}/third/%d{yyyy-MM, aux}/med_ins.%d{yyyy-MM-dd}.%i.log.zip</fileNamePattern>
            <maxHistory>360</maxHistory>
            <maxFileSize>100MB</maxFileSize>
            <totalSizeCap>32GB</totalSizeCap>
        </rollingPolicy>
        <!--<filter class="xyy.saas.localserver.log.ThirdMatchFilter">-->
        <!--    <logNamePatterns>-->
        <!--        com\.xyy\.saas\.localserver\.medical(\.\w+)+-->
        <!--    </logNamePatterns>-->
        <!--    <OnMatch>ACCEPT</OnMatch>-->
        <!--    <OnMismatch>DENY</OnMismatch>-->
        <!--</filter>-->
    </appender>

    <logger name="xyy.saas.localserver.db.TableUpdate" level="WARN"/>
    <logger name="com.xyy.saas.localserver.medical" additivity="true">
        <appender-ref ref="FILE_THIRD" />
    </logger>
    <root level="INFO">
        <appender-ref ref="CONSOLE" />
        <appender-ref ref="FILE" />
        <!--<appender-ref ref="FILE_THIRD" />-->
    </root>
</configuration>
