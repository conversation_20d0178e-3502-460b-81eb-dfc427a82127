spring:
  data:
    redis:
      host: db01-medicare-test.redis.ybm100.top # 地址
      port: 30002 # 端口
      password: JEf8CVrnY0G3RPEZ # 密码，建议生产环境开启
      database: 15

saas-cloud:
  url:

mqtt:
  serverUrl: tcp://mqtt-cn-0pp1dq9nc07.mqtt.aliyuncs.com:1883
  accessKey: LTAI4FogiMb6vPryht13eRGy
  secretKey: ******************************
  instanceId: mqtt-cn-0pp1dq9nc07
  parentTopic: SAAS-Test
  clientId: GID_Medicare@@@Test
  groupId: GID_Saas
  keepAliveInterval: 15000
  connectionTimeout: 6000