[{"name": "[Lcom.sun.management.internal.DiagnosticCommandArgumentInfo;"}, {"name": "[Lcom.sun.management.internal.DiagnosticCommandInfo;"}, {"name": "[Z"}, {"name": "com.sun.management.internal.DiagnosticCommandArgumentInfo", "methods": [{"name": "<init>", "parameterTypes": ["java.lang.String", "java.lang.String", "java.lang.String", "java.lang.String", "boolean", "boolean", "boolean", "int"]}]}, {"name": "com.sun.management.internal.DiagnosticCommandInfo", "methods": [{"name": "<init>", "parameterTypes": ["java.lang.String", "java.lang.String", "java.lang.String", "java.lang.String", "java.lang.String", "java.lang.String", "boolean", "java.util.List"]}]}, {"name": "java.lang.Bo<PERSON>an", "methods": [{"name": "getBoolean", "parameterTypes": ["java.lang.String"]}]}, {"name": "java.lang.InternalError", "methods": [{"name": "<init>", "parameterTypes": ["java.lang.String"]}]}, {"name": "java.lang.Throwable", "methods": [{"name": "toString", "parameterTypes": []}]}, {"name": "java.util.Arrays", "methods": [{"name": "asList", "parameterTypes": ["java.lang.Object[]"]}]}, {"name": "org.sqlite.BusyHandler", "methods": [{"name": "callback", "parameterTypes": ["int"]}]}, {"name": "org.sqlite.Collation", "methods": [{"name": "xCompare", "parameterTypes": ["java.lang.String", "java.lang.String"]}]}, {"name": "org.sqlite.Function", "fields": [{"name": "args"}, {"name": "context"}, {"name": "value"}], "methods": [{"name": "xFunc", "parameterTypes": []}]}, {"name": "org.sqlite.Function$Aggregate", "methods": [{"name": "clone", "parameterTypes": []}, {"name": "xFinal", "parameterTypes": []}, {"name": "xStep", "parameterTypes": []}]}, {"name": "org.sqlite.Function$Window", "methods": [{"name": "xInverse", "parameterTypes": []}, {"name": "xValue", "parameterTypes": []}]}, {"name": "org.sqlite.ProgressHandler", "methods": [{"name": "progress", "parameterTypes": []}]}, {"name": "org.sqlite.core.DB", "methods": [{"name": "onCommit", "parameterTypes": ["boolean"]}, {"name": "onUpdate", "parameterTypes": ["int", "java.lang.String", "java.lang.String", "long"]}, {"name": "throwex", "parameterTypes": []}, {"name": "throwex", "parameterTypes": ["int"]}]}, {"name": "org.sqlite.core.DB$ProgressObserver", "methods": [{"name": "progress", "parameterTypes": ["int", "int"]}]}, {"name": "org.sqlite.core.NativeDB", "fields": [{"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "commitListener"}, {"name": "pointer"}, {"name": "progressHandler"}, {"name": "updateListener"}], "methods": [{"name": "stringToUtf8ByteArray", "parameterTypes": ["java.lang.String"]}, {"name": "throwex", "parameterTypes": ["java.lang.String"]}]}, {"name": "sun.instrument.InstrumentationImpl", "methods": [{"name": "<init>", "parameterTypes": ["long", "boolean", "boolean", "boolean"]}, {"name": "loadClassAndCallAgentmain", "parameterTypes": ["java.lang.String", "java.lang.String"]}, {"name": "loadClassAndCallPremain", "parameterTypes": ["java.lang.String", "java.lang.String"]}, {"name": "transform", "parameterTypes": ["java.lang.Module", "java.lang.ClassLoader", "java.lang.String", "java.lang.Class", "java.security.ProtectionDomain", "byte[]", "boolean"]}]}, {"name": "sun.management.VMManagementImpl", "fields": [{"name": "compTimeMonitoringSupport"}, {"name": "currentThreadCpuTimeSupport"}, {"name": "objectMonitorUsageSupport"}, {"name": "otherThreadCpuTimeSupport"}, {"name": "remoteDiagnosticCommandsSupport"}, {"name": "synchronizerUsageSupport"}, {"name": "threadAllocatedMemorySupport"}, {"name": "threadContentionMonitoringSupport"}]}]