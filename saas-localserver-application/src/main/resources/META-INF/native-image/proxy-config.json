[{"interfaces": ["com.xyy.saas.datasync.client.protocol.DataSyncClient", "org.springframework.aop.SpringProxy", "org.springframework.aop.framework.Advised", "org.springframework.core.DecoratingProxy"]}, {"interfaces": ["com.xyy.saas.localserver.inventory.server.dal.mysql.bill.InventoryBillDetailMapper"]}, {"interfaces": ["com.xyy.saas.localserver.inventory.server.dal.mysql.bill.InventoryBillMapper"]}, {"interfaces": ["com.xyy.saas.localserver.inventory.server.dal.mysql.campon.InventoryCampOnBillDetailMapper"]}, {"interfaces": ["com.xyy.saas.localserver.inventory.server.dal.mysql.campon.InventoryCampOnBillMapper"]}, {"interfaces": ["com.xyy.saas.localserver.inventory.server.service.bill.InventoryBillDetailService", "org.springframework.aop.SpringProxy", "org.springframework.aop.framework.Advised", "org.springframework.core.DecoratingProxy"]}, {"interfaces": ["com.xyy.saas.localserver.inventory.server.service.campon.InventoryCampOnBillDetailService", "org.springframework.aop.SpringProxy", "org.springframework.aop.framework.Advised", "org.springframework.core.DecoratingProxy"]}, {"interfaces": ["com.xyy.saas.localserver.inventory.server.service.campon.InventoryCampOnBillService", "org.springframework.aop.SpringProxy", "org.springframework.aop.framework.Advised", "org.springframework.core.DecoratingProxy"]}, {"interfaces": ["com.xyy.saas.localserver.inventory.server.service.campon.InventoryCampOnService", "org.springframework.aop.SpringProxy", "org.springframework.aop.framework.Advised", "org.springframework.core.DecoratingProxy"]}, {"interfaces": ["com.xyy.saas.localserver.inventory.server.service.stock.InventorySelectService", "org.springframework.aop.SpringProxy", "org.springframework.aop.framework.Advised", "org.springframework.core.DecoratingProxy"]}, {"interfaces": ["com.xyy.saas.localserver.inventory.server.service.stock.InventoryStockService", "org.springframework.aop.SpringProxy", "org.springframework.aop.framework.Advised", "org.springframework.core.DecoratingProxy"]}, {"interfaces": ["com.xyy.saas.localserver.purchase.server.dal.mysql.extend.PurchaseErpBillMapper"]}, {"interfaces": ["com.xyy.saas.localserver.purchase.server.dal.mysql.extend.PurchaseInvoiceMapper"]}, {"interfaces": ["com.xyy.saas.localserver.purchase.server.dal.mysql.extend.PurchaseTransportMapper"]}, {"interfaces": ["com.xyy.saas.localserver.purchase.server.dal.mysql.purchase.PurchaseBillDetailMapper"]}, {"interfaces": ["com.xyy.saas.localserver.purchase.server.dal.mysql.purchase.PurchaseBillMapper"]}, {"interfaces": ["com.xyy.saas.localserver.purchase.server.dal.mysql.receive.ReceiveBillDetailMapper"]}, {"interfaces": ["com.xyy.saas.localserver.purchase.server.dal.mysql.receive.ReceiveBillMapper"]}, {"interfaces": ["com.xyy.saas.localserver.purchase.server.dal.mysql.returned.ReturnBillDetailMapper"]}, {"interfaces": ["com.xyy.saas.localserver.purchase.server.dal.mysql.returned.ReturnBillMapper"]}, {"interfaces": ["com.xyy.saas.localserver.purchase.server.dal.mysql.stockout.StockoutBillDetailMapper"]}, {"interfaces": ["com.xyy.saas.localserver.purchase.server.dal.mysql.stockout.StockoutBillMapper"]}, {"interfaces": ["com.xyy.saas.localserver.purchase.server.dal.mysql.supplier.SupplierMapper"]}, {"interfaces": ["com.xyy.saas.localserver.purchase.server.dal.mysql.supplier.TenantSupplierRelationMapper"]}, {"interfaces": ["com.xyy.saas.localserver.purchase.server.dal.mysql.supplier.TenantSupplierSalesMapper"]}, {"interfaces": ["com.xyy.saas.localserver.purchase.server.service.extend.PurchaseErpBillService", "org.springframework.aop.SpringProxy", "org.springframework.aop.framework.Advised", "org.springframework.core.DecoratingProxy"]}, {"interfaces": ["com.xyy.saas.localserver.purchase.server.service.extend.PurchaseInvoiceService", "org.springframework.aop.SpringProxy", "org.springframework.aop.framework.Advised", "org.springframework.core.DecoratingProxy"]}, {"interfaces": ["com.xyy.saas.localserver.purchase.server.service.extend.PurchaseTransportService", "org.springframework.aop.SpringProxy", "org.springframework.aop.framework.Advised", "org.springframework.core.DecoratingProxy"]}, {"interfaces": ["com.xyy.saas.localserver.purchase.server.service.purchase.PurchaseBillDetailService", "org.springframework.aop.SpringProxy", "org.springframework.aop.framework.Advised", "org.springframework.core.DecoratingProxy"]}, {"interfaces": ["com.xyy.saas.localserver.purchase.server.service.receive.ReceiveBillDetailService", "org.springframework.aop.SpringProxy", "org.springframework.aop.framework.Advised", "org.springframework.core.DecoratingProxy"]}, {"interfaces": ["com.xyy.saas.localserver.purchase.server.service.returned.AllocationReturnService", "org.springframework.aop.SpringProxy", "org.springframework.aop.framework.Advised", "org.springframework.core.DecoratingProxy"]}, {"interfaces": ["com.xyy.saas.localserver.purchase.server.service.returned.ReturnBillDetailService", "org.springframework.aop.SpringProxy", "org.springframework.aop.framework.Advised", "org.springframework.core.DecoratingProxy"]}, {"interfaces": ["com.xyy.saas.localserver.purchase.server.service.returned.StoreReturnService", "org.springframework.aop.SpringProxy", "org.springframework.aop.framework.Advised", "org.springframework.core.DecoratingProxy"]}, {"interfaces": ["com.xyy.saas.localserver.purchase.server.service.stockout.StockoutBillDetailService", "org.springframework.aop.SpringProxy", "org.springframework.aop.framework.Advised", "org.springframework.core.DecoratingProxy"]}, {"interfaces": ["com.xyy.saas.localserver.purchase.server.service.stockout.StockoutBillService", "org.springframework.aop.SpringProxy", "org.springframework.aop.framework.Advised", "org.springframework.core.DecoratingProxy"]}, {"interfaces": ["com.xyy.saas.localserver.purchase.server.service.supplier.SupplierService", "org.springframework.aop.SpringProxy", "org.springframework.aop.framework.Advised", "org.springframework.core.DecoratingProxy"]}, {"interfaces": ["com.xyy.saas.localserver.purchase.server.service.supplier.TenantSupplierSalesService", "org.springframework.aop.SpringProxy", "org.springframework.aop.framework.Advised", "org.springframework.core.DecoratingProxy"]}, {"interfaces": ["java.lang.reflect.GenericArrayType", "org.springframework.core.SerializableTypeWrapper$SerializableTypeProxy", "java.io.Serializable"]}, {"interfaces": ["java.lang.reflect.ParameterizedType", "org.springframework.core.SerializableTypeWrapper$SerializableTypeProxy", "java.io.Serializable"]}, {"interfaces": ["java.lang.reflect.TypeVariable", "org.springframework.core.SerializableTypeWrapper$SerializableTypeProxy", "java.io.Serializable"]}, {"interfaces": ["java.lang.reflect.WildcardType", "org.springframework.core.SerializableTypeWrapper$SerializableTypeProxy", "java.io.Serializable"]}, {"interfaces": ["java.sql.Connection"]}, {"interfaces": ["org.apache.ibatis.session.SqlSession"]}, {"interfaces": ["org.springframework.beans.factory.annotation.Qualifier"]}, {"interfaces": ["org.springframework.boot.actuate.endpoint.annotation.EndpointExtension"]}, {"interfaces": ["org.springframework.boot.context.properties.ConfigurationProperties"]}, {"interfaces": ["org.springframework.context.event.EventListener"]}, {"interfaces": ["org.springframework.security.authentication.AuthenticationManager", "org.springframework.aop.SpringProxy", "org.springframework.aop.framework.Advised", "org.springframework.core.DecoratingProxy"]}, {"interfaces": ["org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity"]}, {"interfaces": ["org.springframework.web.bind.annotation.ControllerAdvice"]}, {"interfaces": ["org.springframework.web.bind.annotation.PathVariable"]}, {"interfaces": ["org.springframework.web.bind.annotation.RequestMapping"]}, {"interfaces": ["org.springframework.web.bind.annotation.RequestParam"]}, {"interfaces": ["org.springframework.web.service.annotation.HttpExchange"]}]