{"reflection": [{"type": "[Lcom.fasterxml.jackson.databind.deser.BeanDeserializerModifier;"}, {"type": "[Lcom.fasterxml.jackson.databind.deser.Deserializers;"}, {"type": "[Lcom.fasterxml.jackson.databind.deser.KeyDeserializers;"}, {"type": "[Lcom.fasterxml.jackson.databind.deser.ValueInstantiators;"}, {"type": "[Lcom.fasterxml.jackson.databind.ser.BeanSerializerModifier;"}, {"type": "[Lcom.fasterxml.jackson.databind.ser.Serializers;"}, {"type": "[Lorg.springframework.util.ConcurrentReferenceHashMap$Segment;"}, {"type": "[Z"}, {"type": "ch.qos.logback.classic.LoggerContext"}, {"type": "ch.qos.logback.classic.encoder.PatternLayoutEncoder", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "ch.qos.logback.classic.joran.SerializedModelConfigurator", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "ch.qos.logback.classic.pattern.DateConverter", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "ch.qos.logback.classic.pattern.LevelConverter", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "ch.qos.logback.classic.pattern.LineOfCallerConverter", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "ch.qos.logback.classic.pattern.LineSeparatorConverter", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "ch.qos.logback.classic.pattern.LoggerConverter", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "ch.qos.logback.classic.pattern.MDCConverter", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "ch.qos.logback.classic.pattern.MessageConverter", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "ch.qos.logback.classic.spi.LogbackServiceProvider"}, {"type": "ch.qos.logback.classic.util.DefaultJoranConfigurator", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "ch.qos.logback.core.Console<PERSON>ppender", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "ch.qos.logback.core.FileAppender"}, {"type": "ch.qos.logback.core.OutputStreamAppender", "methods": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "parameterTypes": ["ch.qos.logback.core.encoder.Encoder"]}]}, {"type": "ch.qos.logback.core.encoder.Encoder"}, {"type": "ch.qos.logback.core.encoder.LayoutWrappingEncoder", "methods": [{"name": "setParent", "parameterTypes": ["ch.qos.logback.core.spi.ContextAware"]}]}, {"type": "ch.qos.logback.core.pattern.IdentityCompositeConverter", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "ch.qos.logback.core.pattern.PatternLayoutEncoderBase", "methods": [{"name": "setPattern", "parameterTypes": ["java.lang.String"]}]}, {"type": "ch.qos.logback.core.pattern.ReplacingCompositeConverter", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "ch.qos.logback.core.rolling.RollingFileAppender", "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "setFile", "parameterTypes": ["java.lang.String"]}, {"name": "setRollingPolicy", "parameterTypes": ["ch.qos.logback.core.rolling.RollingPolicy"]}]}, {"type": "ch.qos.logback.core.rolling.RollingPolicy"}, {"type": "ch.qos.logback.core.rolling.RollingPolicyBase", "methods": [{"name": "setFileNamePattern", "parameterTypes": ["java.lang.String"]}, {"name": "setParent", "parameterTypes": ["ch.qos.logback.core.FileAppender"]}]}, {"type": "ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy", "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "setMaxFileSize", "parameterTypes": ["ch.qos.logback.core.util.FileSize"]}]}, {"type": "ch.qos.logback.core.rolling.TimeBasedRollingPolicy", "methods": [{"name": "setMaxHistory", "parameterTypes": ["int"]}, {"name": "setTotalSizeCap", "parameterTypes": ["ch.qos.logback.core.util.FileSize"]}]}, {"type": "ch.qos.logback.core.rolling.helper.DateTokenConverter", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "ch.qos.logback.core.rolling.helper.IntegerTokenConverter", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "ch.qos.logback.core.spi.ContextAware"}, {"type": "ch.qos.logback.core.util.FileSize", "methods": [{"name": "valueOf", "parameterTypes": ["java.lang.String"]}]}, {"type": "cn.iocoder.yudao.framework.mybatis.config.IdTypeEnvironmentPostProcessor", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "cn.iocoder.yudao.framework.tenant.core.mq.kafka.TenantKafkaEnvironmentPostProcessor", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "com.baomidou.mybatisplus.autoconfigure.SafetyEncryptProcessor", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "com.fasterxml.jackson.core.JsonGenerator"}, {"type": "com.fasterxml.jackson.databind.ObjectMapper"}, {"type": "com.fasterxml.jackson.databind.ext.Java7SupportImpl", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "com.fasterxml.jackson.dataformat.smile.SmileFactory"}, {"type": "com.fasterxml.jackson.dataformat.xml.XmlMapper"}, {"type": "com.fasterxml.jackson.datatype.jdk8.Jdk8Module", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "com.fasterxml.jackson.datatype.jsr310.JavaTimeModule", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "com.fasterxml.jackson.module.kotlin.KotlinModule"}, {"type": "com.fasterxml.jackson.module.paramnames.ParameterNamesModule", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "com.google.gson.Gson"}, {"type": "com.intellij.rt.execution.application.AppMainV2$Agent", "methods": [{"name": "premain", "parameterTypes": ["java.lang.String", "java.lang.instrument.Instrumentation"]}]}, {"type": "com.sun.org.apache.xalan.internal.xsltc.trax.TransformerFactoryImpl", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "com.sun.org.apache.xerces.internal.jaxp.SAXParserFactoryImpl", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "com.xyy.saas.localserver.LocalserverApplication__ApplicationContextInitializer"}, {"type": "io.micrometer.tracing.Tracer"}, {"type": "jakarta.inject.Provider"}, {"type": "jakarta.json.bind.Jsonb"}, {"type": "jakarta.persistence.Persistence"}, {"type": "jakarta.servlet.Servlet"}, {"type": "jakarta.xml.bind.Binder"}, {"type": "java.io.FilePermission"}, {"type": "java.lang.Class", "methods": [{"name": "getModule", "parameterTypes": []}]}, {"type": "java.lang.Module", "methods": [{"name": "isNamed", "parameterTypes": []}]}, {"type": "java.lang.Object", "allDeclaredFields": true}, {"type": "java.lang.RuntimePermission"}, {"type": "java.lang.String"}, {"type": "java.lang.Thread", "fields": [{"name": "threadLocalRandomProbe"}]}, {"type": "java.lang.reflect.ParameterizedType", "methods": [{"name": "getRawType", "parameterTypes": []}]}, {"type": "java.net.NetPermission"}, {"type": "java.net.SocketPermission"}, {"type": "java.net.URLPermission", "methods": [{"name": "<init>", "parameterTypes": ["java.lang.String", "java.lang.String"]}]}, {"type": "java.security.AllPermission"}, {"type": "java.security.SecurityPermission"}, {"type": "java.sql.Date"}, {"type": "java.sql.Timestamp"}, {"type": "java.util.PropertyPermission"}, {"type": "java.util.concurrent.atomic.AtomicBoolean", "fields": [{"name": "value"}]}, {"type": "java.util.concurrent.atomic.Striped64", "fields": [{"name": "base"}, {"name": "cellsBusy"}]}, {"type": "java.util.logging.LogManager"}, {"type": "java.util.logging.SimpleFormatter", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "javafx.beans.value.ObservableValue"}, {"type": "javax.money.MonetaryAmount"}, {"type": "javax.naming.InitialContext"}, {"type": "javax.smartcardio.CardPermission"}, {"type": "jdk.crac.management.CRaCMXBean"}, {"type": "kotlin.Metadata"}, {"type": "kotlin.jvm.JvmInline"}, {"type": "kotlin.reflect.full.<PERSON>es"}, {"type": "kotlinx.coroutines.reactor.MonoKt"}, {"type": "kotlinx.serialization.cbor.Cbor"}, {"type": "kotlinx.serialization.json.Json"}, {"type": "kotlinx.serialization.protobuf.ProtoBuf"}, {"type": "org.apache.el.ExpressionFactoryImpl", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.apache.logging.log4j.core.impl.Log4jContextFactory"}, {"type": "org.apache.logging.log4j.spi.ExtendedLogger"}, {"type": "org.apache.logging.slf4j.SLF4JProvider"}, {"type": "org.eclipse.core.runtime.FileLocator"}, {"type": "org.hibernate.validator.HibernateValidator"}, {"type": "org.hibernate.validator.internal.util.logging.Log_$logger", "methods": [{"name": "<init>", "parameterTypes": ["org.jboss.logging.Logger"]}]}, {"type": "org.hibernate.validator.internal.util.logging.Log_$logger_zh"}, {"type": "org.hibernate.validator.internal.util.logging.Log_$logger_zh_CN"}, {"type": "org.hibernate.validator.internal.util.logging.Messages_$bundle", "fields": [{"name": "INSTANCE"}]}, {"type": "org.hibernate.validator.internal.util.logging.Messages_$bundle_zh"}, {"type": "org.hibernate.validator.internal.util.logging.Messages_$bundle_zh_CN"}, {"type": "org.jboss.logging.Logger"}, {"type": "org.joda.time.ReadableInstant"}, {"type": "org.slf4j.<PERSON>"}, {"type": "org.slf4j.bridge.SLF4JBridgeHandler"}, {"type": "org.slf4j.spi.LocationAwareLogger", "methods": [{"name": "log", "parameterTypes": ["org.slf4j.Marker", "java.lang.String", "int", "java.lang.String", "java.lang.Object[]", "java.lang.Throwable"]}]}, {"type": "org.springframework.boot.ClearCachesApplicationListener", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.SpringApplication", "allDeclaredFields": true, "methods": [{"name": "setAllowBeanDefinitionOverriding", "parameterTypes": ["boolean"]}, {"name": "setAllowCircularReferences", "parameterTypes": ["boolean"]}]}, {"type": "org.springframework.boot.actuate.autoconfigure.health.NoSuchHealthContributorFailureAnalyzer", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.actuate.autoconfigure.metrics.ValidationFailureAnalyzer", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.actuate.autoconfigure.tracing.LogCorrelationEnvironmentPostProcessor", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.ansi.AnsiOutput$Enabled"}, {"type": "org.springframework.boot.autoconfigure.BackgroundPreinitializer", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.SharedMetadataReaderFactoryContextInitializer", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.data.redis.RedisUrlSyntaxFailureAnalyzer", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.diagnostics.analyzer.NoSuchBeanDefinitionFailureAnalyzer", "methods": [{"name": "<init>", "parameterTypes": ["org.springframework.beans.factory.BeanFactory"]}]}, {"type": "org.springframework.boot.autoconfigure.integration.IntegrationPropertiesEnvironmentPostProcessor", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.jdbc.DataSourceBeanCreationFailureAnalyzer", "methods": [{"name": "<init>", "parameterTypes": ["org.springframework.core.env.Environment"]}]}, {"type": "org.springframework.boot.autoconfigure.jdbc.HikariDriverConfigurationFailureAnalyzer", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.jooq.NoDslContextBeanFailureAnalyzer", "methods": [{"name": "<init>", "parameterTypes": ["org.springframework.beans.factory.BeanFactory"]}]}, {"type": "org.springframework.boot.autoconfigure.logging.ConditionEvaluationReportLoggingListener", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.r2dbc.ConnectionFactoryBeanCreationFailureAnalyzer", "methods": [{"name": "<init>", "parameterTypes": ["org.springframework.core.env.Environment"]}]}, {"type": "org.springframework.boot.autoconfigure.r2dbc.MissingR2dbcPoolDependencyFailureAnalyzer", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.r2dbc.MultipleConnectionPoolConfigurationsFailureAnalyzer", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.r2dbc.NoConnectionFactoryBeanFailureAnalyzer", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.ssl.BundleContentNotWatchableFailureAnalyzer", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.builder.ParentContextCloserApplicationListener", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.cloud.CloudFoundryVcapEnvironmentPostProcessor", "methods": [{"name": "<init>", "parameterTypes": ["org.springframework.boot.logging.DeferredLogFactory"]}]}, {"type": "org.springframework.boot.context.ConfigurationWarningsApplicationContextInitializer", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.context.ContextIdApplicationContextInitializer", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.context.FileEncodingApplicationListener", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.context.config.AnsiOutputApplicationListener", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.context.config.ConfigDataEnvironmentPostProcessor", "methods": [{"name": "<init>", "parameterTypes": ["org.springframework.boot.logging.DeferredLogFactory", "org.springframework.boot.ConfigurableBootstrapContext"]}]}, {"type": "org.springframework.boot.context.config.ConfigDataNotFoundAction"}, {"type": "org.springframework.boot.context.config.ConfigDataNotFoundFailureAnalyzer", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.context.config.ConfigDataProperties", "fields": [{"name": "this$0"}]}, {"type": "org.springframework.boot.context.config.ConfigTreeConfigDataLoader", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.context.config.ConfigTreeConfigDataLocationResolver", "methods": [{"name": "<init>", "parameterTypes": ["org.springframework.core.io.ResourceLoader"]}]}, {"type": "org.springframework.boot.context.config.DelegatingApplicationContextInitializer", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.context.config.DelegatingApplicationListener", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.context.config.StandardConfigDataLoader", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.context.config.StandardConfigDataLocationResolver", "methods": [{"name": "<init>", "parameterTypes": ["org.springframework.boot.logging.DeferredLogFactory", "org.springframework.boot.context.properties.bind.Binder", "org.springframework.core.io.ResourceLoader"]}]}, {"type": "org.springframework.boot.context.event.EventPublishingRunListener", "methods": [{"name": "<init>", "parameterTypes": ["org.springframework.boot.SpringApplication", "java.lang.String[]"]}]}, {"type": "org.springframework.boot.context.logging.LoggingApplicationListener", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.context.properties.IncompatibleConfigurationFailureAnalyzer", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.context.properties.NotConstructorBoundInjectionFailureAnalyzer", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.context.properties.bind.Name"}, {"type": "org.springframework.boot.diagnostics.FailureAnalyzers", "methods": [{"name": "<init>", "parameterTypes": ["org.springframework.context.ConfigurableApplicationContext"]}]}, {"type": "org.springframework.boot.diagnostics.LoggingFailureAnalysisReporter", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.diagnostics.analyzer.AotInitializerNotFoundFailureAnalyzer", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.diagnostics.analyzer.BeanCurrentlyInCreationFailureAnalyzer", "methods": [{"name": "<init>", "parameterTypes": ["org.springframework.beans.factory.BeanFactory"]}]}, {"type": "org.springframework.boot.diagnostics.analyzer.BeanDefinitionOverrideFailureAnalyzer", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.diagnostics.analyzer.BeanNotOfRequiredTypeFailureAnalyzer", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.diagnostics.analyzer.BindFailureAnalyzer", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.diagnostics.analyzer.BindValidationFailureAnalyzer", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.diagnostics.analyzer.InvalidConfigurationPropertyNameFailureAnalyzer", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.diagnostics.analyzer.InvalidConfigurationPropertyValueFailureAnalyzer", "methods": [{"name": "<init>", "parameterTypes": ["org.springframework.core.env.Environment"]}]}, {"type": "org.springframework.boot.diagnostics.analyzer.MissingParameterNamesFailureAnalyzer", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.diagnostics.analyzer.MutuallyExclusiveConfigurationPropertiesFailureAnalyzer", "methods": [{"name": "<init>", "parameterTypes": ["org.springframework.core.env.Environment"]}]}, {"type": "org.springframework.boot.diagnostics.analyzer.NoSuchMethodFailureAnalyzer", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.diagnostics.analyzer.NoUniqueBeanDefinitionFailureAnalyzer", "methods": [{"name": "<init>", "parameterTypes": ["org.springframework.beans.factory.BeanFactory"]}]}, {"type": "org.springframework.boot.diagnostics.analyzer.PatternParseFailureAnalyzer", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.diagnostics.analyzer.PortInUseFailureAnalyzer", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.diagnostics.analyzer.UnboundConfigurationPropertyFailureAnalyzer", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.diagnostics.analyzer.ValidationExceptionFailureAnalyzer", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.env.EnvironmentPostProcessorApplicationListener", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.env.PropertiesPropertySourceLoader", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.env.RandomValuePropertySourceEnvironmentPostProcessor", "methods": [{"name": "<init>", "parameterTypes": ["org.springframework.boot.logging.DeferredLogFactory"]}]}, {"type": "org.springframework.boot.env.SpringApplicationJsonEnvironmentPostProcessor", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.env.YamlPropertySourceLoader", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.io.Base64ProtocolResolver", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.liquibase.LiquibaseChangelogMissingFailureAnalyzer"}, {"type": "org.springframework.boot.logging.LogLevelEditor"}, {"type": "org.springframework.boot.logging.java.JavaLoggingSystem$Factory", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.logging.java.JavaLoggingSystem.Factory"}, {"type": "org.springframework.boot.logging.log4j2.Log4J2LoggingSystem$Factory", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.logging.log4j2.Log4J2LoggingSystem.Factory"}, {"type": "org.springframework.boot.logging.logback.LogbackLoggingSystem$Factory", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.logging.logback.LogbackLoggingSystem.Factory"}, {"type": "org.springframework.boot.logging.logback.RootLogLevelConfigurator"}, {"type": "org.springframework.boot.reactor.ReactorEnvironmentPostProcessor", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.rsocket.context.RSocketPortInfoApplicationContextInitializer", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.test.context.filter.ExcludeFilterApplicationContextInitializer", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.test.web.SpringBootTestRandomPortEnvironmentPostProcessor", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.web.context.MissingWebServerFactoryBeanFailureAnalyzer", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.web.context.ServerPortInfoApplicationContextInitializer", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.web.embedded.tomcat.ConnectorStartFailureAnalyzer", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.web.reactive.context.ReactiveWebServerApplicationContextFactory", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.web.servlet.context.ServletWebServerApplicationContextFactory", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.core.annotation.Order"}, {"type": "org.springframework.web.context.ConfigurableWebApplicationContext"}, {"type": "org.springframework.web.reactive.DispatcherHandler"}, {"type": "org.springframework.web.servlet.DispatcherServlet"}, {"type": "org.yaml.snakeyaml.Yaml"}, {"type": "reactor.tools.agent.ReactorDebugAgent"}, {"type": {"proxy": ["java.lang.reflect.ParameterizedType", "org.springframework.core.SerializableTypeWrapper$SerializableTypeProxy", "java.io.Serializable"]}}], "resources": [{"glob": "META-INF/services/ch.qos.logback.classic.spi.Configurator"}, {"glob": "META-INF/services/jakarta.el.ExpressionFactory"}, {"glob": "META-INF/services/jakarta.validation.ConstraintValidator"}, {"glob": "META-INF/services/jakarta.validation.spi.ValidationProvider"}, {"glob": "META-INF/services/jakarta.validation.valueextraction.ValueExtractor"}, {"glob": "META-INF/services/java.lang.System$LoggerFinder"}, {"glob": "META-INF/services/java.time.zone.ZoneRulesProvider"}, {"glob": "META-INF/services/java.util.spi.ResourceBundleControlProvider"}, {"glob": "META-INF/services/javax.xml.parsers.SAXParserFactory"}, {"glob": "META-INF/services/javax.xml.transform.TransformerFactory"}, {"glob": "META-INF/services/org.apache.juli.logging.Log"}, {"glob": "META-INF/services/org.slf4j.spi.SLF4JServiceProvider"}, {"glob": "META-INF/spring.factories"}, {"glob": "META-INF/spring.integration.properties"}, {"glob": "META-INF/spring/logback-pattern-rules"}, {"glob": "META-INF/validation.xml"}, {"glob": "application-test.properties"}, {"glob": "application-test.xml"}, {"glob": "application-test.yaml"}, {"glob": "application-test.yml"}, {"glob": "application.properties"}, {"glob": "application.xml"}, {"glob": "application.yaml"}, {"glob": "application.yml"}, {"glob": "banner.txt"}, {"glob": "config/application-test.properties"}, {"glob": "config/application-test.xml"}, {"glob": "config/application-test.yaml"}, {"glob": "config/application-test.yml"}, {"glob": "config/application.properties"}, {"glob": "config/application.xml"}, {"glob": "config/application.yaml"}, {"glob": "config/application.yml"}, {"glob": "jndi.properties"}, {"glob": "logback-test.groovy"}, {"glob": "logback-test.scmo"}, {"glob": "logback-test.xml"}, {"glob": "logback.groovy"}, {"glob": "logback.scmo"}, {"glob": "logback.xml"}, {"glob": "org/apache/catalina/authenticator/LocalStrings.properties"}, {"glob": "org/apache/catalina/authenticator/LocalStrings_zh.properties"}, {"glob": "org/apache/catalina/authenticator/LocalStrings_zh_CN.properties"}, {"glob": "org/apache/catalina/authenticator/LocalStrings_zh_Hans.properties"}, {"glob": "org/apache/catalina/authenticator/LocalStrings_zh_Hans_CN.properties"}, {"glob": "org/apache/catalina/util/LocalStrings.properties"}, {"glob": "org/apache/catalina/util/LocalStrings_zh.properties"}, {"glob": "org/apache/catalina/util/LocalStrings_zh_CN.properties"}, {"glob": "org/apache/catalina/util/LocalStrings_zh_Hans.properties"}, {"glob": "org/apache/catalina/util/LocalStrings_zh_Hans_CN.properties"}, {"glob": "org/apache/catalina/valves/LocalStrings.properties"}, {"glob": "org/apache/catalina/valves/LocalStrings_zh.properties"}, {"glob": "org/apache/catalina/valves/LocalStrings_zh_CN.properties"}, {"glob": "org/apache/catalina/valves/LocalStrings_zh_Hans.properties"}, {"glob": "org/apache/catalina/valves/LocalStrings_zh_Hans_CN.properties"}, {"glob": "org/apache/tomcat/util/http/LocalStrings.properties"}, {"glob": "org/apache/tomcat/util/http/LocalStrings_zh.properties"}, {"glob": "org/apache/tomcat/util/http/LocalStrings_zh_CN.properties"}, {"glob": "org/apache/tomcat/util/http/LocalStrings_zh_Hans.properties"}, {"glob": "org/apache/tomcat/util/http/LocalStrings_zh_Hans_CN.properties"}, {"glob": "spring.properties"}, {"module": "java.xml", "glob": "jdk/xml/internal/jdkcatalog/JDKCatalog.xml"}], "bundles": [{"name": "org.apache.catalina.authenticator.LocalStrings", "locales": ["zh-CN"]}, {"name": "org.apache.catalina.util.LocalStrings", "locales": ["zh-CN"]}, {"name": "org.apache.catalina.valves.LocalStrings", "locales": ["zh-CN"]}, {"name": "org.apache.tomcat.util.http.LocalStrings", "locales": ["zh-CN"]}], "jni": [{"type": "com.xyy.saas.localserver.LocalserverApplication", "methods": [{"name": "main", "parameterTypes": ["java.lang.String[]"]}]}, {"type": "java.lang.Bo<PERSON>an", "methods": [{"name": "getBoolean", "parameterTypes": ["java.lang.String"]}]}, {"type": "java.lang.InternalError", "methods": [{"name": "<init>", "parameterTypes": ["java.lang.String"]}]}, {"type": "sun.instrument.InstrumentationImpl", "methods": [{"name": "<init>", "parameterTypes": ["long", "boolean", "boolean", "boolean"]}, {"name": "loadClassAndCallAgentmain", "parameterTypes": ["java.lang.String", "java.lang.String"]}, {"name": "loadClassAndCallPremain", "parameterTypes": ["java.lang.String", "java.lang.String"]}, {"name": "transform", "parameterTypes": ["java.lang.Module", "java.lang.ClassLoader", "java.lang.String", "java.lang.Class", "java.security.ProtectionDomain", "byte[]", "boolean"]}]}, {"type": "sun.launcher.LauncherHelper", "fields": [{"name": "isStaticMain"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "methods": [{"name": "checkAndLoadMain", "parameterTypes": ["boolean", "int", "java.lang.String"]}, {"name": "getApplicationClass", "parameterTypes": []}, {"name": "makePlatformString", "parameterTypes": ["boolean", "byte[]"]}]}]}