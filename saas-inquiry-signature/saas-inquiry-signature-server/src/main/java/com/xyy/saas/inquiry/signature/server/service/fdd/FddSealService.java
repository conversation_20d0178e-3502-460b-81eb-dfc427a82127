package com.xyy.saas.inquiry.signature.server.service.fdd;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import com.fasc.open.api.v5_1.client.SealClient;
import com.fasc.open.api.v5_1.req.seal.CreatePersonalSealByImageReq;
import com.fasc.open.api.v5_1.req.seal.CreatePersonalSealByTemplateReq;
import com.fasc.open.api.v5_1.req.seal.DeletePersonalSealReq;
import com.fasc.open.api.v5_1.req.seal.GetPersonalSealFreeSignUrlReq;
import com.fasc.open.api.v5_1.req.seal.GetPersonalSealListReq;
import com.fasc.open.api.v5_1.req.seal.GetSeaCertInfoReq;
import com.fasc.open.api.v5_1.req.template.CancelPersonalSealFreeSignReq;
import com.fasc.open.api.v5_1.res.seal.CreatePersonalSealByImageRes;
import com.fasc.open.api.v5_1.res.seal.CreatePersonalSealByTemplateRes;
import com.fasc.open.api.v5_1.res.seal.GetPersonalSealListRes;
import com.fasc.open.api.v5_1.res.seal.GetSeaCertInfoRes;
import com.fasc.open.api.v5_1.res.seal.GetSealFreeSignUrlRes;
import com.xyy.saas.inquiry.signature.server.service.fdd.bean.FddBaseReqDto;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 法大大 签名印章管理service
 *
 * @Author:chenxiaoyi
 * @Date:2024/02/23 18:36
 */
@Component
@Slf4j
public class FddSealService extends FddBaseService {

    private static final Map<Integer, SealClient> sealClientMap = new HashMap<>();

    private SealClient getSealClient(Integer configId) {
        if (sealClientMap.get(configId) == null) {
            synchronized (FddSealService.class) {
                if (sealClientMap.get(configId) == null) {
                    sealClientMap.put(configId, new SealClient(getOpenApiClient(configId)));
                }
            }
        }
        return sealClientMap.get(configId);
    }


    /**
     * 根据图片创建签章
     *
     * @param fddBaseReqDto
     * @return
     */
    public CommonResult<CreatePersonalSealByImageRes> createSealByImage(FddBaseReqDto<CreatePersonalSealByImageReq> fddBaseReqDto) {
        return execute((t) -> getSealClient(fddBaseReqDto.getConfigId()).createPersonalSealByImage(t)
            , fddBaseReqDto, "根据图片创建签章");
    }

    /**
     * 根据图片创建签章
     *
     * @param fddBaseReqDto
     * @return
     */
    public CommonResult<CreatePersonalSealByTemplateRes> createSealByTemplate(FddBaseReqDto<CreatePersonalSealByTemplateReq> fddBaseReqDto) {
        return execute((t) -> getSealClient(fddBaseReqDto.getConfigId()).createPersonalSealByTemplate(t)
            , fddBaseReqDto, "根据模板创建签章");
    }

    /**
     * 获取设置个人签名免验证签链接
     *
     * @param fddBaseReqDto
     * @return
     */
    public CommonResult<GetSealFreeSignUrlRes> getPersonalSealFreeSignUrl(FddBaseReqDto<GetPersonalSealFreeSignUrlReq> fddBaseReqDto) {
        return execute((t) -> getSealClient(fddBaseReqDto.getConfigId()).getPersonalSealFreeSignUrl(t)
            , fddBaseReqDto, "获取设置个人签名免验证签链接");
    }

    /**
     * 查询个人签名列表
     *
     * @param fddBaseReqDto
     * @return
     */
    public CommonResult<GetPersonalSealListRes> getPersonalSealList(FddBaseReqDto<GetPersonalSealListReq> fddBaseReqDto) {
        return execute((t) -> getSealClient(fddBaseReqDto.getConfigId()).getPersonalSealList(t)
            , fddBaseReqDto, "查询个人签名列表");
    }

    /**
     * 删除个人签名
     *
     * @param fddBaseReqDto
     * @return
     */
    public CommonResult<Void> deletePersonalSeal(FddBaseReqDto<DeletePersonalSealReq> fddBaseReqDto) {
        return executeVoid((t) -> getSealClient(fddBaseReqDto.getConfigId()).deletePersonalSeal(t)
            , fddBaseReqDto, "删除个人签名");
    }

    /**
     * 解除签名免验证签
     *
     * @param fddBaseReqDto
     * @return
     */
    public CommonResult<Void> cancelPersonalSealFreeSign(FddBaseReqDto<CancelPersonalSealFreeSignReq> fddBaseReqDto) {
        return executeVoid((t) -> getSealClient(fddBaseReqDto.getConfigId()).cancelPersonalSealFreeSign(t)
            , fddBaseReqDto, "解除签名免验证签");
    }

    /**
     * 获取CA认证
     *
     * @param fddBaseReqDto
     * @return
     */
    public CommonResult<List<GetSeaCertInfoRes>> getSealCertInfo(FddBaseReqDto<GetSeaCertInfoReq> fddBaseReqDto) {
        return execute((t)->getSealClient(fddBaseReqDto.getConfigId()).getSealCertInfo(fddBaseReqDto.getData()),fddBaseReqDto,"查询CA认证");

    }
}
