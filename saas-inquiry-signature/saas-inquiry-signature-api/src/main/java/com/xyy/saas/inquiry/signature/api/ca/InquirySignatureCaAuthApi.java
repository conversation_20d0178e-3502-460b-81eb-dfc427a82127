package com.xyy.saas.inquiry.signature.api.ca;

import com.xyy.saas.inquiry.enums.signature.SignaturePlatformEnum;
import com.xyy.saas.inquiry.signature.api.ca.dto.InquirySignatureCaAuthRespDto;
import com.xyy.saas.inquiry.signature.api.ca.dto.SyncCreateCaDto;
import java.util.List;

/**
 * @Author:chenxia<PERSON>i
 * @Date:2025/02/20 19:33
 */
public interface InquirySignatureCaAuthApi {

    boolean isCaAuthFreeSign(Long userId, SignaturePlatformEnum signaturePlatform);


    InquirySignatureCaAuthRespDto getCaAuthInfo(Long userId, SignaturePlatformEnum signaturePlatform);


    List<InquirySignatureCaAuthRespDto> getCaAuthInfo(List<Long> userIds, SignaturePlatformEnum signaturePlatform);

    /**
     * 创建同步用户签章信息、CA+person
     */
    void createSyncSignatureCaAuth(SyncCreateCaDto syncCreateCaDto);


    /**
     * 重置CA认证状态
     *
     * @param userId
     */
    void resetCaAuth(Long userId);

    /**
     * 获取CA
     * @param userid
     * @return
     */
    String getSealCertInfo(Long userid);
}
