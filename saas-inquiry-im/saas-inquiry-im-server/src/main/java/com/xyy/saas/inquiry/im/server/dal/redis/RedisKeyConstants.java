package com.xyy.saas.inquiry.im.server.dal.redis;

public interface RedisKeyConstants {
    /**
     * 查询创建用户锁
     */
    String IM_USER_LOCK_KEY = "im:user:lockKey:";

    /**
     * 远程审方进房次数
     */
    String REMOTE_AUDIT_ENTER_ROOM_COUNT = "remote:audit:enter:room:count:";

    /**
     * 视频问诊进房次数
     */
    String INQUIRY_ENTER_ROOM_COUNT = "inquiry:enter:room:count:";

    /**
     * 问诊转码任务
     */
    String INQUIRY_TRANSCODING_TASK = "inquiry:transcoding:task:";

}
