package com.xyy.saas.inquiry.im.server.service.trtc.strategy.trtccallback;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.inquiry.im.enums.ErrorCodeConstants.INQUIRY_TRTC_PUSH_STREAM_ERROR;
import static com.xyy.saas.inquiry.im.enums.ErrorCodeConstants.INQUIRY_TRTC_SOURCE_STREAM_PULL_ERROR;
import static com.xyy.saas.inquiry.im.enums.ErrorCodeConstants.INQUIRY_TRTC_START_PUSH_STREAM_ERROR;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.xyy.saas.inquiry.ding.DingService;
import com.xyy.saas.inquiry.ding.DingService.Markdown;
import com.xyy.saas.inquiry.enums.inquiry.InquiryBizTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.StreamStatus;
import com.xyy.saas.inquiry.enums.tencent.TencentTrtcCallBackEventEnum;
import com.xyy.saas.inquiry.hospital.api.doctor.InquiryDoctorApi;
import com.xyy.saas.inquiry.hospital.api.doctor.dto.InquiryDoctorDto;
import com.xyy.saas.inquiry.hospital.api.doctor.video.InquiryDoctorVideoApi;
import com.xyy.saas.inquiry.im.server.controller.app.callback.vo.TencentTrtcCallBackReqVO;
import com.xyy.saas.inquiry.im.server.controller.app.callback.vo.TencentTrtcCallBackReqVO.EventInfo;
import com.xyy.saas.inquiry.im.server.dal.redis.RedisKeyConstants;
import com.xyy.saas.inquiry.im.server.service.tencent.TencentTrtcClient;
import com.xyy.saas.inquiry.patient.api.inquiry.InquiryApi;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import com.xyy.saas.inquiry.util.RedisUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

/**
 * @Author: xucao
 * @Date: 2024/12/09 17:05
 * @Description: 进入房间事件处理策略 进入房间事件处理 1、根据房间号查询问诊单信息，获取患者和医生的user以及问诊开方类型（自动还是真人） 2、患者进入房间： 真人：不处理 自动：拉流转推 3、医生进入房间、启动混流转推（医生进房只有真人场景才存在）
 */
@Component
@Slf4j
public class EnterRoomHandleStrategy extends TencentTrtcCallBackHandleStrategy {

    @DubboReference
    private InquiryApi inquiryApi;

    @DubboReference
    private InquiryDoctorVideoApi inquiryDoctorVideoApi;

    @DubboReference
    private InquiryDoctorApi inquiryDoctorApi;

    @Resource
    private TencentTrtcClient tencentTrtcClient;

    @Resource
    private DingService dingService;

    /**
     * 策略执行器
     *
     * @param callBackReqVO 回调参数
     */
    @Override
    public Boolean execute(TencentTrtcCallBackReqVO callBackReqVO) {
        log.info("进房事件处理:{}", JSON.toJSONString(callBackReqVO));
        // 校验参数
        callBackReqVO.checkEvent();
        // 事件参数
        EventInfo eventInfo = callBackReqVO.getEventInfo();
        // 1、根据房间号查询问诊单信息，获取患者和医生的user以及问诊开方类型（自动还是真人）
        InquiryRecordDto inquiryRecordDto = inquiryApi.getInquiryRecord(eventInfo.getRoomId());
        // 2、处理商家进房事件
        if (ObjectUtil.equals(inquiryRecordDto.getInquiryBizType(), InquiryBizTypeEnum.DRUGSTORE_INQUIRY.getCode()) && ObjectUtil.equals(inquiryRecordDto.getCreator(), eventInfo.getUserId())) {
            handlePatientEnterRoom(inquiryRecordDto);
        }
        // 3、处理混流准备事件
        handleEnterRoom(inquiryRecordDto,eventInfo);
       return Boolean.TRUE;
    }

    /**
     * 处理远程审方进房事件
     *
     * @param inquiryRecordDto 问诊单信息
     * @param eventInfo        回调参数
     */
    private void handleEnterRoom(InquiryRecordDto inquiryRecordDto, EventInfo eventInfo) {
        // 进房记录key
        String key = ObjectUtil.equals(inquiryRecordDto.getInquiryBizType(), InquiryBizTypeEnum.REMOTE_INQUIRY.getCode()) ? RedisKeyConstants.REMOTE_AUDIT_ENTER_ROOM_COUNT + inquiryRecordDto.getPref() : RedisKeyConstants.INQUIRY_ENTER_ROOM_COUNT + inquiryRecordDto.getPref();
        // 查询redis  key  获取已进房人员
        Object entedUser = RedisUtils.get(key);
        if(ObjectUtil.isEmpty(entedUser)){
            // 写入redis  有效时长1小时
            RedisUtils.set(key,eventInfo.getUserId(),60*60);
            return;
        }
        if(ObjectUtil.equals(entedUser.toString(),eventInfo.getUserId())){
            return;
        }
        // 进过一次房，第二次进房则直接开启混流
        log.info("问诊单:{} ,开启混流...",inquiryRecordDto.getPref());
        // 获取接诊医生或者药师userid
        String userId = getReceptionUserId(inquiryRecordDto,entedUser,eventInfo);
        // 启动混流
        super.startPublishStreamCdn(inquiryRecordDto,userId);
        // 删除redis
        RedisUtils.del(key);
    }

    /**
     * 获取接诊医生或者药师userid
     * @param inquiryRecordDto 问诊单信息
     * @param entedUser 已进房人员
     * @param eventInfo 当前进房人员
     * @return
     */
    private String getReceptionUserId(InquiryRecordDto inquiryRecordDto, Object entedUser, EventInfo eventInfo) {
        // 判断已进房人员是否为发起商家
        if(StringUtils.equals(inquiryRecordDto.getCreator(),entedUser.toString())){
            return eventInfo.getUserId();
        }
        return entedUser.toString();
    }


    /**
     * 处理患者进入房间
     *
     * @param inquiryRecordDto 问诊单信息
     * @return 处理结果
     */
    private Boolean handlePatientEnterRoom(InquiryRecordDto inquiryRecordDto) {
        log.info("商家端进房处理开始...");
        // 非自动开方场景（真人接诊）无需处理
        if (!inquiryRecordDto.isAutoInquiry()) {
            log.info("真人问诊商家端无需处理");
            return Boolean.TRUE;
        }
        // 是否已推流，不允许二次推流
        if (inquiryRecordDto.getStreamStatus() >= StreamStatus.PUSH_STREAM.getCode()) {
            throw exception(INQUIRY_TRTC_PUSH_STREAM_ERROR);
        }
        log.info("录屏编码:{}",inquiryRecordDto.getDoctorVideoPref());
        // 查询医生信息
        InquiryDoctorDto doctorDto = inquiryDoctorApi.getInquiryDoctorByDoctorPref(inquiryRecordDto.getDoctorPref());
        // 获取录屏地址
        String streamUrl = inquiryDoctorVideoApi.getVideoUrlByPref(inquiryRecordDto.getDoctorVideoPref());
        log.info("获取到的录屏url:{}",streamUrl);
        if (StringUtils.isBlank(streamUrl)) {
            log.error("录屏地址为空,inquiryPref:{}", inquiryRecordDto.getPref());
            throw exception(INQUIRY_TRTC_SOURCE_STREAM_PULL_ERROR);
        }
        // 给拉流转推机器人指定userId 因为不能重复，所以拼上问诊单号
        String jobUserId = inquiryRecordDto.getPref() + "@" + doctorDto.getUserId();
        Boolean result = tencentTrtcClient.startInputOnlineStream(streamUrl, inquiryRecordDto.getPref(), jobUserId);
        if (!result) {
            dingService.send(Markdown
                .title("AI视频开方拉流转推失败")
                .add("问诊单号", inquiryRecordDto.getPref())
                .add("录屏编码", inquiryRecordDto.getDoctorVideoPref())
            );
            throw exception(INQUIRY_TRTC_START_PUSH_STREAM_ERROR);
        }
        return inquiryApi.updateInquiry(InquiryRecordDto.builder().streamStatus(StreamStatus.PUSH_STREAM.getCode()).id(inquiryRecordDto.getId()).build());
    }

    /**
     * 获取策略对应的事件
     *
     * @return 事件
     */
    @Override
    public TencentTrtcCallBackEventEnum getEvent() {
        return TencentTrtcCallBackEventEnum.EVENT_TYPE_ENTER_ROOM;
    }
}
